# Development dependencies for Agent Framework
-r requirements.txt

# Testing
pytest>=8.4.1
pytest-asyncio>=1.1.0
pytest-cov>=6.2.1
pytest-mock>=3.10.0
pytest-xdist>=3.0.0

# Code quality
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0
pre-commit>=3.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
myst-parser>=1.0.0

# Development tools
ipython>=8.0.0
jupyter>=1.0.0
twine>=4.0.0
build>=0.10.0

# Performance profiling
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Security scanning
bandit>=1.7.0
safety>=2.3.0

# Test infrastructure enhancements
factory-boy>=3.3.0
pytest-benchmark>=4.0.0
testcontainers>=3.7.0
hypothesis>=6.88.0

# Dependency vulnerability scanning
pip-audit>=2.6.0
