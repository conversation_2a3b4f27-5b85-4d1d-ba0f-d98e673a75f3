# Core dependencies for Agent Framework
autogen-agentchat>=0.7.1
autogen-ext[diskcache,docker,mcp,openai]>=0.7.1
colorama>=0.4.6
docker>=7.1.0
mcp-server-fetch>=2025.4.7
patch>=1.16
psutil>=5.9.0
pydantic>=2.0.0
pyyaml>=6.0.0
rich>=13.0.0

# Production monitoring and observability
prometheus-client>=0.17.0
opentelemetry-api>=1.20.0
opentelemetry-sdk>=1.20.0
opentelemetry-instrumentation-asyncio>=0.41b0
opentelemetry-exporter-jaeger>=1.20.0

# Database connection pooling
asyncpg>=0.29.0
aiosqlite>=0.19.0

# Security enhancements
cryptography>=41.0.0
keyring>=24.0.0

# Circuit breaker and resilience
circuit-breaker>=1.4.0
tenacity>=8.2.0

# Configuration management
python-dotenv>=1.0.0
pydantic-settings>=2.0.0

# Rate limiting and throttling
slowapi>=0.1.9
limits>=3.6.0

# Enhanced HTTP client with connection pooling
httpx>=0.28.1

# Updated core dependencies
anyio>=4.10.0
certifi>=2025.8.3
