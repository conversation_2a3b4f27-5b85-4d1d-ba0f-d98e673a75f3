# Agent Framework Development Makefile

.PHONY: help install install-dev test test-unit test-integration test-coverage lint format type-check clean build docs serve-docs release

# Default target
help:
	@echo "Agent Framework Development Commands"
	@echo "===================================="
	@echo ""
	@echo "Setup:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo ""
	@echo "Testing:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-coverage    Run tests with coverage report"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint             Run all linting checks"
	@echo "  format           Format code with black and isort"
	@echo "  type-check       Run type checking with mypy"
	@echo ""
	@echo "Documentation:"
	@echo "  docs             Build documentation"
	@echo "  serve-docs       Serve documentation locally"
	@echo ""
	@echo "Build & Release:"
	@echo "  clean            Clean build artifacts"
	@echo "  build            Build package"
	@echo "  release          Build and upload to PyPI"

# Installation
install:
	uv sync --no-dev

install-dev:
	uv sync --dev
	pre-commit install

# Testing
test:
	pytest

test-unit:
	pytest tests/ -m "not integration"

test-integration:
	pytest tests/ -m "integration"

test-coverage:
	pytest --cov=src/agent_framework --cov-report=html --cov-report=term-missing

# Code Quality
lint: type-check
	flake8 src/ tests/
	black --check src/ tests/
	isort --check-only src/ tests/

format:
	black src/ tests/
	isort src/ tests/

type-check:
	mypy src/agent_framework

# Documentation
docs:
	cd docs && make html

serve-docs:
	cd docs && make html && python -m http.server 8000 -d _build/html

# Build & Release
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

release: build
	python -m twine upload dist/*

# Development shortcuts
dev-setup: install-dev
	@echo "Development environment setup complete!"
	@echo "Run 'make test' to verify everything works."

check: lint test
	@echo "All checks passed!"

# CI/CD helpers
ci-test:
	pytest --cov=src/agent_framework --cov-report=xml --cov-fail-under=80

ci-lint:
	flake8 src/ tests/ --format=github
	black --check src/ tests/
	isort --check-only src/ tests/
	mypy src/agent_framework
