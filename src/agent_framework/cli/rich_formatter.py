"""
Rich Terminal Formatter for enhanced CLI experience.

Provides advanced terminal formatting capabilities using the rich library,
including colored output, progress bars, tables, panels, and status indicators.
"""

import sys
import time
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Union, Iterator
from datetime import datetime, timedelta

try:
    from rich.console import Console
    from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich.syntax import Syntax
    from rich.prompt import Prompt, Confirm
    from rich.status import Status
    from rich.live import Live
    from rich.layout import Layout
    from rich.align import Align
    from rich.rule import Rule
    from rich import box
    _RICH_AVAILABLE = True
except ImportError:
    # Fallback when rich is not available
    _RICH_AVAILABLE = False
    Console = None  # type: ignore[assignment,misc]


class RichTerminalFormatter:
    """Enhanced terminal formatter using the rich library."""
    
    def __init__(self, force_terminal: Optional[bool] = None, width: Optional[int] = None):
        """Initialize the rich terminal formatter."""
        if not _RICH_AVAILABLE:
            raise ImportError("Rich library is not available. Please install it with: pip install rich")
        
        self.console = Console(
            force_terminal=force_terminal,
            width=width,
            stderr=False
        )
        self.error_console = Console(
            force_terminal=force_terminal,
            width=width,
            stderr=True
        )
        
        # Color scheme
        self.colors = {
            'success': 'green',
            'error': 'red',
            'warning': 'yellow',
            'info': 'blue',
            'debug': 'dim white',
            'highlight': 'bright_cyan',
            'accent': 'magenta',
            'muted': 'dim'
        }
        
        # Icons
        self.icons = {
            'success': '✓',
            'error': '✗',
            'warning': '⚠',
            'info': 'ℹ',
            'debug': '🐛',
            'progress': '⚡',
            'step': '→',
            'bullet': '•'
        }
    
    def print_success(self, message: str, prefix: bool = True) -> None:
        """Print success message in green with checkmark."""
        icon = f"{self.icons['success']} " if prefix else ""
        self.console.print(f"[{self.colors['success']}]{icon}{message}[/]")
    
    def print_error(self, message: str, prefix: bool = True) -> None:
        """Print error message in red with X mark."""
        icon = f"{self.icons['error']} " if prefix else ""
        self.error_console.print(f"[{self.colors['error']}]{icon}{message}[/]")
    
    def print_warning(self, message: str, prefix: bool = True) -> None:
        """Print warning message in yellow with warning icon."""
        icon = f"{self.icons['warning']} " if prefix else ""
        self.console.print(f"[{self.colors['warning']}]{icon}{message}[/]")
    
    def print_info(self, message: str, prefix: bool = True) -> None:
        """Print info message in blue with info icon."""
        icon = f"{self.icons['info']} " if prefix else ""
        self.console.print(f"[{self.colors['info']}]{icon}{message}[/]")
    
    def print_debug(self, message: str, prefix: bool = True) -> None:
        """Print debug message in dim white with debug icon."""
        icon = f"{self.icons['debug']} " if prefix else ""
        self.console.print(f"[{self.colors['debug']}]{icon}{message}[/]")
    
    def print_highlight(self, message: str) -> None:
        """Print highlighted message."""
        self.console.print(f"[{self.colors['highlight']}]{message}[/]")
    
    def print_header(self, title: str, subtitle: Optional[str] = None) -> None:
        """Print a formatted header with title and optional subtitle."""
        if subtitle:
            content = f"[bold]{title}[/]\n[{self.colors['muted']}]{subtitle}[/]"
        else:
            content = f"[bold]{title}[/]"
        
        panel = Panel(
            content,
            box=box.ROUNDED,
            border_style=self.colors['accent'],
            padding=(1, 2)
        )
        self.console.print(panel)
    
    def print_section(self, title: str) -> None:
        """Print a section divider."""
        rule = Rule(f"[bold]{title}[/]", style=self.colors['accent'])
        self.console.print(rule)
    
    def print_step(self, step_num: int, total_steps: int, description: str) -> None:
        """Print step progress indicator."""
        progress_text = f"[{self.colors['muted']}][{step_num}/{total_steps}][/]"
        step_text = f"[{self.colors['info']}]{self.icons['step']} {description}[/]"
        self.console.print(f"{progress_text} {step_text}")
    
    def create_table(self, title: str = None, show_header: bool = True, 
                    show_lines: bool = False) -> Table:
        """Create a rich table with consistent styling."""
        table = Table(
            title=title,
            show_header=show_header,
            show_lines=show_lines,
            box=box.SIMPLE_HEAD if show_header else box.SIMPLE,
            title_style=f"bold {self.colors['accent']}"
        )
        return table
    
    def print_table(self, headers: List[str], rows: List[List[str]], 
                   title: str = None) -> None:
        """Print a formatted table."""
        table = self.create_table(title=title)
        
        # Add headers
        for header in headers:
            table.add_column(header, style=self.colors['info'], header_style="bold")
        
        # Add rows
        for row in rows:
            table.add_row(*[str(cell) for cell in row])
        
        self.console.print(table)
    
    def print_code(self, code: str, language: str = "python", 
                  title: str = None, line_numbers: bool = True) -> None:
        """Print syntax-highlighted code block."""
        syntax = Syntax(
            code,
            language,
            theme="monokai",
            line_numbers=line_numbers,
            background_color="default"
        )
        
        if title:
            panel = Panel(syntax, title=title, border_style=self.colors['accent'])
            self.console.print(panel)
        else:
            self.console.print(syntax)
    
    def print_json(self, data: Dict[str, Any], title: str = None) -> None:
        """Print formatted JSON data."""
        import json
        json_str = json.dumps(data, indent=2, ensure_ascii=False)
        self.print_code(json_str, "json", title)
    
    def print_panel(self, content: str, title: str = None, 
                   style: str = None) -> None:
        """Print content in a panel."""
        panel = Panel(
            content,
            title=title,
            border_style=style or self.colors['accent'],
            box=box.ROUNDED,
            padding=(1, 2)
        )
        self.console.print(panel)
    
    def print_list(self, items: List[str], bullet_style: str = None) -> None:
        """Print a bulleted list."""
        bullet = self.icons['bullet']
        style = bullet_style or self.colors['info']
        
        for item in items:
            self.console.print(f"[{style}]{bullet}[/] {item}")
    
    def prompt(self, message: str, default: str = None, 
              password: bool = False) -> str:
        """Interactive prompt with rich formatting."""
        return Prompt.ask(
            f"[{self.colors['info']}]{message}[/]",
            default=default,
            password=password,
            console=self.console
        )
    
    def confirm(self, message: str, default: bool = True) -> bool:
        """Interactive confirmation prompt."""
        return Confirm.ask(
            f"[{self.colors['warning']}]{message}[/]",
            default=default,
            console=self.console
        )
    
    @contextmanager
    def status(self, message: str, spinner: str = "dots") -> Iterator[Status]:
        """Context manager for status indicator with spinner."""
        with self.console.status(
            f"[{self.colors['info']}]{message}[/]",
            spinner=spinner
        ) as status:
            yield status
    
    def clear(self) -> None:
        """Clear the terminal screen."""
        self.console.clear()
    
    def print_separator(self, char: str = "─", style: str = None) -> None:
        """Print a separator line."""
        rule = Rule(characters=char, style=style or self.colors['muted'])
        self.console.print(rule)
    
    def print_centered(self, text: str, style: str = None) -> None:
        """Print centered text."""
        centered = Align.center(Text(text, style=style))
        self.console.print(centered)
    
    def get_width(self) -> int:
        """Get console width."""
        return self.console.size.width
    
    def get_height(self) -> int:
        """Get console height."""
        return self.console.size.height


class WorkflowProgressTracker:
    """Advanced progress tracking for multi-step workflows."""
    
    def __init__(self, formatter: RichTerminalFormatter):
        """Initialize workflow progress tracker."""
        self.formatter = formatter
        self.console = formatter.console
        self.steps: List[Dict[str, Any]] = []
        self.current_step = 0
        self.start_time = None
        self.step_start_time = None
    
    def add_step(self, name: str, description: str = None, 
                estimated_duration: float = None) -> None:
        """Add a step to the workflow."""
        self.steps.append({
            'name': name,
            'description': description or name,
            'estimated_duration': estimated_duration,
            'status': 'pending',
            'start_time': None,
            'end_time': None,
            'error': None
        })
    
    def start_workflow(self, title: str = "Workflow Progress") -> None:
        """Start the workflow tracking."""
        self.start_time = time.time()
        self.formatter.print_header(title, f"Total steps: {len(self.steps)}")
        self._print_workflow_overview()
    
    def start_step(self, step_index: int = None) -> None:
        """Start a specific step."""
        if step_index is None:
            step_index = self.current_step
        
        if step_index >= len(self.steps):
            return
        
        self.current_step = step_index
        step = self.steps[step_index]
        step['status'] = 'running'
        step['start_time'] = time.time()
        self.step_start_time = step['start_time']
        
        self.formatter.print_step(
            step_index + 1,
            len(self.steps),
            f"Starting: {step['description']}"
        )
    
    def complete_step(self, step_index: int = None, message: str = None) -> None:
        """Complete a specific step."""
        if step_index is None:
            step_index = self.current_step
        
        if step_index >= len(self.steps):
            return
        
        step = self.steps[step_index]
        step['status'] = 'completed'
        step['end_time'] = time.time()
        
        duration = step['end_time'] - step['start_time'] if step['start_time'] else 0
        duration_str = f"({duration:.1f}s)" if duration > 0 else ""
        
        success_msg = message or f"Completed: {step['description']} {duration_str}"
        self.formatter.print_success(success_msg)
        
        self.current_step += 1
    
    def fail_step(self, step_index: int = None, error: str = None) -> None:
        """Mark a step as failed."""
        if step_index is None:
            step_index = self.current_step
        
        if step_index >= len(self.steps):
            return
        
        step = self.steps[step_index]
        step['status'] = 'failed'
        step['end_time'] = time.time()
        step['error'] = error
        
        error_msg = f"Failed: {step['description']}"
        if error:
            error_msg += f" - {error}"
        
        self.formatter.print_error(error_msg)
    
    def _print_workflow_overview(self) -> None:
        """Print workflow overview table."""
        table = self.formatter.create_table("Workflow Steps", show_lines=True)
        table.add_column("Step", style="bold")
        table.add_column("Description")
        table.add_column("Status", justify="center")
        table.add_column("Duration", justify="right")
        
        for i, step in enumerate(self.steps):
            status_style = {
                'pending': 'dim',
                'running': 'yellow',
                'completed': 'green',
                'failed': 'red'
            }.get(step['status'], 'dim')
            
            status_icon = {
                'pending': '⏳',
                'running': '🔄',
                'completed': '✅',
                'failed': '❌'
            }.get(step['status'], '⏳')
            
            duration = ""
            if step['start_time'] and step['end_time']:
                duration = f"{step['end_time'] - step['start_time']:.1f}s"
            elif step['estimated_duration']:
                duration = f"~{step['estimated_duration']:.1f}s"
            
            table.add_row(
                f"{i + 1}",
                step['description'],
                f"[{status_style}]{status_icon} {step['status'].title()}[/]",
                duration
            )
        
        self.console.print(table)
    
    def print_summary(self) -> None:
        """Print workflow completion summary."""
        if not self.start_time:
            return
        
        total_duration = time.time() - self.start_time
        completed_steps = sum(1 for step in self.steps if step['status'] == 'completed')
        failed_steps = sum(1 for step in self.steps if step['status'] == 'failed')
        
        summary_data = {
            "Total Steps": len(self.steps),
            "Completed": completed_steps,
            "Failed": failed_steps,
            "Total Duration": f"{total_duration:.1f}s"
        }
        
        # Create summary panel
        summary_text = "\n".join([
            f"[bold]{key}:[/] [{self.formatter.colors['info']}]{value}[/]"
            for key, value in summary_data.items()
        ])
        
        if failed_steps == 0:
            title = f"[{self.formatter.colors['success']}]Workflow Completed Successfully[/]"
            border_style = self.formatter.colors['success']
        else:
            title = f"[{self.formatter.colors['error']}]Workflow Completed with Errors[/]"
            border_style = self.formatter.colors['error']
        
        self.formatter.print_panel(summary_text, title, border_style)
