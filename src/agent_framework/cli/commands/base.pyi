"""
Base command class for CLI commands.
"""

import argparse
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from ..utils import CLIUtils

class BaseCommand(ABC):
    """
    Base class for CLI commands.
    
    Provides common functionality for all CLI commands including
    argument parsing, output formatting, and plugin integration.
    """
    
    def __init__(self) -> None: ...
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Command name."""
        ...
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Command description."""
        ...
    
    @abstractmethod
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        ...
    
    @abstractmethod
    async def execute(self, 
                     args: argparse.Namespace,
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the command."""
        ...
    
    def format_output(self, data: Any, format_type: str = "text", pretty: bool = True) -> str:
        """Format command output."""
        ...
    
    async def handle_plugin_request(self,
                                   orchestrator: AgentOrchestrator,
                                   plugin_name: str,
                                   method_name: str,
                                   parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle a plugin request."""
        ...
    
    async def list_plugins(self, orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """List available plugins."""
        ...
    
    def validate_file_path(self, file_path: str) -> bool:
        """Validate file path."""
        ...
    
    def read_file_content(self, file_path: str) -> str:
        """Read file content safely."""
        ...
    
    def write_file_content(self, file_path: str, content: str) -> bool:
        """Write file content safely."""
        ...
    
    # Properties
    @property
    def utils(self) -> CLIUtils: ...
