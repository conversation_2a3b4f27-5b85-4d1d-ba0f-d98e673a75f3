"""
Debug command for the CLI.

Provides debugging assistance including error detection, traceback analysis,
and debugging suggestions.
"""

import argparse
from typing import Any, Dict

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from ...core.agent_orchestrator import AdvancedAgentOrchestrator, AgentCapabilities
from .base import (
    AsyncCommandBase, FileInputMixin, OutputFormatMixin,
    PluginCommandMixin, ValidationMixin
)


class DebugCommand(AsyncCommandBase, OutputFormatMixin,
                  PluginCommandMixin, ValidationMixin):
    """Command for debugging assistance and error analysis."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "debug"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Debug code and analyze errors"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # Debug mode selection
        debug_group = parser.add_mutually_exclusive_group(required=True)
        debug_group.add_argument(
            '--traceback', '-t',
            type=str,
            help='Analyze error traceback from file or string'
        )
        debug_group.add_argument(
            '--check-errors', '-e',
            action='store_true',
            help='Check code for potential errors'
        )
        debug_group.add_argument(
            '--syntax-check', '-s',
            action='store_true',
            help='Check code syntax'
        )
        debug_group.add_argument(
            '--auto-fix', '-a',
            action='store_true',
            help='Automatically attempt to fix detected issues'
        )

        # Enhancement options for auto-fix
        fix_group = parser.add_argument_group('Auto-Fix Options')
        fix_group.add_argument(
            '--max-fix-iterations',
            type=int,
            default=3,
            help='Maximum iterations for automatic fixing (default: 3)'
        )
        fix_group.add_argument(
            '--enable-evaluation',
            action='store_true',
            help='Run evaluation cycles after fixes'
        )
        fix_group.add_argument(
            '--fix-output-file',
            type=str,
            help='Output file for fixed code'
        )
        debug_group.add_argument(
            '--code-smells', '-c',
            action='store_true',
            help='Detect code smells and anti-patterns'
        )

        # File input arguments (for code analysis)
        input_group = parser.add_argument_group('Input Options')
        input_group.add_argument(
            '--file', '-f',
            type=str,
            help='Path to Python file to analyze'
        )
        input_group.add_argument(
            '--code',
            type=str,
            help='Code string to analyze'
        )
        input_group.add_argument(
            '--stdin',
            action='store_true',
            help='Read from standard input'
        )
        
        # Debug options
        options_group = parser.add_argument_group('Debug Options')
        options_group.add_argument(
            '--severity',
            choices=['low', 'medium', 'high'],
            default='medium',
            help='Minimum severity level for issues (default: medium)'
        )
        options_group.add_argument(
            '--suggest-fixes',
            action='store_true',
            help='Suggest fixes for detected issues'
        )
        options_group.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed analysis'
        )
        
        # Plugin arguments
        self.add_plugin_arguments(parser)

        # Output arguments
        self.add_output_arguments(parser)

    def get_code_input(self, args: argparse.Namespace) -> str:
        """Get code input from arguments."""
        if args.file:
            from pathlib import Path
            file_path = Path(args.file)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {args.file}")
            return file_path.read_text(encoding='utf-8')
        elif args.code:
            return args.code
        elif args.stdin:
            import sys
            return sys.stdin.read()
        else:
            raise ValueError("No code input provided")
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Debug Python code and analyze errors for quick resolution.

Debug Modes:
  --traceback       Analyze error traceback and suggest fixes
  --check-errors    Check code for potential runtime errors
  --syntax-check    Check code syntax and suggest corrections
  --code-smells     Detect code smells and anti-patterns
  --auto-fix      - Automatically attempt to fix detected issues

Auto-Fix Options:
  --max-fix-iterations  - Maximum iterations for automatic fixing (default: 3)
  --enable-evaluation   - Run evaluation cycles after fixes
  --fix-output-file     - Output file for fixed code

Examples:
  agent-framework debug --traceback error.txt
  agent-framework debug --check-errors --file buggy_code.py
  agent-framework debug --syntax-check --code "def hello("
  agent-framework debug --code-smells --file legacy_code.py --severity low
  agent-framework debug --auto-fix --file buggy_code.py --fix-output-file fixed.py
  agent-framework debug --auto-fix --code "def broken(): return x" --enable-evaluation
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the debug command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Execute specific debug operation
            if args.traceback:
                result = await self._analyze_traceback(args, orchestrator)
            elif args.check_errors:
                result = await self._check_errors(args, orchestrator)
            elif args.syntax_check:
                result = await self._check_syntax(args, orchestrator)
            elif args.code_smells:
                result = await self._detect_code_smells(args, orchestrator)
            elif args.auto_fix:
                result = await self._auto_fix_issues(args, orchestrator)
            else:
                return {
                    "success": False,
                    "error": "No debug mode specified"
                }
            
            # Format and output results
            formatted_output = self._format_debug_results(result, args)
            self.write_output(formatted_output, args.output)
            
            return {
                "success": True,
                "message": "Debug analysis completed",
                "result": result
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _analyze_traceback(self, args: argparse.Namespace,
                                orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Analyze error traceback."""
        # Get traceback content
        traceback_content = ""
        if args.traceback:
            from pathlib import Path
            traceback_path = Path(args.traceback)
            if traceback_path.exists():
                traceback_content = traceback_path.read_text(encoding='utf-8')
            else:
                # Treat as direct traceback string
                traceback_content = args.traceback
        
        if not traceback_content:
            return {"success": False, "error": "No traceback content provided"}
        
        # Get related code if provided
        code = ""
        if args.file or args.code or args.stdin:
            try:
                code = self.get_code_input(args)
            except:
                pass  # Code is optional for traceback analysis
        
        # Try to use enhanced capabilities if available
        if hasattr(orchestrator, 'enhanced_debugger'):
            enhanced_orchestrator = orchestrator
        else:
            # Create advanced orchestrator for better debugging
            capabilities = AgentCapabilities(
                enable_automatic_bug_fixing=args.suggest_fixes,
                enable_automatic_evaluation=True
            )
            enhanced_orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

        if hasattr(enhanced_orchestrator, 'enhanced_debugger'):
            # Use enhanced debugging
            from ...core.debugger import DebugContext

            # Parse traceback to create debug context
            debug_context = await enhanced_orchestrator.enhanced_debugger.parse_traceback(
                traceback_content, code
            )

            # Perform root cause analysis
            root_cause = await enhanced_orchestrator.enhanced_debugger.analyze_root_cause(
                debug_context
            )

            # Generate suggestions
            suggestions = await enhanced_orchestrator.enhanced_debugger.generate_debug_suggestions(
                debug_context, root_cause
            )

            result = {
                'success': True,
                'result': {
                    'debug_context': {
                        'error_type': debug_context.error_type,
                        'error_message': debug_context.error_message,
                        'file_path': debug_context.file_path,
                        'line_number': debug_context.line_number
                    },
                    'root_cause': {
                        'primary_cause': root_cause.primary_cause,
                        'contributing_factors': root_cause.contributing_factors,
                        'confidence_score': root_cause.confidence_score,
                        'evidence': root_cause.evidence
                    },
                    'suggestions': [
                        {
                            'type': s.suggestion_type,
                            'description': s.description,
                            'code_example': s.code_example,
                            'confidence': s.confidence,
                            'priority': s.priority
                        } for s in suggestions
                    ]
                }
            }
        else:
            # Fallback to plugin system
            request_params = {"traceback": traceback_content}
            if code:
                request_params["code"] = code

            result = await self.handle_plugin_request(
                orchestrator,
                'error_detection',
                'debug_traceback',
                request_params
            )
        
        # Add fix suggestions if requested
        if args.suggest_fixes and result.get('success'):
            traceback_data = result.get('result', {})
            error_type = traceback_data.get('error_type', '')
            error_message = traceback_data.get('error_message', '')
            
            if error_type or error_message:
                fix_result = await self.handle_plugin_request(
                    orchestrator,
                    'error_detection',
                    'suggest_fixes',
                    {
                        'code': code,
                        'error_message': error_message,
                        'error_type': error_type
                    }
                )
                
                if fix_result.get('success'):
                    result['fixes'] = fix_result.get('result', {})
        
        return result
    
    async def _check_errors(self, args: argparse.Namespace,
                           orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Check code for potential runtime errors."""
        # Get code input
        code = self.get_code_input(args)
        
        # Check for potential errors
        result = await self.handle_plugin_request(
            orchestrator,
            'error_detection',
            'detect_runtime_errors',
            {'code': code}
        )
        
        return result
    
    async def _check_syntax(self, args: argparse.Namespace,
                           orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Check code syntax."""
        # Get code input
        code = self.get_code_input(args)
        
        # Check syntax
        result = await self.handle_plugin_request(
            orchestrator,
            'error_detection',
            'check_syntax',
            {'code': code}
        )
        
        return result
    
    async def _detect_code_smells(self, args: argparse.Namespace,
                                 orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Detect code smells and anti-patterns."""
        # Get code input
        code = self.get_code_input(args)
        
        # Detect code smells
        result = await self.handle_plugin_request(
            orchestrator,
            'error_detection',
            'detect_code_smells',
            {
                'code': code,
                'severity_level': args.severity
            }
        )
        
        return result
    
    def _format_debug_results(self, result: Dict[str, Any], 
                             args: argparse.Namespace) -> str:
        """Format debug results for output."""
        if args.format == 'json':
            return self.format_output(result, 'json', args.pretty)
        elif args.format == 'yaml':
            return self.format_output(result, 'yaml', args.pretty)
        else:
            return self._format_as_text(result, args)
    
    def _format_as_text(self, result: Dict[str, Any], 
                       args: argparse.Namespace) -> str:
        """Format results as human-readable text."""
        lines = []
        
        if not result.get('success'):
            lines.append(f"Debug analysis failed: {result.get('error', 'Unknown error')}")
            return "\n".join(lines)
        
        data = result.get('result', {})
        
        # Format based on debug mode
        if args.traceback:
            lines.append("Traceback Analysis")
            lines.append("=" * 50)
            
            lines.append(f"\nExplanation: {data.get('explanation', 'No explanation available')}")
            lines.append(f"Root Cause: {data.get('root_cause', 'Unknown')}")
            
            suggested_fixes = data.get('suggested_fixes', [])
            if suggested_fixes:
                lines.append("\nSuggested Fixes:")
                for fix in suggested_fixes:
                    lines.append(f"  • {fix}")
            
            # Add additional fixes if available
            if 'fixes' in result:
                fix_data = result['fixes']
                additional_fixes = fix_data.get('fixes', [])
                if additional_fixes:
                    lines.append(f"\nAdditional Suggestions (confidence: {fix_data.get('confidence', 0):.1f}):")
                    for fix in additional_fixes:
                        lines.append(f"  • {fix}")
        
        elif args.check_errors:
            lines.append("Runtime Error Analysis")
            lines.append("=" * 50)
            
            potential_errors = data.get('potential_errors', [])
            risk_level = data.get('risk_level', 'unknown')
            
            lines.append(f"\nRisk Level: {risk_level.upper()}")
            lines.append(f"Potential Errors Found: {len(potential_errors)}")
            
            if potential_errors:
                lines.append("\nPotential Issues:")
                for error in potential_errors:
                    severity = error.get('severity', 'unknown')
                    message = error.get('message', 'No description')
                    line_num = error.get('line', 'unknown')
                    lines.append(f"  • [{severity.upper()}] Line {line_num}: {message}")
            
            recommendations = data.get('recommendations', [])
            if recommendations:
                lines.append("\nRecommendations:")
                for rec in recommendations:
                    lines.append(f"  • {rec}")
        
        elif args.syntax_check:
            lines.append("Syntax Check Results")
            lines.append("=" * 50)
            
            is_valid = data.get('is_valid', False)
            lines.append(f"\nSyntax Valid: {'Yes' if is_valid else 'No'}")
            
            errors = data.get('errors', [])
            if errors:
                lines.append("\nSyntax Errors:")
                for error in errors:
                    line_num = error.get('line', 'unknown')
                    message = error.get('message', 'No description')
                    lines.append(f"  • Line {line_num}: {message}")
            
            suggestions = data.get('suggestions', [])
            if suggestions:
                lines.append("\nSuggestions:")
                for suggestion in suggestions:
                    lines.append(f"  • {suggestion}")
        
        elif args.code_smells:
            lines.append("Code Smell Analysis")
            lines.append("=" * 50)
            
            smells = data.get('smells', [])
            severity_counts = data.get('severity_counts', {})
            
            lines.append(f"\nTotal Issues: {len(smells)}")
            for severity, count in severity_counts.items():
                if count > 0:
                    lines.append(f"  {severity.title()}: {count}")
            
            if smells:
                lines.append("\nDetected Issues:")
                for smell in smells:
                    severity = smell.get('severity', 'unknown')
                    message = smell.get('message', 'No description')
                    line_num = smell.get('line', 'unknown')
                    smell_type = smell.get('type', 'Unknown')
                    lines.append(f"  • [{severity.upper()}] {smell_type} (Line {line_num}): {message}")
            
            refactoring_suggestions = data.get('refactoring_suggestions', [])
            if refactoring_suggestions:
                lines.append("\nRefactoring Suggestions:")
                for suggestion in refactoring_suggestions:
                    lines.append(f"  • {suggestion}")

        return "\n".join(lines)

    async def _auto_fix_issues(self, args: argparse.Namespace,
                              orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Automatically fix detected issues using the enhanced bug fix loop."""
        try:
            # Get code input
            code = self.get_code_input(args)

            # Validate code
            if not self.validate_python_code(code):
                return {
                    "success": False,
                    "error": "Invalid Python code syntax"
                }

            # Determine file path
            file_path = args.file if args.file else "debug_code.py"

            # Configure agent capabilities for debugging
            capabilities = AgentCapabilities(
                enable_advanced_code_generation=True,
                enable_comprehensive_testing=False,  # Focus on fixing, not testing
                enable_automatic_bug_fixing=True,
                enable_automatic_evaluation=args.enable_evaluation,
                max_fix_iterations=args.max_fix_iterations,
                evaluation_on_every_change=args.enable_evaluation,
                rollback_on_critical_issues=True
            )

            # Create advanced orchestrator
            enhanced_orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

            # Run automatic bug fixing
            fix_results = await enhanced_orchestrator.run_automatic_bug_fixing(
                code_content=code,
                file_path=file_path,
                error_context="Debug command auto-fix"
            )

            # Save fixed code if output file specified
            if args.fix_output_file and fix_results.get('success'):
                fixed_code = fix_results.get('result', {}).get('fixed_code', '')
                if fixed_code:
                    from pathlib import Path
                    output_path = Path(args.fix_output_file)
                    output_path.write_text(fixed_code, encoding='utf-8')
                    fix_results['output_file'] = str(output_path)

            return {
                "success": True,
                "message": "Auto-fix completed",
                "fix_results": fix_results,
                "iterations_used": fix_results.get('result', {}).get('iterations_used', 0),
                "issues_fixed": fix_results.get('result', {}).get('issues_fixed', []),
                "final_code": fix_results.get('result', {}).get('fixed_code', code)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Auto-fix failed: {str(e)}"
            }
        return "\n".join(lines)
