"""
Interactive CLI mode for the agent framework.
"""

from typing import Any, Dict, List, Optional
from ..core.config import FrameworkConfig
from ..core.orchestrator import AgentOrchestrator
from .utils import CLIUtils

class InteractiveCLI:
    """
    Interactive command-line interface for the agent framework.
    
    Provides an interactive shell for executing commands,
    natural language queries, and real-time framework interaction.
    """
    
    def __init__(self, 
                 orchestrator: AgentOrchestrator,
                 config: FrameworkConfig,
                 show_banner: bool = True) -> None: ...
    
    async def run(self) -> int:
        """Run the interactive CLI."""
        ...
    
    async def _process_command(self, command: str) -> None:
        """Process a user command."""
        ...
    
    async def _execute_framework_command(self, command: str) -> None:
        """Execute a framework command."""
        ...
    
    async def _handle_natural_language(self, query: str) -> None:
        """Handle natural language queries."""
        ...
    
    async def _handle_analyze_command(self, args: List[str]) -> None:
        """Handle analyze command."""
        ...
    
    async def _handle_generate_command(self, args: List[str]) -> None:
        """Handle generate command."""
        ...
    
    async def _handle_optimize_command(self, args: List[str]) -> None:
        """Handle optimize command."""
        ...
    
    async def _handle_debug_command(self, args: List[str]) -> None:
        """Handle debug command."""
        ...
    
    async def _handle_document_command(self, args: List[str]) -> None:
        """Handle document command."""
        ...
    
    async def _show_status(self) -> None:
        """Show framework status."""
        ...
    
    def _show_help(self, args: List[str]) -> None:
        """Show help information."""
        ...
    
    def _show_history(self) -> None:
        """Show command history."""
        ...
    
    def _clear_screen(self) -> None:
        """Clear the terminal screen."""
        ...
    
    def _print_banner(self) -> None:
        """Print the interactive mode banner."""
        ...
    
    def _setup_readline(self) -> None:
        """Setup readline for command history and completion."""
        ...
    
    # Properties
    @property
    def orchestrator(self) -> AgentOrchestrator: ...
    
    @property
    def config(self) -> FrameworkConfig: ...
    
    @property
    def utils(self) -> CLIUtils: ...
    
    @property
    def running(self) -> bool: ...
    
    @property
    def session_history(self) -> List[str]: ...
