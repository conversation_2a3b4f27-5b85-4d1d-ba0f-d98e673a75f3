"""
Interactive User Experience Features for CLI.

Provides enhanced user experience features including loading spinners,
interactive prompts, consistent formatting, and improved error reporting.
"""

import time
import sys
from typing import Any, Dict, List, Optional, Callable, Union
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum

try:
    from rich.console import Console
    from rich.prompt import Prompt, Confirm, IntPrompt, FloatPrompt
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.panel import Panel
    from rich.table import Table
    from rich.text import Text
    from rich.align import Align
    from rich.columns import Columns
    from rich.status import Status
    from rich import box
    _RICH_AVAILABLE = True
except ImportError:
    _RICH_AVAILABLE = False


class MessageType(Enum):
    """Types of user messages."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    DEBUG = "debug"


@dataclass
class ActionableError:
    """Error with actionable suggestions."""
    message: str
    suggestions: List[str]
    error_code: Optional[str] = None
    documentation_url: Optional[str] = None


class InteractiveUX:
    """Enhanced interactive user experience for CLI applications."""
    
    def __init__(self, console: Optional[Console] = None):
        """Initialize interactive UX."""
        if not _RICH_AVAILABLE:
            raise ImportError("Rich library is not available. Please install it with: pip install rich")
        
        self.console = console or Console()
        
        # Message styling
        self.message_styles = {
            MessageType.SUCCESS: {"color": "green", "icon": "✓"},
            MessageType.ERROR: {"color": "red", "icon": "✗"},
            MessageType.WARNING: {"color": "yellow", "icon": "⚠"},
            MessageType.INFO: {"color": "blue", "icon": "ℹ"},
            MessageType.DEBUG: {"color": "dim white", "icon": "🐛"}
        }
    
    def show_welcome_banner(self, title: str, version: str = None, 
                           description: str = None) -> None:
        """Show welcome banner with application info."""
        content_lines = [f"[bold blue]{title}[/]"]
        
        if version:
            content_lines.append(f"[dim]Version {version}[/]")
        
        if description:
            content_lines.append(f"\n{description}")
        
        panel = Panel(
            "\n".join(content_lines),
            box=box.DOUBLE,
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(Align.center(panel))
        self.console.print()
    
    def show_loading_spinner(self, message: str, task: Callable = None, 
                           spinner: str = "dots") -> Any:
        """Show loading spinner while executing a task."""
        if task is None:
            # Return context manager for manual control
            return self.console.status(f"[blue]{message}[/]", spinner=spinner)
        
        # Execute task with spinner
        with self.console.status(f"[blue]{message}[/]", spinner=spinner):
            return task()
    
    def show_progress_with_steps(self, steps: List[str], 
                               step_executor: Callable[[int, str], Any]) -> List[Any]:
        """Show progress through multiple steps."""
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            for i, step_description in enumerate(steps):
                task = progress.add_task(f"Step {i+1}/{len(steps)}: {step_description}")
                
                try:
                    result = step_executor(i, step_description)
                    results.append(result)
                    progress.update(task, description=f"✓ Completed: {step_description}")
                    time.sleep(0.5)  # Brief pause to show completion
                except Exception as e:
                    progress.update(task, description=f"✗ Failed: {step_description}")
                    self.show_error(f"Step failed: {str(e)}")
                    raise
        
        return results
    
    def show_actionable_error(self, error: ActionableError) -> None:
        """Show error with actionable suggestions."""
        # Main error message
        self.console.print(f"[red]✗ {error.message}[/]")
        
        if error.error_code:
            self.console.print(f"[dim]Error Code: {error.error_code}[/]")
        
        # Suggestions panel
        if error.suggestions:
            suggestions_text = "\n".join([
                f"[yellow]•[/] {suggestion}" 
                for suggestion in error.suggestions
            ])
            
            panel = Panel(
                suggestions_text,
                title="[bold yellow]Suggested Actions[/]",
                border_style="yellow",
                box=box.ROUNDED
            )
            self.console.print(panel)
        
        # Documentation link
        if error.documentation_url:
            self.console.print(
                f"[dim]📖 Documentation: {error.documentation_url}[/]"
            )
    
    def create_interactive_menu(self, title: str, options: List[str],
                              description: str = None, 
                              allow_multiple: bool = False) -> Union[int, List[int]]:
        """Create an interactive menu for option selection."""
        # Show menu header
        if description:
            self.console.print(f"[blue]{title}[/]")
            self.console.print(f"[dim]{description}[/]\n")
        else:
            self.console.print(f"[blue]{title}[/]\n")
        
        # Show options
        for i, option in enumerate(options, 1):
            self.console.print(f"  [cyan]{i}.[/] {option}")
        
        self.console.print()
        
        if allow_multiple:
            response = Prompt.ask(
                "[yellow]Select options (comma-separated)[/]",
                console=self.console
            )
            
            try:
                selections = [int(x.strip()) - 1 for x in response.split(",")]
                return [s for s in selections if 0 <= s < len(options)]
            except ValueError:
                self.show_error("Invalid selection format")
                return []
        else:
            while True:
                try:
                    choice = IntPrompt.ask(
                        f"[yellow]Select option (1-{len(options)})[/]",
                        console=self.console
                    )
                    
                    if 1 <= choice <= len(options):
                        return choice - 1
                    else:
                        self.show_error(f"Please select a number between 1 and {len(options)}")
                except KeyboardInterrupt:
                    raise
                except Exception:
                    self.show_error("Please enter a valid number")
    
    def create_form(self, fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create an interactive form with multiple fields."""
        self.console.print("[blue]Please fill out the following information:[/]\n")
        
        results = {}
        
        for field in fields:
            field_name = field["name"]
            field_type = field.get("type", "text")
            prompt_text = field.get("prompt", field_name.replace("_", " ").title())
            default = field.get("default")
            required = field.get("required", False)
            
            while True:
                try:
                    if field_type == "text":
                        value = Prompt.ask(
                            f"[cyan]{prompt_text}[/]",
                            default=default,
                            console=self.console
                        )
                    elif field_type == "int":
                        value = IntPrompt.ask(
                            f"[cyan]{prompt_text}[/]",
                            default=default,
                            console=self.console
                        )
                    elif field_type == "float":
                        value = FloatPrompt.ask(
                            f"[cyan]{prompt_text}[/]",
                            default=default,
                            console=self.console
                        )
                    elif field_type == "bool":
                        value = Confirm.ask(
                            f"[cyan]{prompt_text}[/]",
                            default=default or False,
                            console=self.console
                        )
                    elif field_type == "choice":
                        choices = field.get("choices", [])
                        if choices:
                            self.console.print(f"[cyan]{prompt_text}[/]")
                            for i, choice in enumerate(choices, 1):
                                self.console.print(f"  {i}. {choice}")
                            
                            choice_idx = IntPrompt.ask(
                                f"Select option (1-{len(choices)})",
                                console=self.console
                            )
                            value = choices[choice_idx - 1] if 1 <= choice_idx <= len(choices) else None
                        else:
                            value = Prompt.ask(f"[cyan]{prompt_text}[/]", console=self.console)
                    else:
                        value = Prompt.ask(f"[cyan]{prompt_text}[/]", console=self.console)
                    
                    # Validate required fields
                    if required and not value:
                        self.show_error("This field is required")
                        continue
                    
                    results[field_name] = value
                    break
                    
                except KeyboardInterrupt:
                    raise
                except Exception as e:
                    self.show_error(f"Invalid input: {str(e)}")
        
        return results
    
    def show_confirmation_dialog(self, message: str, details: List[str] = None,
                               default: bool = False) -> bool:
        """Show confirmation dialog with optional details."""
        self.console.print(f"[yellow]{message}[/]")
        
        if details:
            details_text = "\n".join([f"• {detail}" for detail in details])
            panel = Panel(
                details_text,
                title="Details",
                border_style="dim",
                box=box.SIMPLE
            )
            self.console.print(panel)
        
        return Confirm.ask(
            "[yellow]Do you want to continue?[/]",
            default=default,
            console=self.console
        )
    
    def show_summary_table(self, title: str, data: Dict[str, Any],
                          highlight_changes: bool = False) -> None:
        """Show a summary table of key-value pairs."""
        table = Table(title=title, box=box.ROUNDED)
        table.add_column("Property", style="cyan", width=20)
        table.add_column("Value", style="white")
        
        for key, value in data.items():
            key_display = key.replace("_", " ").title()
            value_display = str(value)
            
            if highlight_changes and isinstance(value, tuple) and len(value) == 2:
                # Show before -> after for changes
                old_val, new_val = value
                value_display = f"[dim]{old_val}[/] → [green]{new_val}[/]"
            
            table.add_row(key_display, value_display)
        
        self.console.print(table)
    
    def show_message(self, message: str, message_type: MessageType = MessageType.INFO,
                    title: str = None) -> None:
        """Show a formatted message."""
        style = self.message_styles.get(message_type, self.message_styles[MessageType.INFO])
        color = style["color"]
        icon = style["icon"]
        
        formatted_message = f"[{color}]{icon} {message}[/]"
        
        if title:
            panel = Panel(
                formatted_message,
                title=title,
                border_style=color,
                box=box.ROUNDED
            )
            self.console.print(panel)
        else:
            self.console.print(formatted_message)
    
    def show_success(self, message: str, title: str = None) -> None:
        """Show success message."""
        self.show_message(message, MessageType.SUCCESS, title)
    
    def show_error(self, message: str, title: str = None) -> None:
        """Show error message."""
        self.show_message(message, MessageType.ERROR, title)
    
    def show_warning(self, message: str, title: str = None) -> None:
        """Show warning message."""
        self.show_message(message, MessageType.WARNING, title)
    
    def show_info(self, message: str, title: str = None) -> None:
        """Show info message."""
        self.show_message(message, MessageType.INFO, title)
    
    def create_two_column_layout(self, left_content: str, right_content: str,
                               left_title: str = None, right_title: str = None) -> None:
        """Create a two-column layout."""
        left_panel = Panel(
            left_content,
            title=left_title,
            border_style="blue",
            box=box.ROUNDED
        )
        
        right_panel = Panel(
            right_content,
            title=right_title,
            border_style="green",
            box=box.ROUNDED
        )
        
        columns = Columns([left_panel, right_panel], equal=True)
        self.console.print(columns)
