"""
Core CLI application for the agent framework.
"""

import argparse
from typing import Any, Dict, List, Optional, Sequence
from ..core.config import FrameworkConfig
from ..core.orchestrator import AgentOrchestrator
from .utils import CLIUtils, ProgressIndicator

class AgentCLI:
    """
    Main CLI application for the agent framework.
    
    Provides command-line interface for all framework functionality
    including analysis, generation, optimization, debugging, and documentation.
    """
    
    def __init__(self) -> None: ...
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser."""
        ...
    
    async def initialize(self, config_path: Optional[str] = None) -> None:
        """Initialize the CLI application."""
        ...
    
    async def run(self, args: Optional[Sequence[str]] = None) -> int:
        """Run the CLI application."""
        ...
    
    async def _execute_command(self, args: argparse.Namespace) -> int:
        """Execute a specific command."""
        ...
    
    def _setup_commands(self, parser: argparse.ArgumentParser) -> None:
        """Setup command subparsers."""
        ...
    
    def _print_banner(self) -> None:
        """Print the application banner."""
        ...
    
    # Properties
    @property
    def config(self) -> Optional[FrameworkConfig]: ...
    
    @property
    def orchestrator(self) -> Optional[AgentOrchestrator]: ...
    
    @property
    def utils(self) -> CLIUtils: ...
    
    @property
    def progress(self) -> ProgressIndicator: ...

def main() -> int:
    """Main entry point for the CLI application."""
    ...
