"""
Workflow Visualization System for CLI operations.

Provides comprehensive workflow visualization with step-by-step progress display,
completion checkmarks, workflow phase separation, and clear status reporting.
"""

import time
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn
    from rich.tree import Tree
    from rich.text import Text
    from rich.layout import Layout
    from rich.live import Live
    from rich.align import Align
    from rich.rule import Rule
    from rich import box
    _RICH_AVAILABLE = True
except ImportError:
    _RICH_AVAILABLE = False


class WorkflowPhase(Enum):
    """Workflow phase types."""
    INITIALIZATION = "initialization"
    PREPARATION = "preparation"
    EXECUTION = "execution"
    VALIDATION = "validation"
    CLEANUP = "cleanup"
    FINALIZATION = "finalization"


class StepStatus(Enum):
    """Step status types."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


@dataclass
class WorkflowStep:
    """Represents a single workflow step."""
    id: str
    name: str
    description: str
    phase: WorkflowPhase
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    progress: float = 0.0
    total_work: Optional[int] = None
    completed_work: int = 0
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> Optional[float]:
        """Get step duration."""
        if self.start_time is None:
            return None
        end_time = self.end_time or time.time()
        return end_time - self.start_time
    
    @property
    def is_complete(self) -> bool:
        """Check if step is complete."""
        return self.status in [StepStatus.COMPLETED, StepStatus.SKIPPED]
    
    @property
    def is_failed(self) -> bool:
        """Check if step failed."""
        return self.status == StepStatus.FAILED
    
    @property
    def can_start(self) -> bool:
        """Check if step can start (dependencies met)."""
        return self.status == StepStatus.PENDING


class WorkflowVisualizer:
    """Advanced workflow visualization system."""
    
    def __init__(self, console: Optional[Console] = None):
        """Initialize workflow visualizer."""
        if not _RICH_AVAILABLE:
            raise ImportError("Rich library is not available. Please install it with: pip install rich")
        
        self.console = console or Console()
        self.steps: Dict[str, WorkflowStep] = {}
        self.phases: Dict[WorkflowPhase, List[str]] = {}
        self.workflow_title = "Workflow"
        self.workflow_start_time: Optional[float] = None
        
        # Visual elements
        self.status_icons = {
            StepStatus.PENDING: "⏳",
            StepStatus.RUNNING: "🔄",
            StepStatus.COMPLETED: "✅",
            StepStatus.FAILED: "❌",
            StepStatus.SKIPPED: "⏭️",
            StepStatus.CANCELLED: "🚫"
        }
        
        self.status_colors = {
            StepStatus.PENDING: "dim white",
            StepStatus.RUNNING: "yellow",
            StepStatus.COMPLETED: "green",
            StepStatus.FAILED: "red",
            StepStatus.SKIPPED: "blue",
            StepStatus.CANCELLED: "orange3"
        }
        
        self.phase_colors = {
            WorkflowPhase.INITIALIZATION: "cyan",
            WorkflowPhase.PREPARATION: "blue",
            WorkflowPhase.EXECUTION: "green",
            WorkflowPhase.VALIDATION: "yellow",
            WorkflowPhase.CLEANUP: "orange3",
            WorkflowPhase.FINALIZATION: "magenta"
        }
    
    def add_step(self, step_id: str, name: str, description: str,
                phase: WorkflowPhase, dependencies: Optional[List[str]] = None,
                total_work: Optional[int] = None, **metadata: Any) -> None:
        """Add a step to the workflow."""
        step = WorkflowStep(
            id=step_id,
            name=name,
            description=description,
            phase=phase,
            dependencies=dependencies or [],
            total_work=total_work,
            metadata=metadata
        )
        
        self.steps[step_id] = step
        
        # Group by phase
        if phase not in self.phases:
            self.phases[phase] = []
        self.phases[phase].append(step_id)
    
    def start_workflow(self, title: str = "Workflow Execution") -> None:
        """Start workflow visualization."""
        self.workflow_title = title
        self.workflow_start_time = time.time()
        
        # Print workflow header
        self.console.print(f"\n[bold blue]{title}[/]")
        self.console.print(f"[dim]Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}[/]")
        self.console.print(f"[dim]Total steps: {len(self.steps)} across {len(self.phases)} phases[/]\n")
        
        # Show workflow overview
        self.print_workflow_overview()
    
    def start_step(self, step_id: str) -> bool:
        """Start a workflow step."""
        if step_id not in self.steps:
            return False
        
        step = self.steps[step_id]
        
        # Check dependencies
        if not self._check_dependencies(step_id):
            self.console.print(f"[red]Cannot start step '{step.name}' - dependencies not met[/]")
            return False
        
        step.status = StepStatus.RUNNING
        step.start_time = time.time()
        
        # Print step start
        phase_color = self.phase_colors.get(step.phase, "white")
        self.console.print(
            f"[{phase_color}]{step.phase.value.title()}[/] "
            f"[bold]→[/] {self.status_icons[step.status]} {step.name}"
        )
        
        return True
    
    def update_step_progress(self, step_id: str, completed_work: Optional[int] = None,
                           increment: Optional[int] = None, progress: Optional[float] = None,
                           message: Optional[str] = None) -> None:
        """Update step progress."""
        if step_id not in self.steps:
            return
        
        step = self.steps[step_id]
        
        if completed_work is not None:
            step.completed_work = completed_work
        elif increment is not None:
            step.completed_work += increment
        
        if progress is not None:
            step.progress = progress
        elif step.total_work and step.total_work > 0:
            step.progress = (step.completed_work / step.total_work) * 100
        
        if message:
            step.description = message
    
    def complete_step(self, step_id: str, message: Optional[str] = None) -> None:
        """Complete a workflow step."""
        if step_id not in self.steps:
            return
        
        step = self.steps[step_id]
        step.status = StepStatus.COMPLETED
        step.end_time = time.time()
        step.progress = 100.0
        
        if message:
            step.description = message
        
        # Print completion
        duration_str = f"({step.duration:.1f}s)" if step.duration else ""
        self.console.print(
            f"[green]{self.status_icons[step.status]} Completed: {step.name} {duration_str}[/]"
        )
    
    def fail_step(self, step_id: str, error_message: str) -> None:
        """Mark a step as failed."""
        if step_id not in self.steps:
            return
        
        step = self.steps[step_id]
        step.status = StepStatus.FAILED
        step.end_time = time.time()
        step.error_message = error_message
        
        # Print failure
        duration_str = f"({step.duration:.1f}s)" if step.duration else ""
        self.console.print(
            f"[red]{self.status_icons[step.status]} Failed: {step.name} {duration_str}[/]"
        )
        self.console.print(f"[red]Error: {error_message}[/]")
    
    def skip_step(self, step_id: str, reason: Optional[str] = None) -> None:
        """Skip a workflow step."""
        if step_id not in self.steps:
            return
        
        step = self.steps[step_id]
        step.status = StepStatus.SKIPPED
        step.end_time = time.time()
        
        reason_str = f" - {reason}" if reason else ""
        self.console.print(
            f"[blue]{self.status_icons[step.status]} Skipped: {step.name}{reason_str}[/]"
        )
    
    def print_workflow_overview(self) -> None:
        """Print workflow overview organized by phases."""
        for phase in WorkflowPhase:
            if phase not in self.phases:
                continue
            
            phase_steps = self.phases[phase]
            if not phase_steps:
                continue
            
            # Phase header
            phase_color = self.phase_colors.get(phase, "white")
            self.console.print(f"\n[bold {phase_color}]{phase.value.title()} Phase[/]")
            
            # Steps in phase
            for step_id in phase_steps:
                step = self.steps[step_id]
                status_color = self.status_colors.get(step.status, "white")
                status_icon = self.status_icons.get(step.status, "")
                
                # Dependencies info
                deps_info = ""
                if step.dependencies:
                    deps_info = f" [dim](depends on: {', '.join(step.dependencies)})[/]"
                
                self.console.print(
                    f"  [{status_color}]{status_icon} {step.name}[/] - {step.description}{deps_info}"
                )
    
    def print_workflow_tree(self) -> None:
        """Print workflow as a tree structure."""
        tree = Tree(f"[bold blue]{self.workflow_title}[/]")
        
        for phase in WorkflowPhase:
            if phase not in self.phases:
                continue
            
            phase_steps = self.phases[phase]
            if not phase_steps:
                continue
            
            phase_color = self.phase_colors.get(phase, "white")
            phase_node = tree.add(f"[bold {phase_color}]{phase.value.title()}[/]")
            
            for step_id in phase_steps:
                step = self.steps[step_id]
                status_color = self.status_colors.get(step.status, "white")
                status_icon = self.status_icons.get(step.status, "")
                
                step_text = f"[{status_color}]{status_icon} {step.name}[/]"
                if step.duration:
                    step_text += f" [dim]({step.duration:.1f}s)[/]"
                
                step_node = phase_node.add(step_text)
                
                if step.error_message:
                    step_node.add(f"[red]Error: {step.error_message}[/]")
        
        self.console.print(tree)
    
    def print_progress_summary(self) -> None:
        """Print workflow progress summary."""
        total_steps = len(self.steps)
        completed_steps = sum(1 for step in self.steps.values() if step.is_complete)
        failed_steps = sum(1 for step in self.steps.values() if step.is_failed)
        running_steps = sum(1 for step in self.steps.values() if step.status == StepStatus.RUNNING)
        
        # Create progress table
        table = Table(title="Workflow Progress Summary", box=box.ROUNDED)
        table.add_column("Phase", style="bold")
        table.add_column("Total", justify="center")
        table.add_column("Completed", justify="center")
        table.add_column("Failed", justify="center")
        table.add_column("Running", justify="center")
        table.add_column("Progress", justify="center")
        
        for phase in WorkflowPhase:
            if phase not in self.phases:
                continue
            
            phase_steps = [self.steps[step_id] for step_id in self.phases[phase]]
            if not phase_steps:
                continue
            
            phase_total = len(phase_steps)
            phase_completed = sum(1 for step in phase_steps if step.is_complete)
            phase_failed = sum(1 for step in phase_steps if step.is_failed)
            phase_running = sum(1 for step in phase_steps if step.status == StepStatus.RUNNING)
            phase_progress = (phase_completed / phase_total) * 100 if phase_total > 0 else 0
            
            phase_color = self.phase_colors.get(phase, "white")
            
            table.add_row(
                f"[{phase_color}]{phase.value.title()}[/]",
                str(phase_total),
                f"[green]{phase_completed}[/]" if phase_completed > 0 else "0",
                f"[red]{phase_failed}[/]" if phase_failed > 0 else "0",
                f"[yellow]{phase_running}[/]" if phase_running > 0 else "0",
                f"{phase_progress:.1f}%"
            )
        
        # Add total row
        total_progress = (completed_steps / total_steps) * 100 if total_steps > 0 else 0
        table.add_row(
            "[bold]Total[/]",
            f"[bold]{total_steps}[/]",
            f"[bold green]{completed_steps}[/]",
            f"[bold red]{failed_steps}[/]",
            f"[bold yellow]{running_steps}[/]",
            f"[bold]{total_progress:.1f}%[/]"
        )
        
        self.console.print(table)
    
    def print_workflow_summary(self) -> None:
        """Print final workflow summary."""
        if not self.workflow_start_time:
            return
        
        total_duration = time.time() - self.workflow_start_time
        total_steps = len(self.steps)
        completed_steps = sum(1 for step in self.steps.values() if step.is_complete)
        failed_steps = sum(1 for step in self.steps.values() if step.is_failed)
        
        # Determine overall status
        if failed_steps > 0:
            status_text = "[red]Completed with Errors[/]"
            border_style = "red"
        elif completed_steps == total_steps:
            status_text = "[green]Completed Successfully[/]"
            border_style = "green"
        else:
            status_text = "[yellow]Partially Completed[/]"
            border_style = "yellow"
        
        # Create summary content
        summary_lines = [
            f"[bold]Status:[/] {status_text}",
            f"[bold]Total Steps:[/] {total_steps}",
            f"[bold]Completed:[/] [green]{completed_steps}[/]",
            f"[bold]Failed:[/] [red]{failed_steps}[/]",
            f"[bold]Duration:[/] {total_duration:.1f}s",
            f"[bold]Finished:[/] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        panel = Panel(
            "\n".join(summary_lines),
            title=f"[bold]{self.workflow_title} Summary[/]",
            border_style=border_style,
            box=box.DOUBLE
        )
        
        self.console.print(panel)
        
        # Print detailed tree view
        self.console.print("\n[bold]Detailed Workflow Tree:[/]")
        self.print_workflow_tree()
    
    def _check_dependencies(self, step_id: str) -> bool:
        """Check if step dependencies are met."""
        step = self.steps[step_id]
        
        for dep_id in step.dependencies:
            if dep_id not in self.steps:
                return False
            
            dep_step = self.steps[dep_id]
            if not dep_step.is_complete:
                return False
        
        return True
    
    def get_next_available_steps(self) -> List[str]:
        """Get list of steps that can be started."""
        available_steps = []
        
        for step_id, step in self.steps.items():
            if step.can_start and self._check_dependencies(step_id):
                available_steps.append(step_id)
        
        return available_steps
    
    def get_workflow_progress(self) -> float:
        """Get overall workflow progress percentage."""
        if not self.steps:
            return 0.0
        
        completed_steps = sum(1 for step in self.steps.values() if step.is_complete)
        return (completed_steps / len(self.steps)) * 100
