"""
CLI utilities for the agent framework.
"""

import sys
import time
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Iterator, Union

class CLIUtils:
    """Utility functions for CLI operations with rich terminal support."""
    
    def __init__(self, use_rich: bool = True) -> None: ...
    
    def has_rich_support(self) -> bool:
        """Check if rich terminal formatting is available and enabled."""
        ...
    
    def print_success(self, message: str) -> None:
        """Print success message in green."""
        ...
    
    def print_error(self, message: str) -> None:
        """Print error message in red."""
        ...
    
    def print_warning(self, message: str) -> None:
        """Print warning message in yellow."""
        ...
    
    def print_info(self, message: str) -> None:
        """Print info message in blue."""
        ...
    
    def print_debug(self, message: str) -> None:
        """Print debug message in gray."""
        ...
    
    def print_header(self, message: str, level: int = 1) -> None:
        """Print a header message."""
        ...
    
    def print_table(self, data: List[Dict[str, Any]], headers: Optional[List[str]] = None) -> None:
        """Print data in table format."""
        ...
    
    def print_json(self, data: Dict[str, Any], indent: int = 2) -> None:
        """Print JSON data with syntax highlighting."""
        ...
    
    def print_code(self, code: str, language: str = "python") -> None:
        """Print code with syntax highlighting."""
        ...
    
    def format_output(self, data: Any, format_type: str = "text", pretty: bool = True) -> str:
        """Format output data."""
        ...
    
    def confirm(self, message: str, default: bool = False) -> bool:
        """Ask for user confirmation."""
        ...
    
    def prompt(self, message: str, default: Optional[str] = None) -> str:
        """Prompt user for input."""
        ...
    
    def select_option(self, message: str, options: List[str]) -> str:
        """Let user select from options."""
        ...
    
    def clear(self) -> None:
        """Clear the terminal screen."""
        ...
    
    @contextmanager
    def status_context(self, message: str, spinner: str = "dots") -> Iterator[None]:
        """Context manager for status indicator with spinner."""
        ...

class ProgressIndicator:
    """Progress indicator for long-running operations."""
    
    def __init__(self, use_rich: bool = True) -> None: ...
    
    @contextmanager
    def spinner(self, message: str, spinner_type: str = "dots") -> Iterator[None]:
        """Context manager for spinner progress indicator."""
        ...
    
    @contextmanager
    def progress_bar(self, total: int, description: str = "Processing") -> Iterator['ProgressBar']:
        """Context manager for progress bar."""
        ...
    
    def start_spinner(self, message: str, spinner_type: str = "dots") -> None:
        """Start a spinner."""
        ...
    
    def stop_spinner(self) -> None:
        """Stop the current spinner."""
        ...
    
    def update_spinner_message(self, message: str) -> None:
        """Update spinner message."""
        ...

class ProgressBar:
    """Progress bar for tracking completion."""
    
    def __init__(self, total: int, description: str = "Processing") -> None: ...
    
    def update(self, advance: int = 1) -> None:
        """Update progress."""
        ...
    
    def set_description(self, description: str) -> None:
        """Set progress description."""
        ...
    
    def finish(self) -> None:
        """Finish progress tracking."""
        ...
    
    @property
    def current(self) -> int: ...
    
    @property
    def total(self) -> int: ...
    
    @property
    def percentage(self) -> float: ...
