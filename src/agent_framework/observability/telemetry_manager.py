"""
Comprehensive telemetry and observability manager for the agent framework.

Provides OpenTelemetry integration, distributed tracing, metrics collection,
and structured logging with correlation IDs.
"""

import time
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from contextlib import contextmanager, asynccontextmanager
from functools import wraps
import uuid

try:
    from opentelemetry import trace, metrics
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.sdk.metrics import MeterProvider
    from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
    from opentelemetry.exporter.jaeger.thrift import JaegerExporter
    from opentelemetry.exporter.prometheus import PrometheusMetricReader
    from opentelemetry.instrumentation.asyncio import AsyncioInstrumentor
    from opentelemetry.instrumentation.logging import LoggingInstrumentor
    from opentelemetry.propagate import set_global_textmap
    from opentelemetry.propagators.b3 import B3MultiFormat
    OTEL_AVAILABLE = True
except ImportError:
    OTEL_AVAILABLE = False

try:
    from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False


@dataclass
class TelemetryConfig:
    """Configuration for telemetry and observability."""
    service_name: str = "agent-framework"
    service_version: str = "1.0.0"
    environment: str = "development"
    
    # Tracing configuration
    enable_tracing: bool = True
    jaeger_endpoint: Optional[str] = "http://localhost:14268/api/traces"
    trace_sample_rate: float = 1.0
    
    # Metrics configuration
    enable_metrics: bool = True
    prometheus_port: int = 8000
    metrics_export_interval: int = 30
    
    # Logging configuration
    enable_structured_logging: bool = True
    log_level: str = "INFO"
    correlation_id_header: str = "X-Correlation-ID"


class TelemetryManager:
    """
    Comprehensive telemetry manager with OpenTelemetry integration.
    
    Features:
    - Distributed tracing with Jaeger
    - Prometheus metrics collection
    - Structured logging with correlation IDs
    - Automatic instrumentation for asyncio
    - Custom span and metric decorators
    - Performance monitoring
    """
    
    def __init__(self, config: TelemetryConfig = None):
        """Initialize the telemetry manager."""
        self.config = config or TelemetryConfig()
        self.logger = logging.getLogger(__name__)
        
        # OpenTelemetry components
        self._tracer = None
        self._meter = None
        self._initialized = False
        
        # Prometheus metrics
        self._prometheus_metrics = {}
        
        # Performance tracking
        self._performance_data = {}
        
        # Correlation ID tracking
        self._correlation_context = {}
        
        # Initialize if dependencies are available
        if OTEL_AVAILABLE:
            self._initialize_telemetry()
        else:
            self.logger.warning("OpenTelemetry not available - telemetry features disabled")
    
    def _initialize_telemetry(self) -> None:
        """Initialize OpenTelemetry components."""
        try:
            # Configure tracing
            if self.config.enable_tracing:
                self._setup_tracing()
            
            # Configure metrics
            if self.config.enable_metrics:
                self._setup_metrics()
            
            # Configure logging
            if self.config.enable_structured_logging:
                self._setup_logging()
            
            # Set up automatic instrumentation
            self._setup_auto_instrumentation()
            
            self._initialized = True
            self.logger.info("Telemetry manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize telemetry: {e}")
            raise
    
    def _setup_tracing(self) -> None:
        """Set up distributed tracing."""
        # Create tracer provider
        tracer_provider = TracerProvider(
            resource=self._create_resource()
        )
        trace.set_tracer_provider(tracer_provider)
        
        # Configure Jaeger exporter
        if self.config.jaeger_endpoint:
            jaeger_exporter = JaegerExporter(
                endpoint=self.config.jaeger_endpoint
            )
            span_processor = BatchSpanProcessor(jaeger_exporter)
            tracer_provider.add_span_processor(span_processor)
        
        # Set up propagation
        set_global_textmap(B3MultiFormat())
        
        # Get tracer
        self._tracer = trace.get_tracer(
            __name__,
            version=self.config.service_version
        )
        
        self.logger.info("Distributed tracing configured")
    
    def _setup_metrics(self) -> None:
        """Set up metrics collection."""
        # Create metric readers
        readers = []
        
        # Prometheus reader
        if PROMETHEUS_AVAILABLE:
            prometheus_reader = PrometheusMetricReader()
            readers.append(prometheus_reader)
            
            # Start Prometheus HTTP server
            start_http_server(self.config.prometheus_port)
            self.logger.info(f"Prometheus metrics server started on port {self.config.prometheus_port}")
        
        # Create meter provider
        meter_provider = MeterProvider(
            resource=self._create_resource(),
            metric_readers=readers
        )
        metrics.set_meter_provider(meter_provider)
        
        # Get meter
        self._meter = metrics.get_meter(
            __name__,
            version=self.config.service_version
        )
        
        # Initialize core metrics
        self._initialize_core_metrics()
        
        self.logger.info("Metrics collection configured")
    
    def _setup_logging(self) -> None:
        """Set up structured logging."""
        # Configure logging instrumentation
        LoggingInstrumentor().instrument(set_logging_format=True)
        
        # Set log level
        logging.getLogger().setLevel(getattr(logging, self.config.log_level.upper()))
        
        self.logger.info("Structured logging configured")
    
    def _setup_auto_instrumentation(self) -> None:
        """Set up automatic instrumentation."""
        # Instrument asyncio
        AsyncioInstrumentor().instrument()
        
        self.logger.info("Automatic instrumentation configured")
    
    def _create_resource(self):
        """Create OpenTelemetry resource."""
        from opentelemetry.sdk.resources import Resource
        
        return Resource.create({
            "service.name": self.config.service_name,
            "service.version": self.config.service_version,
            "deployment.environment": self.config.environment
        })
    
    def _initialize_core_metrics(self) -> None:
        """Initialize core application metrics."""
        if not self._meter:
            return
        
        # Request metrics
        self._prometheus_metrics['requests_total'] = self._meter.create_counter(
            name="requests_total",
            description="Total number of requests",
            unit="1"
        )
        
        self._prometheus_metrics['request_duration'] = self._meter.create_histogram(
            name="request_duration_seconds",
            description="Request duration in seconds",
            unit="s"
        )
        
        # Task metrics
        self._prometheus_metrics['tasks_total'] = self._meter.create_counter(
            name="tasks_total",
            description="Total number of tasks",
            unit="1"
        )
        
        self._prometheus_metrics['task_duration'] = self._meter.create_histogram(
            name="task_duration_seconds",
            description="Task execution duration in seconds",
            unit="s"
        )
        
        # System metrics
        self._prometheus_metrics['memory_usage'] = self._meter.create_gauge(
            name="memory_usage_bytes",
            description="Memory usage in bytes",
            unit="By"
        )
        
        self._prometheus_metrics['active_connections'] = self._meter.create_gauge(
            name="active_connections",
            description="Number of active connections",
            unit="1"
        )
        
        # Error metrics
        self._prometheus_metrics['errors_total'] = self._meter.create_counter(
            name="errors_total",
            description="Total number of errors",
            unit="1"
        )
    
    def generate_correlation_id(self) -> str:
        """Generate a new correlation ID."""
        return str(uuid.uuid4())
    
    @contextmanager
    def correlation_context(self, correlation_id: str = None):
        """Context manager for correlation ID tracking."""
        if correlation_id is None:
            correlation_id = self.generate_correlation_id()
        
        # Store in context
        import contextvars
        correlation_var = contextvars.ContextVar('correlation_id')
        token = correlation_var.set(correlation_id)
        
        try:
            yield correlation_id
        finally:
            correlation_var.reset(token)
    
    @asynccontextmanager
    async def trace_span(self, 
                        name: str, 
                        attributes: Dict[str, Any] = None,
                        correlation_id: str = None):
        """Async context manager for creating traced spans."""
        if not self._tracer:
            yield None
            return
        
        span_attributes = attributes or {}
        if correlation_id:
            span_attributes['correlation.id'] = correlation_id
        
        with self._tracer.start_as_current_span(name, attributes=span_attributes) as span:
            start_time = time.time()
            try:
                yield span
            except Exception as e:
                span.record_exception(e)
                span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                raise
            finally:
                duration = time.time() - start_time
                span.set_attribute("duration.seconds", duration)
    
    def trace_function(self, name: str = None, attributes: Dict[str, Any] = None):
        """Decorator for tracing function calls."""
        def decorator(func):
            span_name = name or f"{func.__module__}.{func.__name__}"
            
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    async with self.trace_span(span_name, attributes) as span:
                        if span:
                            span.set_attribute("function.name", func.__name__)
                            span.set_attribute("function.module", func.__module__)
                        return await func(*args, **kwargs)
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    if not self._tracer:
                        return func(*args, **kwargs)
                    
                    with self._tracer.start_as_current_span(span_name, attributes=attributes) as span:
                        span.set_attribute("function.name", func.__name__)
                        span.set_attribute("function.module", func.__module__)
                        try:
                            return func(*args, **kwargs)
                        except Exception as e:
                            span.record_exception(e)
                            span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                            raise
                return sync_wrapper
        return decorator
    
    def record_metric(self, 
                     metric_name: str, 
                     value: float, 
                     attributes: Dict[str, Any] = None,
                     metric_type: str = "counter"):
        """Record a metric value."""
        if not self._meter or metric_name not in self._prometheus_metrics:
            return
        
        metric = self._prometheus_metrics[metric_name]
        attrs = attributes or {}
        
        if metric_type == "counter":
            metric.add(value, attrs)
        elif metric_type == "histogram":
            metric.record(value, attrs)
        elif metric_type == "gauge":
            metric.set(value, attrs)
    
    def time_operation(self, operation_name: str, attributes: Dict[str, Any] = None):
        """Decorator for timing operations."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                
                async with self.trace_span(f"operation.{operation_name}", attributes) as span:
                    try:
                        result = await func(*args, **kwargs)
                        
                        # Record success metrics
                        duration = time.time() - start_time
                        self.record_metric(
                            "request_duration",
                            duration,
                            {"operation": operation_name, "status": "success"},
                            "histogram"
                        )
                        
                        return result
                        
                    except Exception as e:
                        # Record error metrics
                        duration = time.time() - start_time
                        self.record_metric(
                            "request_duration",
                            duration,
                            {"operation": operation_name, "status": "error"},
                            "histogram"
                        )
                        self.record_metric(
                            "errors_total",
                            1,
                            {"operation": operation_name, "error_type": type(e).__name__}
                        )
                        raise
            
            return wrapper
        return decorator
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return dict(self._performance_data)
    
    def is_initialized(self) -> bool:
        """Check if telemetry is initialized."""
        return self._initialized
    
    async def shutdown(self) -> None:
        """Shutdown telemetry components."""
        if self._tracer:
            # Flush any pending spans
            trace.get_tracer_provider().force_flush(timeout_millis=5000)
        
        if self._meter:
            # Flush any pending metrics
            metrics.get_meter_provider().force_flush(timeout_millis=5000)
        
        self.logger.info("Telemetry manager shutdown complete")
