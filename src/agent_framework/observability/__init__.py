"""
Comprehensive observability module for the agent framework.

Integrates telemetry, metrics, health monitoring, and dashboards
into a unified observability system.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .telemetry_manager import TelemetryManager, TelemetryConfig
from .metrics_collector import MetricsCollector, MetricDefinition
from ..health.health_monitor import HealthMonitor
from ..health.health_endpoints import HealthEndpoints
from .dashboard_config import DashboardGenerator

try:
    from fastapi import FastAPI
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False


@dataclass
class ObservabilityConfig:
    """Configuration for the observability system."""
    # Telemetry settings
    service_name: str = "agent-framework"
    service_version: str = "1.0.0"
    environment: str = "development"
    enable_tracing: bool = True
    enable_metrics: bool = True
    
    # Metrics settings
    prometheus_port: int = 8000
    metrics_collection_interval: float = 30.0
    
    # Health monitoring settings
    health_check_interval: float = 30.0
    health_history_size: int = 100
    
    # Dashboard settings
    export_dashboards: bool = True
    dashboard_output_dir: str = "dashboards"


class ObservabilityManager:
    """
    Unified observability manager.
    
    Provides a single interface for all observability features including
    telemetry, metrics, health monitoring, and dashboard generation.
    """
    
    def __init__(self, config: ObservabilityConfig = None):
        """Initialize the observability manager."""
        self.config = config or ObservabilityConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self._telemetry_manager = None
        self._metrics_collector = None
        self._health_monitor = None
        self._health_endpoints = None
        self._dashboard_generator = None
        
        # State
        self._initialized = False
        self._running = False
    
    async def initialize(self) -> None:
        """Initialize all observability components."""
        if self._initialized:
            return
        
        try:
            # Initialize telemetry
            telemetry_config = TelemetryConfig(
                service_name=self.config.service_name,
                service_version=self.config.service_version,
                environment=self.config.environment,
                enable_tracing=self.config.enable_tracing,
                enable_metrics=self.config.enable_metrics,
                prometheus_port=self.config.prometheus_port
            )
            self._telemetry_manager = TelemetryManager(telemetry_config)
            
            # Initialize metrics collector
            self._metrics_collector = MetricsCollector(
                collection_interval=self.config.metrics_collection_interval
            )
            
            # Initialize health monitor
            self._health_monitor = HealthMonitor(
                check_interval=self.config.health_check_interval,
                history_size=self.config.health_history_size
            )
            
            # Initialize health endpoints
            self._health_endpoints = HealthEndpoints(self._health_monitor)
            
            # Initialize dashboard generator
            self._dashboard_generator = DashboardGenerator()
            
            self._initialized = True
            self.logger.info("Observability manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize observability manager: {e}")
            raise
    
    async def start(self) -> None:
        """Start all observability services."""
        if not self._initialized:
            await self.initialize()
        
        if self._running:
            return
        
        try:
            # Start health monitoring
            await self._health_monitor.start_monitoring()
            
            # Start metrics collection
            await self._metrics_collector.start_collection()
            
            # Export dashboards if configured
            if self.config.export_dashboards:
                self._export_dashboards()
            
            self._running = True
            self.logger.info("Observability services started")
            
        except Exception as e:
            self.logger.error(f"Failed to start observability services: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop all observability services."""
        if not self._running:
            return
        
        try:
            # Stop metrics collection
            if self._metrics_collector:
                await self._metrics_collector.stop_collection()
            
            # Stop health monitoring
            if self._health_monitor:
                await self._health_monitor.stop_monitoring()
            
            # Shutdown telemetry
            if self._telemetry_manager:
                await self._telemetry_manager.shutdown()
            
            self._running = False
            self.logger.info("Observability services stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping observability services: {e}")
    
    def register_health_endpoints(self, app: 'FastAPI') -> None:
        """Register health endpoints with FastAPI app."""
        if not FASTAPI_AVAILABLE:
            self.logger.warning("FastAPI not available - health endpoints not registered")
            return
        
        if self._health_endpoints:
            self._health_endpoints.register_routes(app)
            self.logger.info("Health endpoints registered")
    
    def register_custom_metric(self, metric_def: MetricDefinition) -> bool:
        """Register a custom metric."""
        if self._metrics_collector:
            return self._metrics_collector.register_custom_metric(metric_def)
        return False
    
    def record_metric(self, 
                     metric_name: str, 
                     value: float,
                     labels: Optional[Dict[str, str]] = None,
                     metric_type: str = "counter") -> None:
        """Record a metric value."""
        if self._metrics_collector:
            if metric_type == "counter":
                self._metrics_collector.increment_counter(metric_name, value, labels)
            elif metric_type == "histogram":
                self._metrics_collector.observe_histogram(metric_name, value, labels)
            elif metric_type == "gauge":
                self._metrics_collector.set_gauge(metric_name, value, labels)
    
    async def trace_operation(self, operation_name: str, **kwargs):
        """Context manager for tracing operations."""
        if self._telemetry_manager:
            return self._telemetry_manager.trace_span(operation_name, kwargs)
        else:
            # Return a no-op context manager
            from contextlib import asynccontextmanager
            
            @asynccontextmanager
            async def noop_context():
                yield None
            
            return noop_context()
    
    def trace_function(self, name: str = None, **kwargs):
        """Decorator for tracing functions."""
        if self._telemetry_manager:
            return self._telemetry_manager.trace_function(name, kwargs)
        else:
            # Return a no-op decorator
            def noop_decorator(func):
                return func
            return noop_decorator
    
    def time_operation(self, operation_name: str, **kwargs):
        """Decorator for timing operations."""
        if self._telemetry_manager:
            return self._telemetry_manager.time_operation(operation_name, kwargs)
        else:
            # Return a no-op decorator
            def noop_decorator(func):
                return func
            return noop_decorator
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        if self._health_monitor:
            return await self._health_monitor.get_health_status()
        return {"status": "unknown", "message": "Health monitor not available"}
    
    async def get_readiness_status(self) -> Dict[str, Any]:
        """Get readiness status."""
        if self._health_monitor:
            return await self._health_monitor.get_readiness_status()
        return {"ready": False, "message": "Health monitor not available"}
    
    def get_metrics_exposition(self) -> str:
        """Get metrics in Prometheus format."""
        if self._metrics_collector:
            return self._metrics_collector.get_metrics_exposition()
        return ""
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = {}
        
        if self._telemetry_manager:
            stats["telemetry"] = self._telemetry_manager.get_performance_stats()
        
        if self._metrics_collector:
            stats["metrics"] = self._metrics_collector.get_performance_stats()
        
        return stats
    
    def _export_dashboards(self) -> None:
        """Export monitoring dashboards."""
        try:
            if self._dashboard_generator:
                exported = self._dashboard_generator.export_all_dashboards(
                    self.config.dashboard_output_dir
                )
                self.logger.info(f"Exported dashboards: {list(exported.keys())}")
        except Exception as e:
            self.logger.error(f"Failed to export dashboards: {e}")
    
    async def add_database_health_check(self, database_pool) -> None:
        """Add database health check."""
        if self._health_endpoints:
            await self._health_endpoints.create_database_health_check(database_pool)
    
    async def add_cache_health_check(self, cache_manager) -> None:
        """Add cache health check."""
        if self._health_endpoints:
            await self._health_endpoints.create_cache_health_check(cache_manager)
    
    async def add_external_service_health_check(self, 
                                               service_name: str,
                                               health_url: str,
                                               timeout: float = 10.0) -> None:
        """Add external service health check."""
        if self._health_endpoints:
            await self._health_endpoints.create_external_service_health_check(
                service_name, health_url, timeout
            )
    
    @property
    def is_initialized(self) -> bool:
        """Check if observability is initialized."""
        return self._initialized
    
    @property
    def is_running(self) -> bool:
        """Check if observability services are running."""
        return self._running


# Global observability manager instance
observability_manager = ObservabilityManager()


# Convenience functions
async def initialize_observability(config: ObservabilityConfig = None) -> ObservabilityManager:
    """Initialize and start observability services."""
    global observability_manager
    
    if config:
        observability_manager = ObservabilityManager(config)
    
    await observability_manager.initialize()
    await observability_manager.start()
    
    return observability_manager


async def shutdown_observability() -> None:
    """Shutdown observability services."""
    global observability_manager
    await observability_manager.stop()


def trace_operation(operation_name: str, **kwargs):
    """Convenience function for tracing operations."""
    return observability_manager.trace_operation(operation_name, **kwargs)


def trace_function(name: str = None, **kwargs):
    """Convenience decorator for tracing functions."""
    return observability_manager.trace_function(name, **kwargs)


def time_operation(operation_name: str, **kwargs):
    """Convenience decorator for timing operations."""
    return observability_manager.time_operation(operation_name, **kwargs)


def record_metric(metric_name: str, 
                 value: float,
                 labels: Optional[Dict[str, str]] = None,
                 metric_type: str = "counter") -> None:
    """Convenience function for recording metrics."""
    observability_manager.record_metric(metric_name, value, labels, metric_type)


# Export main components
__all__ = [
    'ObservabilityManager',
    'ObservabilityConfig',
    'TelemetryManager',
    'TelemetryConfig',
    'MetricsCollector',
    'MetricDefinition',
    'HealthMonitor',
    'HealthEndpoints',
    'DashboardGenerator',
    'observability_manager',
    'initialize_observability',
    'shutdown_observability',
    'trace_operation',
    'trace_function',
    'time_operation',
    'record_metric'
]
