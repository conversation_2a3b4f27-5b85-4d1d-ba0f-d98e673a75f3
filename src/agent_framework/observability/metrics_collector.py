"""
Comprehensive metrics collection for the agent framework.

Provides system metrics, application metrics, and custom business metrics
with Prometheus integration and real-time monitoring capabilities.
"""

import time
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import threading

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from prometheus_client import (
        Counter, Histogram, Gauge, Info, Summary,
        CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
    )
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False


@dataclass
class MetricDefinition:
    """Definition of a metric."""
    name: str
    description: str
    metric_type: str  # counter, histogram, gauge, summary, info
    labels: List[str] = field(default_factory=list)
    buckets: Optional[List[float]] = None  # For histograms


@dataclass
class SystemMetrics:
    """System-level metrics."""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_bytes: int = 0
    memory_available_bytes: int = 0
    disk_percent: float = 0.0
    disk_used_bytes: int = 0
    disk_free_bytes: int = 0
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0
    load_average_1m: float = 0.0
    load_average_5m: float = 0.0
    load_average_15m: float = 0.0
    open_file_descriptors: int = 0


class MetricsCollector:
    """
    Comprehensive metrics collector with Prometheus integration.
    
    Features:
    - System metrics collection (CPU, memory, disk, network)
    - Application metrics (requests, tasks, errors)
    - Custom business metrics
    - Real-time metric updates
    - Prometheus exposition format
    - Metric aggregation and analysis
    """
    
    def __init__(self, 
                 registry: Optional[CollectorRegistry] = None,
                 collection_interval: float = 30.0):
        """Initialize the metrics collector."""
        self.logger = logging.getLogger(__name__)
        self.collection_interval = collection_interval
        
        # Prometheus registry
        self.registry = registry or CollectorRegistry()
        
        # Metrics storage
        self._metrics: Dict[str, Any] = {}
        self._metric_definitions: Dict[str, MetricDefinition] = {}
        
        # Collection state
        self._collecting = False
        self._collection_task: Optional[asyncio.Task] = None
        
        # System metrics tracking
        self._last_network_stats = None
        self._system_metrics_history: List[SystemMetrics] = []
        
        # Initialize core metrics
        self._initialize_core_metrics()
        
        # Performance tracking
        self._performance_counters = {
            'metrics_collected': 0,
            'collection_errors': 0,
            'last_collection_time': 0.0,
            'collection_duration_total': 0.0
        }
    
    def _initialize_core_metrics(self) -> None:
        """Initialize core application metrics."""
        if not PROMETHEUS_AVAILABLE:
            self.logger.warning("Prometheus client not available - metrics disabled")
            return
        
        # Define core metrics
        core_metrics = [
            MetricDefinition(
                name="agent_framework_info",
                description="Agent framework information",
                metric_type="info",
                labels=["version", "environment"]
            ),
            MetricDefinition(
                name="http_requests_total",
                description="Total HTTP requests",
                metric_type="counter",
                labels=["method", "endpoint", "status_code"]
            ),
            MetricDefinition(
                name="http_request_duration_seconds",
                description="HTTP request duration in seconds",
                metric_type="histogram",
                labels=["method", "endpoint"],
                buckets=[0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
            ),
            MetricDefinition(
                name="tasks_total",
                description="Total number of tasks",
                metric_type="counter",
                labels=["task_type", "status"]
            ),
            MetricDefinition(
                name="task_duration_seconds",
                description="Task execution duration in seconds",
                metric_type="histogram",
                labels=["task_type"],
                buckets=[0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0]
            ),
            MetricDefinition(
                name="active_tasks",
                description="Number of currently active tasks",
                metric_type="gauge",
                labels=["task_type"]
            ),
            MetricDefinition(
                name="plugin_operations_total",
                description="Total plugin operations",
                metric_type="counter",
                labels=["plugin_name", "operation", "status"]
            ),
            MetricDefinition(
                name="cache_operations_total",
                description="Total cache operations",
                metric_type="counter",
                labels=["cache_name", "operation"]
            ),
            MetricDefinition(
                name="cache_hit_ratio",
                description="Cache hit ratio",
                metric_type="gauge",
                labels=["cache_name"]
            ),
            MetricDefinition(
                name="database_connections_active",
                description="Number of active database connections",
                metric_type="gauge",
                labels=["database_type"]
            ),
            MetricDefinition(
                name="database_query_duration_seconds",
                description="Database query duration in seconds",
                metric_type="histogram",
                labels=["database_type", "operation"],
                buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0]
            ),
            MetricDefinition(
                name="security_events_total",
                description="Total security events",
                metric_type="counter",
                labels=["event_type", "severity"]
            ),
            MetricDefinition(
                name="system_cpu_percent",
                description="System CPU usage percentage",
                metric_type="gauge"
            ),
            MetricDefinition(
                name="system_memory_percent",
                description="System memory usage percentage",
                metric_type="gauge"
            ),
            MetricDefinition(
                name="system_memory_bytes",
                description="System memory usage in bytes",
                metric_type="gauge",
                labels=["type"]  # used, available, total
            ),
            MetricDefinition(
                name="system_disk_percent",
                description="System disk usage percentage",
                metric_type="gauge",
                labels=["mountpoint"]
            ),
            MetricDefinition(
                name="system_network_bytes_total",
                description="Total network bytes",
                metric_type="counter",
                labels=["direction"]  # sent, received
            ),
            MetricDefinition(
                name="system_load_average",
                description="System load average",
                metric_type="gauge",
                labels=["period"]  # 1m, 5m, 15m
            )
        ]
        
        # Create Prometheus metrics
        for metric_def in core_metrics:
            self._create_prometheus_metric(metric_def)
    
    def _create_prometheus_metric(self, metric_def: MetricDefinition) -> None:
        """Create a Prometheus metric from definition."""
        if not PROMETHEUS_AVAILABLE:
            return
        
        try:
            if metric_def.metric_type == "counter":
                metric = Counter(
                    metric_def.name,
                    metric_def.description,
                    labelnames=metric_def.labels,
                    registry=self.registry
                )
            elif metric_def.metric_type == "histogram":
                metric = Histogram(
                    metric_def.name,
                    metric_def.description,
                    labelnames=metric_def.labels,
                    buckets=metric_def.buckets,
                    registry=self.registry
                )
            elif metric_def.metric_type == "gauge":
                metric = Gauge(
                    metric_def.name,
                    metric_def.description,
                    labelnames=metric_def.labels,
                    registry=self.registry
                )
            elif metric_def.metric_type == "summary":
                metric = Summary(
                    metric_def.name,
                    metric_def.description,
                    labelnames=metric_def.labels,
                    registry=self.registry
                )
            elif metric_def.metric_type == "info":
                metric = Info(
                    metric_def.name,
                    metric_def.description,
                    labelnames=metric_def.labels,
                    registry=self.registry
                )
            else:
                self.logger.error(f"Unknown metric type: {metric_def.metric_type}")
                return
            
            self._metrics[metric_def.name] = metric
            self._metric_definitions[metric_def.name] = metric_def
            
        except Exception as e:
            self.logger.error(f"Failed to create metric {metric_def.name}: {e}")
    
    def register_custom_metric(self, metric_def: MetricDefinition) -> bool:
        """Register a custom metric."""
        try:
            self._create_prometheus_metric(metric_def)
            self.logger.info(f"Registered custom metric: {metric_def.name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register custom metric {metric_def.name}: {e}")
            return False
    
    def increment_counter(self, 
                         metric_name: str, 
                         value: float = 1.0,
                         labels: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric."""
        if metric_name not in self._metrics:
            return
        
        try:
            metric = self._metrics[metric_name]
            if labels:
                metric.labels(**labels).inc(value)
            else:
                metric.inc(value)
        except Exception as e:
            self.logger.error(f"Failed to increment counter {metric_name}: {e}")
    
    def observe_histogram(self, 
                         metric_name: str, 
                         value: float,
                         labels: Optional[Dict[str, str]] = None) -> None:
        """Observe a value in a histogram metric."""
        if metric_name not in self._metrics:
            return
        
        try:
            metric = self._metrics[metric_name]
            if labels:
                metric.labels(**labels).observe(value)
            else:
                metric.observe(value)
        except Exception as e:
            self.logger.error(f"Failed to observe histogram {metric_name}: {e}")
    
    def set_gauge(self, 
                  metric_name: str, 
                  value: float,
                  labels: Optional[Dict[str, str]] = None) -> None:
        """Set a gauge metric value."""
        if metric_name not in self._metrics:
            return
        
        try:
            metric = self._metrics[metric_name]
            if labels:
                metric.labels(**labels).set(value)
            else:
                metric.set(value)
        except Exception as e:
            self.logger.error(f"Failed to set gauge {metric_name}: {e}")
    
    def set_info(self, 
                 metric_name: str, 
                 info_dict: Dict[str, str]) -> None:
        """Set an info metric."""
        if metric_name not in self._metrics:
            return
        
        try:
            metric = self._metrics[metric_name]
            metric.info(info_dict)
        except Exception as e:
            self.logger.error(f"Failed to set info {metric_name}: {e}")
    
    async def start_collection(self) -> None:
        """Start automatic metrics collection."""
        if self._collecting:
            return
        
        self._collecting = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        self.logger.info("Metrics collection started")
    
    async def stop_collection(self) -> None:
        """Stop automatic metrics collection."""
        if not self._collecting:
            return
        
        self._collecting = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Metrics collection stopped")
    
    async def _collection_loop(self) -> None:
        """Main metrics collection loop."""
        while self._collecting:
            try:
                start_time = time.time()
                
                # Collect system metrics
                await self._collect_system_metrics()
                
                # Update performance counters
                collection_duration = time.time() - start_time
                self._performance_counters['metrics_collected'] += 1
                self._performance_counters['last_collection_time'] = time.time()
                self._performance_counters['collection_duration_total'] += collection_duration
                
                # Wait for next collection
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Metrics collection error: {e}")
                self._performance_counters['collection_errors'] += 1
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_system_metrics(self) -> None:
        """Collect system-level metrics."""
        if not PSUTIL_AVAILABLE:
            return
        
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            self.set_gauge("system_cpu_percent", cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.set_gauge("system_memory_percent", memory.percent)
            self.set_gauge("system_memory_bytes", memory.used, {"type": "used"})
            self.set_gauge("system_memory_bytes", memory.available, {"type": "available"})
            self.set_gauge("system_memory_bytes", memory.total, {"type": "total"})
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            self.set_gauge("system_disk_percent", disk.percent, {"mountpoint": "/"})
            
            # Network metrics
            network = psutil.net_io_counters()
            if self._last_network_stats:
                bytes_sent_delta = network.bytes_sent - self._last_network_stats.bytes_sent
                bytes_recv_delta = network.bytes_recv - self._last_network_stats.bytes_recv
                
                self.increment_counter("system_network_bytes_total", bytes_sent_delta, {"direction": "sent"})
                self.increment_counter("system_network_bytes_total", bytes_recv_delta, {"direction": "received"})
            
            self._last_network_stats = network
            
            # Load average (Unix only)
            try:
                load_avg = psutil.getloadavg()
                self.set_gauge("system_load_average", load_avg[0], {"period": "1m"})
                self.set_gauge("system_load_average", load_avg[1], {"period": "5m"})
                self.set_gauge("system_load_average", load_avg[2], {"period": "15m"})
            except (AttributeError, OSError):
                # Not available on Windows
                pass
            
            # Store system metrics for history
            system_metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_bytes=memory.used,
                memory_available_bytes=memory.available,
                disk_percent=disk.percent,
                disk_used_bytes=disk.used,
                disk_free_bytes=disk.free,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv
            )
            
            self._system_metrics_history.append(system_metrics)
            
            # Keep only last 100 entries
            if len(self._system_metrics_history) > 100:
                self._system_metrics_history.pop(0)
                
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
    
    def get_metrics_exposition(self) -> str:
        """Get metrics in Prometheus exposition format."""
        if not PROMETHEUS_AVAILABLE:
            return ""
        
        return generate_latest(self.registry).decode('utf-8')
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get metrics collection performance statistics."""
        stats = dict(self._performance_counters)
        
        if stats['metrics_collected'] > 0:
            stats['average_collection_duration'] = (
                stats['collection_duration_total'] / stats['metrics_collected']
            )
        else:
            stats['average_collection_duration'] = 0.0
        
        return stats
    
    def get_system_metrics_history(self, limit: int = 50) -> List[SystemMetrics]:
        """Get recent system metrics history."""
        return self._system_metrics_history[-limit:]
    
    def get_metric_definitions(self) -> Dict[str, MetricDefinition]:
        """Get all metric definitions."""
        return dict(self._metric_definitions)
