"""
Monitoring dashboard configurations for Grafana and other visualization tools.

Provides pre-configured dashboards for system monitoring, application metrics,
and business intelligence for the agent framework.
"""

import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict, field
from datetime import datetime


@dataclass
class GrafanaPanel:
    """Grafana panel configuration."""
    id: int
    title: str
    type: str
    targets: List[Dict[str, Any]]
    gridPos: Dict[str, int]
    options: Dict[str, Any] = field(default_factory=dict)
    fieldConfig: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON export."""
        panel_dict = asdict(self)
        if not panel_dict.get('options'):
            panel_dict['options'] = {}
        if not panel_dict.get('fieldConfig'):
            panel_dict['fieldConfig'] = {"defaults": {}, "overrides": []}
        return panel_dict


@dataclass
class GrafanaDashboard:
    """Grafana dashboard configuration."""
    title: str
    panels: List[GrafanaPanel]
    tags: List[str] = field(default_factory=list)
    time_from: str = "now-1h"
    time_to: str = "now"
    refresh: str = "30s"
    
    def to_json(self) -> str:
        """Export dashboard as JSON."""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": self.title,
                "tags": self.tags or [],
                "timezone": "browser",
                "panels": [panel.to_dict() for panel in self.panels],
                "time": {
                    "from": self.time_from,
                    "to": self.time_to
                },
                "refresh": self.refresh,
                "schemaVersion": 30,
                "version": 1,
                "links": []
            },
            "folderId": 0,
            "overwrite": True
        }
        return json.dumps(dashboard, indent=2)


class DashboardGenerator:
    """
    Generator for monitoring dashboards.
    
    Creates pre-configured dashboards for different aspects of the
    agent framework monitoring and observability.
    """
    
    def __init__(self) -> None:
        """Initialize the dashboard generator."""
        self.panel_id_counter = 1
    
    def _next_panel_id(self) -> int:
        """Get next panel ID."""
        panel_id = self.panel_id_counter
        self.panel_id_counter += 1
        return panel_id
    
    def create_system_overview_dashboard(self) -> GrafanaDashboard:
        """Create system overview dashboard."""
        panels = [
            # System CPU Usage
            GrafanaPanel(
                id=self._next_panel_id(),
                title="CPU Usage",
                type="stat",
                targets=[{
                    "expr": "system_cpu_percent",
                    "legendFormat": "CPU %",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 0, "y": 0},
                options={
                    "colorMode": "background",
                    "graphMode": "area",
                    "justifyMode": "center",
                    "orientation": "horizontal"
                },
                fieldConfig={
                    "defaults": {
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 70},
                                {"color": "red", "value": 90}
                            ]
                        },
                        "unit": "percent"
                    }
                }
            ),
            
            # Memory Usage
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Memory Usage",
                type="stat",
                targets=[{
                    "expr": "system_memory_percent",
                    "legendFormat": "Memory %",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 6, "y": 0},
                options={
                    "colorMode": "background",
                    "graphMode": "area",
                    "justifyMode": "center"
                },
                fieldConfig={
                    "defaults": {
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 80},
                                {"color": "red", "value": 95}
                            ]
                        },
                        "unit": "percent"
                    }
                }
            ),
            
            # Disk Usage
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Disk Usage",
                type="stat",
                targets=[{
                    "expr": "system_disk_percent",
                    "legendFormat": "Disk %",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 12, "y": 0},
                options={
                    "colorMode": "background",
                    "graphMode": "area",
                    "justifyMode": "center"
                },
                fieldConfig={
                    "defaults": {
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 80},
                                {"color": "red", "value": 95}
                            ]
                        },
                        "unit": "percent"
                    }
                }
            ),
            
            # Load Average
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Load Average",
                type="timeseries",
                targets=[
                    {
                        "expr": "system_load_average{period=\"1m\"}",
                        "legendFormat": "1m",
                        "refId": "A"
                    },
                    {
                        "expr": "system_load_average{period=\"5m\"}",
                        "legendFormat": "5m",
                        "refId": "B"
                    },
                    {
                        "expr": "system_load_average{period=\"15m\"}",
                        "legendFormat": "15m",
                        "refId": "C"
                    }
                ],
                gridPos={"h": 8, "w": 6, "x": 18, "y": 0}
            ),
            
            # Network Traffic
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Network Traffic",
                type="timeseries",
                targets=[
                    {
                        "expr": "rate(system_network_bytes_total{direction=\"sent\"}[5m])",
                        "legendFormat": "Sent",
                        "refId": "A"
                    },
                    {
                        "expr": "rate(system_network_bytes_total{direction=\"received\"}[5m])",
                        "legendFormat": "Received",
                        "refId": "B"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 0, "y": 8},
                fieldConfig={
                    "defaults": {
                        "unit": "Bps"
                    }
                }
            ),
            
            # System Resource Timeline
            GrafanaPanel(
                id=self._next_panel_id(),
                title="System Resources Over Time",
                type="timeseries",
                targets=[
                    {
                        "expr": "system_cpu_percent",
                        "legendFormat": "CPU %",
                        "refId": "A"
                    },
                    {
                        "expr": "system_memory_percent",
                        "legendFormat": "Memory %",
                        "refId": "B"
                    },
                    {
                        "expr": "system_disk_percent",
                        "legendFormat": "Disk %",
                        "refId": "C"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 12, "y": 8},
                fieldConfig={
                    "defaults": {
                        "unit": "percent",
                        "max": 100,
                        "min": 0
                    }
                }
            )
        ]
        
        return GrafanaDashboard(
            title="Agent Framework - System Overview",
            panels=panels,
            tags=["agent-framework", "system", "overview"]
        )
    
    def create_application_metrics_dashboard(self) -> GrafanaDashboard:
        """Create application metrics dashboard."""
        panels = [
            # Request Rate
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Request Rate",
                type="stat",
                targets=[{
                    "expr": "rate(http_requests_total[5m])",
                    "legendFormat": "Requests/sec",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 0, "y": 0},
                fieldConfig={
                    "defaults": {
                        "unit": "reqps"
                    }
                }
            ),
            
            # Response Time
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Response Time (95th percentile)",
                type="stat",
                targets=[{
                    "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                    "legendFormat": "95th percentile",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 6, "y": 0},
                fieldConfig={
                    "defaults": {
                        "unit": "s",
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 0.5},
                                {"color": "red", "value": 2.0}
                            ]
                        }
                    }
                }
            ),
            
            # Error Rate
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Error Rate",
                type="stat",
                targets=[{
                    "expr": "rate(errors_total[5m]) / rate(http_requests_total[5m]) * 100",
                    "legendFormat": "Error %",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 12, "y": 0},
                fieldConfig={
                    "defaults": {
                        "unit": "percent",
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 1},
                                {"color": "red", "value": 5}
                            ]
                        }
                    }
                }
            ),
            
            # Active Tasks
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Active Tasks",
                type="stat",
                targets=[{
                    "expr": "sum(active_tasks)",
                    "legendFormat": "Active",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 18, "y": 0}
            ),
            
            # Request Duration Histogram
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Request Duration Distribution",
                type="timeseries",
                targets=[
                    {
                        "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
                        "legendFormat": "50th percentile",
                        "refId": "A"
                    },
                    {
                        "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                        "legendFormat": "95th percentile",
                        "refId": "B"
                    },
                    {
                        "expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))",
                        "legendFormat": "99th percentile",
                        "refId": "C"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 0, "y": 8},
                fieldConfig={
                    "defaults": {
                        "unit": "s"
                    }
                }
            ),
            
            # Task Processing
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Task Processing",
                type="timeseries",
                targets=[
                    {
                        "expr": "rate(tasks_total{status=\"completed\"}[5m])",
                        "legendFormat": "Completed",
                        "refId": "A"
                    },
                    {
                        "expr": "rate(tasks_total{status=\"failed\"}[5m])",
                        "legendFormat": "Failed",
                        "refId": "B"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 12, "y": 8}
            )
        ]
        
        return GrafanaDashboard(
            title="Agent Framework - Application Metrics",
            panels=panels,
            tags=["agent-framework", "application", "performance"]
        )
    
    def create_security_dashboard(self) -> GrafanaDashboard:
        """Create security monitoring dashboard."""
        panels = [
            # Security Events
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Security Events",
                type="stat",
                targets=[{
                    "expr": "rate(security_events_total[5m])",
                    "legendFormat": "Events/sec",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 0, "y": 0},
                fieldConfig={
                    "defaults": {
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "green", "value": None},
                                {"color": "yellow", "value": 1},
                                {"color": "red", "value": 10}
                            ]
                        }
                    }
                }
            ),
            
            # Security Events by Type
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Security Events by Type",
                type="piechart",
                targets=[{
                    "expr": "sum by (event_type) (rate(security_events_total[5m]))",
                    "legendFormat": "{{event_type}}",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 6, "y": 0}
            ),
            
            # Security Events by Severity
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Security Events by Severity",
                type="timeseries",
                targets=[
                    {
                        "expr": "rate(security_events_total{severity=\"critical\"}[5m])",
                        "legendFormat": "Critical",
                        "refId": "A"
                    },
                    {
                        "expr": "rate(security_events_total{severity=\"high\"}[5m])",
                        "legendFormat": "High",
                        "refId": "B"
                    },
                    {
                        "expr": "rate(security_events_total{severity=\"medium\"}[5m])",
                        "legendFormat": "Medium",
                        "refId": "C"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 12, "y": 0}
            )
        ]
        
        return GrafanaDashboard(
            title="Agent Framework - Security Monitoring",
            panels=panels,
            tags=["agent-framework", "security", "monitoring"]
        )
    
    def create_plugin_dashboard(self) -> GrafanaDashboard:
        """Create plugin monitoring dashboard."""
        panels = [
            # Plugin Operations
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Plugin Operations",
                type="timeseries",
                targets=[
                    {
                        "expr": "rate(plugin_operations_total{status=\"success\"}[5m])",
                        "legendFormat": "Success",
                        "refId": "A"
                    },
                    {
                        "expr": "rate(plugin_operations_total{status=\"error\"}[5m])",
                        "legendFormat": "Error",
                        "refId": "B"
                    }
                ],
                gridPos={"h": 8, "w": 12, "x": 0, "y": 0}
            ),
            
            # Cache Performance
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Cache Hit Ratio",
                type="stat",
                targets=[{
                    "expr": "avg(cache_hit_ratio)",
                    "legendFormat": "Hit Ratio",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 12, "y": 0},
                fieldConfig={
                    "defaults": {
                        "unit": "percentunit",
                        "color": {"mode": "thresholds"},
                        "thresholds": {
                            "steps": [
                                {"color": "red", "value": None},
                                {"color": "yellow", "value": 0.7},
                                {"color": "green", "value": 0.9}
                            ]
                        }
                    }
                }
            ),
            
            # Database Connections
            GrafanaPanel(
                id=self._next_panel_id(),
                title="Database Connections",
                type="stat",
                targets=[{
                    "expr": "sum(database_connections_active)",
                    "legendFormat": "Active",
                    "refId": "A"
                }],
                gridPos={"h": 8, "w": 6, "x": 18, "y": 0}
            )
        ]
        
        return GrafanaDashboard(
            title="Agent Framework - Plugin Monitoring",
            panels=panels,
            tags=["agent-framework", "plugins", "performance"]
        )
    
    def export_all_dashboards(self, output_dir: str = "dashboards") -> Dict[str, str]:
        """Export all dashboards to JSON files."""
        import os
        
        os.makedirs(output_dir, exist_ok=True)
        
        dashboards = {
            "system_overview": self.create_system_overview_dashboard(),
            "application_metrics": self.create_application_metrics_dashboard(),
            "security_monitoring": self.create_security_dashboard(),
            "plugin_monitoring": self.create_plugin_dashboard()
        }
        
        exported_files = {}
        for name, dashboard in dashboards.items():
            filename = f"{name}.json"
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'w') as f:
                f.write(dashboard.to_json())
            
            exported_files[name] = filepath
        
        return exported_files


# Global dashboard generator instance
dashboard_generator = DashboardGenerator()
