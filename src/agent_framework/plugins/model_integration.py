"""
Model integration interface for plugin functions.

Provides seamless integration between plugin functions and the AI model,
enabling automatic function discovery, schema generation, and execution.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime

from .function_registry import FunctionRegistry, FunctionMetadata, FunctionCallResult
from .manager import PluginManager


@dataclass
class ModelFunction:
    """Function metadata formatted for AI model consumption."""
    name: str
    description: str
    parameters: Dict[str, Any]  # JSON Schema format
    returns: Dict[str, Any]     # Return type schema
    plugin_name: str = ""
    examples: List[Dict[str, Any]] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    deprecated: bool = False


@dataclass
class FunctionCallRequest:
    """Request to call a plugin function from the AI model."""
    function_name: str
    arguments: Dict[str, Any]
    context: Dict[str, Any] = field(default_factory=dict)
    timeout: Optional[float] = None
    validate_args: bool = True


class ModelIntegration:
    """
    Integration layer between plugin functions and AI models.
    
    Provides function discovery, schema generation, and execution
    capabilities for seamless AI model integration.
    """
    
    def __init__(self, plugin_manager: PluginManager):
        """Initialize the model integration."""
        self.plugin_manager = plugin_manager
        self.logger = logging.getLogger(__name__)
        
        # Function discovery cache
        self._function_cache: Dict[str, ModelFunction] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl: float = 300.0  # 5 minutes
        
        # Execution statistics
        self._call_stats: Dict[str, Dict[str, Any]] = {}
    
    def get_available_functions(self, 
                              refresh_cache: bool = False,
                              include_deprecated: bool = False,
                              filter_tags: Optional[List[str]] = None,
                              filter_plugins: Optional[List[str]] = None) -> List[ModelFunction]:
        """
        Get all available plugin functions formatted for AI model consumption.
        
        Args:
            refresh_cache: Force refresh of function cache
            include_deprecated: Include deprecated functions
            filter_tags: Only include functions with these tags
            filter_plugins: Only include functions from these plugins
            
        Returns:
            List of model-formatted functions
        """
        # Check cache validity
        if (not refresh_cache and 
            self._cache_timestamp and 
            (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl):
            functions = list(self._function_cache.values())
        else:
            # Refresh cache
            functions = self._build_function_cache()
        
        # Apply filters
        filtered_functions = []
        for func in functions:
            # Skip deprecated if not requested
            if func.deprecated and not include_deprecated:
                continue
            
            # Filter by tags
            if filter_tags and not any(tag in func.tags for tag in filter_tags):
                continue
            
            # Filter by plugins
            if filter_plugins and func.plugin_name not in filter_plugins:
                continue
            
            filtered_functions.append(func)
        
        return filtered_functions
    
    def get_function_by_name(self, function_name: str) -> Optional[ModelFunction]:
        """
        Get a specific function by name.
        
        Args:
            function_name: Name of the function
            
        Returns:
            Model function or None if not found
        """
        if function_name not in self._function_cache:
            self._build_function_cache()
        
        return self._function_cache.get(function_name)
    
    def get_functions_schema(self, 
                           function_names: Optional[List[str]] = None,
                           format: str = "openai") -> List[Dict[str, Any]]:
        """
        Get function schemas in various formats for AI model consumption.
        
        Args:
            function_names: Specific functions to include (all if None)
            format: Schema format ("openai", "anthropic", "generic")
            
        Returns:
            List of function schemas
        """
        functions = self.get_available_functions()
        
        if function_names:
            functions = [f for f in functions if f.name in function_names]
        
        schemas = []
        for func in functions:
            if format == "openai":
                schema = self._to_openai_schema(func)
            elif format == "anthropic":
                schema = self._to_anthropic_schema(func)
            else:
                schema = self._to_generic_schema(func)
            
            schemas.append(schema)
        
        return schemas
    
    async def call_function(self, request: FunctionCallRequest) -> FunctionCallResult:
        """
        Call a plugin function from the AI model.
        
        Args:
            request: Function call request
            
        Returns:
            Function call result
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"AI model calling function: {request.function_name}")
            
            # Validate function exists
            func_metadata = self.get_function_by_name(request.function_name)
            if not func_metadata:
                return FunctionCallResult(
                    success=False,
                    error=f"Function not found: {request.function_name}",
                    function_name=request.function_name
                )
            
            # Call the function through plugin manager
            result = await self.plugin_manager.call_plugin_function(
                request.function_name,
                request.arguments,
                request.validate_args
            )
            
            # Update statistics
            self._update_call_stats(request.function_name, result, start_time)
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            
            result = FunctionCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                function_name=request.function_name
            )
            
            self._update_call_stats(request.function_name, result, start_time)
            return result
    
    def search_functions(self, 
                        query: str,
                        max_results: int = 10,
                        include_examples: bool = True) -> List[Dict[str, Any]]:
        """
        Search for functions by query for AI model discovery.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            include_examples: Include usage examples
            
        Returns:
            List of matching functions with metadata
        """
        results = self.plugin_manager.search_functions(query)
        
        formatted_results = []
        for i, result in enumerate(results[:max_results]):
            func_data = {
                'name': result['name'],
                'plugin_name': result['plugin_name'],
                'description': result['description'],
                'tags': result['tags'],
                'relevance_score': 1.0 - (i / len(results))  # Simple relevance scoring
            }
            
            # Add schema if requested
            schema = self.plugin_manager.get_function_schema(result['name'])
            if schema:
                func_data['schema'] = schema
            
            # Add examples if requested
            if include_examples:
                func_metadata = self.get_function_by_name(result['name'])
                if func_metadata and func_metadata.examples:
                    func_data['examples'] = func_metadata.examples
            
            formatted_results.append(func_data)
        
        return formatted_results
    
    def get_function_categories(self) -> Dict[str, List[str]]:
        """
        Get functions organized by categories.
        
        Returns:
            Dictionary mapping categories to function names
        """
        functions = self.get_available_functions()
        categories = {}
        
        for func in functions:
            for tag in func.tags:
                if tag not in categories:
                    categories[tag] = []
                categories[tag].append(func.name)
        
        return categories
    
    def get_call_statistics(self) -> Dict[str, Any]:
        """
        Get function call statistics.
        
        Returns:
            Dictionary with call statistics
        """
        total_calls = sum(stats['call_count'] for stats in self._call_stats.values())
        
        return {
            'total_calls': total_calls,
            'function_stats': self._call_stats.copy(),
            'most_called': sorted(
                self._call_stats.items(),
                key=lambda x: x[1]['call_count'],
                reverse=True
            )[:10],
            'average_execution_time': sum(
                stats['average_execution_time'] * stats['call_count']
                for stats in self._call_stats.values()
            ) / max(total_calls, 1)
        }
    
    def _build_function_cache(self) -> List[ModelFunction]:
        """Build the function cache from plugin manager."""
        self._function_cache.clear()
        
        raw_functions = self.plugin_manager.get_available_functions()
        functions = []
        
        for name, func_data in raw_functions.items():
            model_func = ModelFunction(
                name=func_data['name'],
                description=func_data['description'],
                parameters=self._build_parameters_schema(func_data['parameters']),
                returns={
                    'type': func_data['return_type'],
                    'description': func_data['return_description']
                },
                plugin_name=func_data['plugin_name'],
                examples=func_data['examples'],
                tags=func_data['tags'],
                deprecated=func_data['deprecated']
            )
            
            self._function_cache[name] = model_func
            functions.append(model_func)
        
        self._cache_timestamp = datetime.now()
        return functions
    
    def _build_parameters_schema(self, parameters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build JSON Schema for function parameters."""
        properties = {}
        required = []
        
        for param in parameters:
            param_schema = {
                'type': param['type'],
                'description': param['description']
            }
            
            if param.get('default') is not None:
                param_schema['default'] = param['default']
            
            properties[param['name']] = param_schema
            
            if param['required']:
                required.append(param['name'])
        
        return {
            'type': 'object',
            'properties': properties,
            'required': required
        }
    
    def _to_openai_schema(self, func: ModelFunction) -> Dict[str, Any]:
        """Convert to OpenAI function calling schema."""
        return {
            'name': func.name,
            'description': func.description,
            'parameters': func.parameters
        }
    
    def _to_anthropic_schema(self, func: ModelFunction) -> Dict[str, Any]:
        """Convert to Anthropic function calling schema."""
        return {
            'name': func.name,
            'description': func.description,
            'input_schema': func.parameters
        }
    
    def _to_generic_schema(self, func: ModelFunction) -> Dict[str, Any]:
        """Convert to generic schema format."""
        return {
            'name': func.name,
            'description': func.description,
            'parameters': func.parameters,
            'returns': func.returns,
            'plugin_name': func.plugin_name,
            'examples': func.examples,
            'tags': func.tags,
            'deprecated': func.deprecated
        }
    
    def _update_call_stats(self, function_name: str, result: FunctionCallResult, start_time: float) -> None:
        """Update function call statistics."""
        if function_name not in self._call_stats:
            self._call_stats[function_name] = {
                'call_count': 0,
                'success_count': 0,
                'error_count': 0,
                'total_execution_time': 0.0,
                'average_execution_time': 0.0,
                'last_called': None
            }
        
        stats = self._call_stats[function_name]
        stats['call_count'] += 1
        stats['total_execution_time'] += result.execution_time
        stats['average_execution_time'] = stats['total_execution_time'] / stats['call_count']
        stats['last_called'] = datetime.now().isoformat()
        
        if result.success:
            stats['success_count'] += 1
        else:
            stats['error_count'] += 1
