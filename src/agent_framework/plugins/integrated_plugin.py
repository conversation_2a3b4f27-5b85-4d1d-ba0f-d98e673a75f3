"""
Integrated plugin base class with shared infrastructure.

This module provides an enhanced plugin base that integrates with
the shared infrastructure for better component coupling. Includes
hot reload support, function exposure, and model integration.
"""

import asyncio
import inspect
import logging
import time
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from abc import abstractmethod

from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from ..shared.base_processor import BaseProcessor, ProcessingContext
from ..shared.base_cache import BaseCache
from ..shared.data_models import StandardResult, CapabilityModel
from ..communication.enhanced_broker import EnhancedMessageBroker, TypedEvent
from ..utils.validation_utils import validate_json_schema
from .decorators import is_exposed_function, extract_function_metadata
from .function_registry import FunctionParameter, ParameterType


class IntegratedPlugin(PluginInterface, BaseProcessor[PluginRequest, PluginResponse]):
    """
    Enhanced plugin with integrated shared infrastructure.

    Combines the plugin interface with shared processing patterns,
    caching, improved communication capabilities, hot reload support,
    and automatic function exposure.
    """

    def __init__(self, plugin_name: Optional[str] = None, version: Optional[str] = None,
                 message_broker: Optional[EnhancedMessageBroker] = None,
                 cache: Optional[BaseCache] = None):
        """
        Initialize the integrated plugin.

        Args:
            plugin_name: Plugin name (if None, uses class name)
            version: Plugin version (if None, uses default)
            message_broker: Enhanced message broker for communication
            cache: Cache for performance optimization
        """
        # Initialize base classes
        PluginInterface.__init__(self)

        self._name = plugin_name or self.__class__.__name__
        # Initialize BaseProcessor manually to avoid name conflict
        self.config = {}
        self.logger = logging.getLogger(f"{__name__}.{self._name}")
        from ..shared.base_metrics import BaseMetrics
        self.metrics = BaseMetrics(f"{self._name}_processor")
        self._is_initialized = False
        self._processing_count = 0
        self._total_processed = 0
        self._total_errors = 0
        self._version = version or getattr(self, 'PLUGIN_VERSION', '1.0.0')

        # Shared infrastructure
        self.message_broker = message_broker
        self.cache = cache

        # Plugin state
        self._capabilities: List[PluginCapability] = []
        self._config: Dict[str, Any] = {}
        self._is_initialized = False

        # Performance tracking
        self._requests_processed = 0
        self._requests_failed = 0
        self._average_execution_time = 0.0

        # Hot reload support (from EnhancedPlugin)
        self._reload_count = 0
        self._last_reload_time: Optional[float] = None

        # Function exposure support (from EnhancedPlugin)
        self._exposed_functions: Dict[str, Callable] = {}

        # Automatically discover exposed functions
        self._discover_exposed_functions()
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self._name
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return str(self._version)

    @property
    def description(self) -> str:
        """Get the plugin description."""
        return getattr(self, 'PLUGIN_DESCRIPTION', self.__doc__ or '')

    @property
    def author(self) -> str:
        """Get the plugin author."""
        return getattr(self, 'PLUGIN_AUTHOR', '')

    @property
    def license(self) -> str:
        """Get the plugin license."""
        return getattr(self, 'PLUGIN_LICENSE', '')

    @property
    def dependencies(self) -> List[str]:
        """Get the plugin dependencies."""
        return getattr(self, 'PLUGIN_DEPENDENCIES', [])

    @property
    def exposed_functions(self) -> Dict[str, Callable]:
        """Get exposed functions."""
        return self._exposed_functions.copy()

    @property
    def reload_count(self) -> int:
        """Get the number of times this plugin has been reloaded."""
        return self._reload_count
    
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the integrated plugin."""
        if self._is_initialized:
            self.logger.warning(f"Plugin {self.name} already initialized")
            return

        self._config = config or {}

        # Initialize base processor
        await BaseProcessor.initialize(self)

        # Initialize plugin-specific components
        await self._initialize_plugin(self._config)

        # Set up exposed functions
        await self._setup_exposed_functions()

        # Set up event subscriptions if message broker is available
        if self.message_broker:
            await self._setup_event_subscriptions()

        self._is_initialized = True
        self.logger.info(f"Integrated plugin {self.name} initialized")
    
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Plugin-specific initialization. Override in subclasses."""
        pass
    
    async def _setup_event_subscriptions(self) -> None:
        """Set up event subscriptions for plugin communication."""
        if not self.message_broker:
            return
        
        # Subscribe to plugin requests
        self.message_broker.subscribe(
            event_type="plugin_request",
            handler=self._handle_plugin_request,
            subscriber_id=f"plugin_{self.name}",
            filter_func=lambda event: event.payload.get("plugin_name") == self.name
        )
        
        # Subscribe to capability queries
        self.message_broker.subscribe(
            event_type="plugin_capability_query",
            handler=self._handle_capability_query,
            subscriber_id=f"plugin_{self.name}"
        )
    
    async def _validate_input(self, request: PluginRequest, context: ProcessingContext) -> bool:
        """Validate plugin request input."""
        # Check if plugin supports the requested capability
        if not any(cap.name == request.capability for cap in self._capabilities):
            return False
        
        # Validate request parameters against capability schema
        capability = next(
            (cap for cap in self._capabilities if cap.name == request.capability),
            None
        )
        
        if capability and capability.input_schema:
            validation_result = validate_json_schema(
                request.parameters,
                capability.input_schema
            )
            if not validation_result["is_valid"]:
                self.logger.error(f"Request validation failed: {validation_result['errors']}")
                return False
        
        return True
    
    async def _process_data(self, request: PluginRequest, context: ProcessingContext) -> Any:
        """Process the plugin request using integrated infrastructure."""
        try:
            # Check cache for previous results
            cache_key = self._generate_cache_key(request)
            if self.cache:
                cached_result = await self.cache.get(cache_key)
                if cached_result:
                    self.logger.debug(f"Using cached result for request {request.capability}")
                    return cached_result
            
            # Execute the plugin capability
            result = await self._execute_capability(request, context)

            # Cache the result
            if self.cache:
                await self.cache.set(cache_key, result, ttl=1800)  # 30 minutes TTL

            # Update metrics
            self._requests_processed += 1

            # Store metadata for later use
            context.metadata.update({
                "plugin_name": self.name,
                "plugin_version": self.version,
                "capability": request.capability,
                "timestamp": datetime.now().isoformat()
            })

            return result
            
        except Exception as e:
            self._requests_failed += 1
            
            error_response = PluginResponse(
                success=False,
                error=str(e),
                metadata={
                    "plugin_name": self.name,
                    "plugin_version": self.version,
                    "capability": request.capability,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Publish failure event
            if self.message_broker:
                await self._publish_request_failure(request, str(e))
            
            return error_response
    
    async def _handle_error(self, error: Exception, request: PluginRequest, 
                          context: ProcessingContext) -> Optional[PluginResponse]:
        """Handle processing errors with recovery attempts."""
        self.logger.error(f"Error processing request {request.capability}: {error}")
        
        # Try to provide a meaningful error response
        return PluginResponse(
            success=False,
            error=str(error),
            metadata={
                "plugin_name": self.name,
                "error_type": type(error).__name__,
                "recovery_attempted": True
            }
        )
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        try:
            # Check if this is a function call request
            if request.capability in self._exposed_functions:
                return await self._execute_exposed_function(request)

            # Create processing context for capability-based execution
            context = ProcessingContext(
                task_id=f"{self.name}_{request.capability}_{datetime.now().timestamp()}",
                user_id=getattr(request, 'user_id', None),
                session_id=getattr(request, 'session_id', None)
            )

            # Use base processor to handle the request
            result = await self.process(request, context)
        except Exception as e:
            self.logger.error(f"Error executing request in plugin {self.name}: {e}")
            return PluginResponse(
                success=False,
                error=str(e),
                metadata={
                    "plugin_name": self.name,
                    "capability": request.capability
                }
            )
        
        # Convert StandardResult to PluginResponse
        if isinstance(result, StandardResult):
            # Merge context metadata with result metadata
            merged_metadata = {
                "plugin_name": self.name,
                "plugin_version": self.version,
                "capability": request.capability,
                "timestamp": datetime.now().isoformat()
            }
            if result.metadata:
                merged_metadata.update(result.metadata)

            response = PluginResponse(
                success=result.success,
                result=result.result,
                error=result.error,
                execution_time=result.execution_time or 0.0,
                metadata=merged_metadata
            )

            # Publish completion event
            if self.message_broker:
                await self._publish_request_completion(request, response)

            return response

        # Fallback for direct PluginResponse
        return result
    
    async def _execute_capability(self, request: PluginRequest, context: ProcessingContext) -> Any:
        """
        Execute a specific capability. Override in subclasses.
        
        Args:
            request: Plugin request
            context: Processing context
            
        Returns:
            Capability execution result
        """
        raise NotImplementedError("Subclasses must implement _execute_capability")
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        capabilities = []

        # Add capabilities from exposed functions
        for func_name, func in self._exposed_functions.items():
            metadata = extract_function_metadata(func)

            # Create capability from function metadata
            capability = PluginCapability(
                name=func_name,
                description=metadata.get('description', func.__doc__ or ''),
                input_schema=self._build_input_schema(metadata.get('parameters', [])),
                output_schema=self._build_output_schema(metadata.get('return_metadata', {}))
            )
            capabilities.append(capability)

        # Add manually registered capabilities
        capabilities.extend(self._capabilities)

        # Add plugin-specific capabilities
        plugin_capabilities = await self._get_plugin_capabilities()
        capabilities.extend(plugin_capabilities)

        return capabilities

    async def _get_plugin_capabilities(self) -> List[PluginCapability]:
        """Get plugin-specific capabilities. Override in subclasses if needed."""
        return []
    
    def add_capability(self, capability: PluginCapability) -> None:
        """Add a capability to the plugin."""
        self._capabilities.append(capability)
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        if not self._is_initialized:
            return

        try:
            # Call plugin-specific cleanup
            await self._cleanup_plugin()

            # Clear exposed functions
            self._exposed_functions.clear()

            # Unsubscribe from events
            if self.message_broker:
                self.message_broker.unsubscribe("plugin_request", f"plugin_{self.name}")
                self.message_broker.unsubscribe("plugin_capability_query", f"plugin_{self.name}")

            # Shutdown base processor
            await BaseProcessor.shutdown(self)

            self._is_initialized = False
            self.logger.info(f"Plugin {self.name} cleaned up")

        except Exception as e:
            self.logger.error(f"Error cleaning up plugin {self.name}: {e}")
            raise

    async def _cleanup_plugin(self) -> None:
        """Plugin-specific cleanup. Override in subclasses."""
        pass
    
    def _generate_cache_key(self, request: PluginRequest) -> str:
        """Generate cache key for request."""
        if self.cache:
            return self.cache.generate_key(
                self.name,
                request.capability,
                request.parameters
            )
        return ""
    
    async def _handle_plugin_request(self, event: TypedEvent) -> None:
        """Handle plugin request event."""
        request_data = event.payload
        request = PluginRequest(**request_data)
        
        try:
            response = await self.execute(request)
            
            # Send response if this was a request
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.success_result(response.__dict__)
                )
        except Exception as e:
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.error_result(str(e))
                )
    
    async def _handle_capability_query(self, event: TypedEvent) -> None:
        """Handle capability query event."""
        # Respond with plugin capabilities
        if self.message_broker:
            capabilities_data = []
            for cap in self._capabilities:
                capabilities_data.append({
                    "name": cap.name,
                    "description": cap.description,
                    "input_schema": cap.input_schema,
                    "output_schema": cap.output_schema,
                    "supported_languages": cap.supported_languages
                })
            
            response_event = TypedEvent(
                event_type="plugin_capability_response",
                payload={
                    "plugin_name": self.name,
                    "plugin_version": self.version,
                    "capabilities": capabilities_data,
                    "performance_metrics": {
                        "requests_processed": self._requests_processed,
                        "requests_failed": self._requests_failed,
                        "average_execution_time": self._average_execution_time
                    }
                },
                source=f"plugin_{self.name}",
                correlation_id=event.correlation_id
            )
            await self.message_broker.publish_event(response_event)
    
    async def _publish_request_completion(self, request: PluginRequest, response: PluginResponse) -> None:
        """Publish request completion event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="plugin_request_completed",
                payload={
                    "plugin_name": self.name,
                    "capability": request.capability,
                    "success": response.success,
                    "execution_time": response.execution_time
                },
                source=f"plugin_{self.name}"
            )
            await self.message_broker.publish_event(event)
    
    async def _publish_request_failure(self, request: PluginRequest, error: str) -> None:
        """Publish request failure event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="plugin_request_failed",
                payload={
                    "plugin_name": self.name,
                    "capability": request.capability,
                    "error": error
                },
                source=f"plugin_{self.name}"
            )
            await self.message_broker.publish_event(event)
    
    def get_plugin_stats(self) -> Dict[str, Any]:
        """Get plugin statistics."""
        return {
            "name": self.name,
            "version": self.version,
            "requests_processed": self._requests_processed,
            "requests_failed": self._requests_failed,
            "average_execution_time": self._average_execution_time,
            "capabilities_count": len(self._capabilities),
            "is_initialized": self._is_initialized
        }
    
    async def collaborate_with_plugin(self, other_plugin_name: str, 
                                    capability: str, parameters: Dict[str, Any]) -> Any:
        """
        Collaborate with another plugin.
        
        Args:
            other_plugin_name: Name of the plugin to collaborate with
            capability: Capability to request
            parameters: Parameters for the capability
            
        Returns:
            Result from the other plugin
        """
        if not self.message_broker:
            raise RuntimeError("Message broker not available for plugin collaboration")
        
        # Create collaboration request
        request = PluginRequest(
            capability=capability,
            parameters=parameters
        )
        
        # Send request via message broker
        collaboration_event = TypedEvent(
            event_type="plugin_request",
            payload={
                "plugin_name": other_plugin_name,
                **request.__dict__
            },
            source=f"plugin_{self.name}",
            target=f"plugin_{other_plugin_name}"
        )
        
        # This would need to be implemented as a request-response pattern
        await self.message_broker.publish_event(collaboration_event)
        
        # For now, return a placeholder
        return {"collaboration_result": f"Requested {capability} from {other_plugin_name}"}

    # Hot reload lifecycle methods (from EnhancedPlugin)

    async def on_before_reload(self) -> None:
        """Called before the plugin is reloaded."""
        self.logger.info(f"Plugin {self.name} preparing for reload")

        # Save any state that should persist across reloads
        await self._save_reload_state()

    async def on_after_reload(self) -> None:
        """Called after the plugin has been reloaded."""
        self._reload_count += 1
        self._last_reload_time = time.time()

        self.logger.info(f"Plugin {self.name} reloaded (count: {self._reload_count})")

        # Restore any saved state
        await self._restore_reload_state()

        # Re-discover exposed functions
        self._discover_exposed_functions()
        await self._setup_exposed_functions()

    # Hot reload state management (override if needed)

    async def _save_reload_state(self) -> None:
        """Save state before reload. Override in subclasses if needed."""
        pass

    async def _restore_reload_state(self) -> None:
        """Restore state after reload. Override in subclasses if needed."""
        pass

    # Function exposure methods (from EnhancedPlugin)

    def _discover_exposed_functions(self) -> None:
        """Discover exposed functions in the plugin."""
        self._exposed_functions.clear()

        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue

            attr = getattr(self, attr_name)
            if callable(attr) and is_exposed_function(attr):
                self._exposed_functions[attr_name] = attr
                self.logger.debug(f"Discovered exposed function: {attr_name}")

    async def _setup_exposed_functions(self) -> None:
        """Set up exposed functions for execution."""
        for func_name, func in self._exposed_functions.items():
            # Validate function metadata
            try:
                metadata = extract_function_metadata(func)
                self.logger.debug(f"Set up exposed function {func_name} with metadata: {metadata}")
            except Exception as e:
                self.logger.warning(f"Invalid metadata for function {func_name}: {e}")

    async def _execute_exposed_function(self, request: PluginRequest) -> PluginResponse:
        """Execute an exposed function."""
        func_name = request.capability
        func = self._exposed_functions[func_name]

        start_time = time.time()

        try:
            # Prepare arguments
            args = request.parameters

            # Call the function
            if inspect.iscoroutinefunction(func):
                result = await func(**args)
            else:
                result = func(**args)

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "plugin_name": self.name,
                    "function_name": func_name,
                    "execution_type": "exposed_function"
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time

            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time,
                metadata={
                    "plugin_name": self.name,
                    "function_name": func_name,
                    "execution_type": "exposed_function"
                }
            )

    def _build_input_schema(self, parameters: List[FunctionParameter]) -> Dict[str, Any]:
        """Build input schema from function parameters."""
        properties = {}
        required = []

        for param in parameters:
            param_schema = {
                "type": param.type.value,
                "description": param.description
            }

            if param.schema:
                param_schema.update(param.schema)

            if param.examples:
                param_schema["examples"] = param.examples

            if param.default is not None:
                param_schema["default"] = param.default

            properties[param.name] = param_schema

            if param.required:
                required.append(param.name)

        return {
            "type": "object",
            "properties": properties,
            "required": required
        }

    def _build_output_schema(self, return_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Build output schema from return metadata."""
        if not return_metadata:
            return {"type": "any"}

        schema = {
            "type": return_metadata.get('type', {}).value if hasattr(return_metadata.get('type', {}), 'value') else "any",
            "description": return_metadata.get('description', '')
        }

        if 'schema' in return_metadata:
            schema.update(return_metadata['schema'])

        return schema
