"""
Hot reload system for dynamic plugin loading and reloading.

Provides file system monitoring and safe plugin reloading capabilities
without requiring framework restart.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler, FileModifiedEvent, FileCreatedEvent, FileDeletedEvent
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    Observer = None

    # Create a dummy base class when watchdog is not available
    class FileSystemEventHandler:
        def on_modified(self, event):
            pass
        def on_created(self, event):
            pass
        def on_deleted(self, event):
            pass


@dataclass
class ReloadEvent:
    """Represents a plugin reload event."""
    plugin_name: str
    file_path: str
    event_type: str  # 'created', 'modified', 'deleted'
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = False
    error: Optional[str] = None
    reload_time: float = 0.0


@dataclass
class HotReloadConfig:
    """Configuration for hot reload system."""
    enabled: bool = True
    debounce_delay: float = 1.0  # Seconds to wait before reloading after file change
    max_reload_attempts: int = 3
    reload_timeout: float = 30.0  # Timeout for reload operations
    watch_patterns: List[str] = field(default_factory=lambda: ["*.py"])
    ignore_patterns: List[str] = field(default_factory=lambda: ["__pycache__", "*.pyc", ".*"])
    auto_reload_on_startup: bool = True
    log_reload_events: bool = True


class PluginFileWatcher(FileSystemEventHandler):
    """File system event handler for plugin files."""
    
    def __init__(self, hot_reload_manager: 'HotReloadManager'):
        """Initialize the file watcher."""
        super().__init__()
        self.hot_reload_manager = hot_reload_manager
        self.logger = logging.getLogger(__name__)
        self._debounce_timers: Dict[str, asyncio.Handle] = {}
    
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory and self._should_process_file(event.src_path):
            self._schedule_reload(event.src_path, 'modified')
    
    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory and self._should_process_file(event.src_path):
            self._schedule_reload(event.src_path, 'created')
    
    def on_deleted(self, event):
        """Handle file deletion events."""
        if not event.is_directory and self._should_process_file(event.src_path):
            self._schedule_reload(event.src_path, 'deleted')
    
    def _should_process_file(self, file_path: str) -> bool:
        """Check if file should trigger a reload."""
        path = Path(file_path)
        
        # Check ignore patterns
        for pattern in self.hot_reload_manager.config.ignore_patterns:
            if path.match(pattern) or any(part.startswith('.') for part in path.parts):
                return False
        
        # Check watch patterns
        for pattern in self.hot_reload_manager.config.watch_patterns:
            if path.match(pattern):
                return True
        
        return False
    
    def _schedule_reload(self, file_path: str, event_type: str):
        """Schedule a reload with debouncing."""
        # Cancel existing timer for this file
        if file_path in self._debounce_timers:
            self._debounce_timers[file_path].cancel()
        
        # Schedule new reload
        loop = asyncio.get_event_loop()
        timer = loop.call_later(
            self.hot_reload_manager.config.debounce_delay,
            lambda: asyncio.create_task(
                self.hot_reload_manager._handle_file_change(file_path, event_type)
            )
        )
        self._debounce_timers[file_path] = timer


class HotReloadManager:
    """
    Manager for hot reloading plugins.
    
    Provides file system monitoring and safe plugin reloading
    without requiring framework restart.
    """
    
    def __init__(self, plugin_manager, config: Optional[HotReloadConfig] = None):
        """Initialize the hot reload manager."""
        self.plugin_manager = plugin_manager
        self.config = config or HotReloadConfig()
        self.logger = logging.getLogger(__name__)
        
        # State
        self._is_active = False
        self._observer: Optional[Observer] = None
        self._file_watcher: Optional[PluginFileWatcher] = None
        self._reload_history: List[ReloadEvent] = []
        self._plugin_file_map: Dict[str, str] = {}  # plugin_name -> file_path
        self._reload_lock = asyncio.Lock()
        
        # Callbacks
        self._reload_callbacks: List[Callable[[ReloadEvent], None]] = []
        
        if not WATCHDOG_AVAILABLE:
            self.logger.warning("Watchdog not available. Hot reload functionality will be limited.")
    
    async def start(self) -> None:
        """Start the hot reload system."""
        if not self.config.enabled:
            self.logger.info("Hot reload is disabled")
            return
        
        if not WATCHDOG_AVAILABLE:
            self.logger.error("Cannot start hot reload: watchdog library not available")
            return
        
        if self._is_active:
            self.logger.warning("Hot reload system is already active")
            return
        
        try:
            self.logger.info("Starting hot reload system...")
            
            # Build plugin file mapping
            await self._build_plugin_file_map()
            
            # Set up file system monitoring
            self._file_watcher = PluginFileWatcher(self)
            self._observer = Observer()
            
            # Watch plugin directories
            for plugin_dir in self.plugin_manager.config.plugins.plugin_directories:
                path = Path(plugin_dir)
                if path.exists():
                    self._observer.schedule(self._file_watcher, str(path), recursive=True)
                    self.logger.info(f"Watching plugin directory: {path}")
            
            # Start observer
            self._observer.start()
            self._is_active = True
            
            self.logger.info("Hot reload system started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start hot reload system: {e}")
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """Stop the hot reload system."""
        if not self._is_active:
            return
        
        self.logger.info("Stopping hot reload system...")
        
        try:
            if self._observer:
                self._observer.stop()
                self._observer.join(timeout=5.0)
                self._observer = None
            
            self._file_watcher = None
            self._is_active = False
            
            self.logger.info("Hot reload system stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping hot reload system: {e}")
    
    async def reload_plugin(self, plugin_name: str, force: bool = False) -> ReloadEvent:
        """
        Manually reload a specific plugin.
        
        Args:
            plugin_name: Name of the plugin to reload
            force: Force reload even if no changes detected
            
        Returns:
            Reload event with results
        """
        async with self._reload_lock:
            start_time = time.time()
            event = ReloadEvent(
                plugin_name=plugin_name,
                file_path=self._plugin_file_map.get(plugin_name, ""),
                event_type='manual'
            )
            
            try:
                self.logger.info(f"Manually reloading plugin: {plugin_name}")
                
                # Check if plugin exists
                if not self.plugin_manager.registry.get_plugin(plugin_name):
                    raise ValueError(f"Plugin not found: {plugin_name}")
                
                # Perform reload
                await self._perform_plugin_reload(plugin_name)
                
                event.success = True
                event.reload_time = time.time() - start_time
                
                self.logger.info(f"Successfully reloaded plugin {plugin_name} in {event.reload_time:.2f}s")
                
            except Exception as e:
                event.error = str(e)
                event.reload_time = time.time() - start_time
                self.logger.error(f"Failed to reload plugin {plugin_name}: {e}")
            
            # Record event and notify callbacks
            self._reload_history.append(event)
            await self._notify_reload_callbacks(event)
            
            return event
    
    async def _build_plugin_file_map(self) -> None:
        """Build mapping of plugin names to file paths."""
        self._plugin_file_map.clear()
        
        for plugin_name, metadata in self.plugin_manager.registry.get_all_plugins().items():
            if metadata.file_path:
                self._plugin_file_map[plugin_name] = metadata.file_path
    
    async def _handle_file_change(self, file_path: str, event_type: str) -> None:
        """Handle a file system change event."""
        try:
            # Find affected plugins
            affected_plugins = self._find_plugins_for_file(file_path)
            
            if not affected_plugins:
                return
            
            self.logger.info(f"File {event_type}: {file_path}, affecting plugins: {affected_plugins}")
            
            # Reload affected plugins
            for plugin_name in affected_plugins:
                if event_type == 'deleted':
                    await self._handle_plugin_deletion(plugin_name, file_path)
                else:
                    await self.reload_plugin(plugin_name)
                    
        except Exception as e:
            self.logger.error(f"Error handling file change {file_path}: {e}")
    
    def _find_plugins_for_file(self, file_path: str) -> List[str]:
        """Find plugins that are affected by a file change."""
        affected = []
        file_path = str(Path(file_path).resolve())
        
        for plugin_name, plugin_file_path in self._plugin_file_map.items():
            if str(Path(plugin_file_path).resolve()) == file_path:
                affected.append(plugin_name)
        
        return affected
    
    async def _perform_plugin_reload(self, plugin_name: str) -> None:
        """Perform the actual plugin reload."""
        # Unload the plugin if it's currently loaded
        if self.plugin_manager.loader.is_plugin_loaded(plugin_name):
            await self.plugin_manager.loader.unload_plugin(plugin_name)
        
        # Clear module cache for this plugin
        await self._clear_plugin_module_cache(plugin_name)
        
        # Reload plugin metadata
        await self._reload_plugin_metadata(plugin_name)
        
        # Load the plugin again
        await self.plugin_manager.load_plugin(plugin_name)
    
    async def _clear_plugin_module_cache(self, plugin_name: str) -> None:
        """Clear Python module cache for a plugin."""
        import sys
        
        # Find and remove modules related to this plugin
        modules_to_remove = []
        for module_name in sys.modules:
            if f"plugin_{plugin_name}" in module_name:
                modules_to_remove.append(module_name)
        
        for module_name in modules_to_remove:
            del sys.modules[module_name]
            self.logger.debug(f"Removed module from cache: {module_name}")
    
    async def _reload_plugin_metadata(self, plugin_name: str) -> None:
        """Reload plugin metadata from file."""
        metadata = self.plugin_manager.registry.get_plugin(plugin_name)
        if not metadata or not metadata.file_path:
            return
        
        # Re-extract metadata from file
        new_metadata = await self.plugin_manager.loader._extract_plugin_metadata(
            Path(metadata.file_path)
        )
        
        if new_metadata:
            # Update registry with new metadata
            self.plugin_manager.registry.register_plugin(new_metadata)
    
    async def _handle_plugin_deletion(self, plugin_name: str, file_path: str) -> None:
        """Handle deletion of a plugin file."""
        self.logger.info(f"Plugin file deleted: {file_path}, unloading plugin: {plugin_name}")
        
        # Unload the plugin
        if self.plugin_manager.loader.is_plugin_loaded(plugin_name):
            await self.plugin_manager.loader.unload_plugin(plugin_name)
        
        # Remove from registry
        self.plugin_manager.registry.unregister_plugin(plugin_name)
        
        # Remove from file map
        if plugin_name in self._plugin_file_map:
            del self._plugin_file_map[plugin_name]
    
    async def _notify_reload_callbacks(self, event: ReloadEvent) -> None:
        """Notify registered callbacks about reload events."""
        for callback in self._reload_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"Error in reload callback: {e}")
    
    def add_reload_callback(self, callback: Callable[[ReloadEvent], None]) -> None:
        """Add a callback to be notified of reload events."""
        self._reload_callbacks.append(callback)
    
    def remove_reload_callback(self, callback: Callable[[ReloadEvent], None]) -> None:
        """Remove a reload callback."""
        if callback in self._reload_callbacks:
            self._reload_callbacks.remove(callback)
    
    def get_reload_history(self, limit: Optional[int] = None) -> List[ReloadEvent]:
        """Get reload event history."""
        if limit:
            return self._reload_history[-limit:]
        return self._reload_history.copy()
    
    def clear_reload_history(self) -> None:
        """Clear reload event history."""
        self._reload_history.clear()
    
    @property
    def is_active(self) -> bool:
        """Check if hot reload system is active."""
        return self._is_active
