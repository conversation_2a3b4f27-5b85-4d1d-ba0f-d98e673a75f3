"""
Plugin manager for loading and managing plugins.
"""

from typing import Any, Dict, List, Optional, Set, Callable, Awaitable
from pathlib import Path
from ..core.config import FrameworkConfig, PluginConfig
from ..core.types import PluginInterface
from .loader import <PERSON>lug<PERSON><PERSON><PERSON><PERSON>
from .registry import PluginRegistry

class PluginManager:
    """
    Plugin manager for loading and managing plugins.
    
    Provides centralized plugin lifecycle management including:
    - Plugin discovery and loading
    - Dependency resolution
    - Hot reloading
    - Plugin communication
    - Error handling and recovery
    """
    
    def __init__(self, config: PluginConfig) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the plugin manager."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the plugin manager and all plugins."""
        ...
    
    async def load_plugin(self, plugin_name: str, plugin_path: Optional[str] = None) -> bool:
        """Load a plugin by name or path."""
        ...
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a plugin."""
        ...
    
    async def reload_plugin(self, plugin_name: str) -> bool:
        """Reload a plugin."""
        ...
    
    async def load_all_plugins(self) -> Dict[str, bool]:
        """Load all available plugins."""
        ...
    
    async def discover_plugins(self, directories: Optional[List[str]] = None) -> List[str]:
        """Discover available plugins."""
        ...
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginInterface]:
        """Get a loaded plugin by name."""
        ...
    
    def get_loaded_plugins(self) -> Dict[str, PluginInterface]:
        """Get all loaded plugins."""
        ...
    
    def list_available_plugins(self) -> List[str]:
        """List all available plugins."""
        ...
    
    def list_loaded_plugins(self) -> List[str]:
        """List all loaded plugins."""
        ...
    
    async def get_plugin_capabilities(self, plugin_name: str) -> List[str]:
        """Get capabilities of a specific plugin."""
        ...
    
    async def get_all_capabilities(self) -> Dict[str, List[str]]:
        """Get capabilities of all loaded plugins."""
        ...
    
    async def call_plugin_method(self, 
                                plugin_name: str,
                                method_name: str,
                                *args: Any,
                                **kwargs: Any) -> Any:
        """Call a method on a plugin."""
        ...
    
    async def broadcast_to_plugins(self, 
                                  method_name: str,
                                  *args: Any,
                                  **kwargs: Any) -> Dict[str, Any]:
        """Broadcast a method call to all plugins."""
        ...
    
    def add_plugin_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]) -> None:
        """Add an event handler for plugin events."""
        ...
    
    def remove_plugin_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]) -> None:
        """Remove a plugin event handler."""
        ...
    
    async def validate_plugin_dependencies(self, plugin_name: str) -> List[str]:
        """Validate plugin dependencies."""
        ...
    
    async def get_plugin_info(self, plugin_name: str) -> Dict[str, Any]:
        """Get detailed information about a plugin."""
        ...
    
    async def get_plugin_metrics(self) -> Dict[str, Any]:
        """Get plugin system metrics."""
        ...
    
    # Properties
    @property
    def config(self) -> PluginConfig: ...
    
    @property
    def loader(self) -> PluginLoader: ...
    
    @property
    def registry(self) -> PluginRegistry: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def loaded_plugin_count(self) -> int: ...
