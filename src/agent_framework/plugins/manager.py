"""
Plugin manager for coordinating plugin lifecycle and operations.
Enhanced with dependency resolution, versioning, and communication.
"""

import asyncio
import hashlib
import importlib
import inspect
import json
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Awaitable, Sequence, cast, Set, Callable, Type, Union

from ..core.config import FrameworkConfig
from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from .registry import PluginRegistry, PluginMetadata
from .loader import PluginLoader
from .hot_reload import HotReloadManager, HotReloadConfig
from .function_registry import FunctionRegistry, FunctionCallResult

try:
    from packaging import version
    PACKAGING_AVAILABLE = True
except ImportError:
    PACKAGING_AVAILABLE = False


class PluginState(Enum):
    """Plugin states."""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


class MessageType(Enum):
    """Inter-plugin message types."""
    REQUEST = "request"
    RESPONSE = "response"
    EVENT = "event"
    BROADCAST = "broadcast"


@dataclass
class PluginVersion:
    """Plugin version information."""
    major: int
    minor: int
    patch: int
    pre_release: Optional[str] = None

    def __str__(self) -> str:
        version_str = f"{self.major}.{self.minor}.{self.patch}"
        if self.pre_release:
            version_str += f"-{self.pre_release}"
        return version_str

    def __lt__(self, other: 'PluginVersion') -> bool:
        return (self.major, self.minor, self.patch) < (other.major, other.minor, other.patch)

    def __eq__(self, other: 'PluginVersion') -> bool:
        return (self.major, self.minor, self.patch) == (other.major, other.minor, other.patch)

    @classmethod
    def from_string(cls, version_str: str) -> 'PluginVersion':
        """Parse version from string."""
        parts = version_str.split('-', 1)
        version_part = parts[0]
        pre_release = parts[1] if len(parts) > 1 else None

        major, minor, patch = map(int, version_part.split('.'))
        return cls(major, minor, patch, pre_release)


@dataclass
class PluginDependency:
    """Plugin dependency specification."""
    name: str
    min_version: Optional[PluginVersion] = None
    max_version: Optional[PluginVersion] = None
    optional: bool = False

    def is_satisfied_by(self, version: PluginVersion) -> bool:
        """Check if a version satisfies this dependency."""
        if self.min_version and version < self.min_version:
            return False
        if self.max_version and version > self.max_version:
            return False
        return True


@dataclass
class EnhancedPluginMetadata:
    """Enhanced plugin metadata with versioning and dependencies."""
    name: str
    version: PluginVersion
    description: str = ""
    author: str = ""
    dependencies: List[PluginDependency] = field(default_factory=list)
    provides: List[str] = field(default_factory=list)  # Capabilities provided
    requires: List[str] = field(default_factory=list)  # Capabilities required
    tags: List[str] = field(default_factory=list)
    hot_reload: bool = True
    priority: int = 100  # Loading priority (lower = higher priority)


@dataclass
class PluginMessage:
    """Inter-plugin message."""
    message_id: str
    sender: str
    recipient: Optional[str]  # None for broadcast
    message_type: MessageType
    topic: str
    payload: Any
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None


class PluginCommunicationBus:
    """Communication bus for inter-plugin messaging."""

    def __init__(self):
        """Initialize the communication bus."""
        self.logger = logging.getLogger(__name__)
        self._subscribers: Dict[str, List[str]] = {}  # topic -> plugin names
        self._message_queue: asyncio.Queue = asyncio.Queue()
        self._running = False
        self._processor_task: Optional[asyncio.Task] = None

    async def start(self) -> None:
        """Start the communication bus."""
        if self._running:
            return

        self._running = True
        self._processor_task = asyncio.create_task(self._process_messages())
        self.logger.info("Plugin communication bus started")

    async def stop(self) -> None:
        """Stop the communication bus."""
        if not self._running:
            return

        self._running = False
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass

        self.logger.info("Plugin communication bus stopped")

    def subscribe(self, plugin_name: str, topic: str) -> None:
        """Subscribe a plugin to a topic."""
        if topic not in self._subscribers:
            self._subscribers[topic] = []

        if plugin_name not in self._subscribers[topic]:
            self._subscribers[topic].append(plugin_name)
            self.logger.debug(f"Plugin {plugin_name} subscribed to topic {topic}")

    def unsubscribe(self, plugin_name: str, topic: str) -> None:
        """Unsubscribe a plugin from a topic."""
        if topic in self._subscribers and plugin_name in self._subscribers[topic]:
            self._subscribers[topic].remove(plugin_name)
            self.logger.debug(f"Plugin {plugin_name} unsubscribed from topic {topic}")

    async def send_message(self, message: PluginMessage) -> None:
        """Send a message through the bus."""
        await self._message_queue.put(message)

    async def _process_messages(self) -> None:
        """Process messages in the queue."""
        while self._running:
            try:
                message = await asyncio.wait_for(
                    self._message_queue.get(),
                    timeout=1.0
                )
                await self._deliver_message(message)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")

    async def _deliver_message(self, message: PluginMessage) -> None:
        """Deliver a message to recipients."""
        # This will be implemented to work with the PluginManager
        pass


class DependencyResolver:
    """Resolves plugin dependencies and determines loading order."""

    def __init__(self):
        """Initialize the dependency resolver."""
        self.logger = logging.getLogger(__name__)

    def resolve_dependencies(self,
                           plugins: Dict[str, EnhancedPluginMetadata]) -> List[str]:
        """
        Resolve plugin dependencies and return loading order.

        Args:
            plugins: Dictionary of plugin name to metadata

        Returns:
            List of plugin names in loading order

        Raises:
            ValueError: If dependencies cannot be resolved
        """
        # Build dependency graph
        graph = {}
        in_degree = {}

        for name, metadata in plugins.items():
            graph[name] = []
            in_degree[name] = 0

        # Add edges for dependencies
        for name, metadata in plugins.items():
            for dep in metadata.dependencies:
                if dep.name in plugins:
                    # Check version compatibility
                    dep_metadata = plugins[dep.name]
                    if not dep.is_satisfied_by(dep_metadata.version):
                        if not dep.optional:
                            raise ValueError(
                                f"Plugin {name} requires {dep.name} "
                                f"version {dep.min_version}-{dep.max_version}, "
                                f"but found {dep_metadata.version}"
                            )
                        continue

                    graph[dep.name].append(name)
                    in_degree[name] += 1
                elif not dep.optional:
                    raise ValueError(f"Required dependency {dep.name} not found for plugin {name}")

        # Topological sort with priority
        result = []
        queue = []

        # Start with plugins that have no dependencies
        for name in plugins:
            if in_degree[name] == 0:
                queue.append((plugins[name].priority, name))

        queue.sort()  # Sort by priority

        while queue:
            _, current = queue.pop(0)
            result.append(current)

            # Update in-degrees of dependent plugins
            for dependent in graph[current]:
                in_degree[dependent] -= 1
                if in_degree[dependent] == 0:
                    queue.append((plugins[dependent].priority, dependent))
                    queue.sort()

        # Check for circular dependencies
        if len(result) != len(plugins):
            remaining = [name for name in plugins if name not in result]
            raise ValueError(f"Circular dependency detected among plugins: {remaining}")

        return result

    def check_capability_requirements(self,
                                    plugins: Dict[str, EnhancedPluginMetadata]) -> Dict[str, List[str]]:
        """
        Check if all capability requirements are satisfied.

        Returns:
            Dictionary of unsatisfied requirements by plugin
        """
        # Build capability map
        provided_capabilities = set()
        for metadata in plugins.values():
            provided_capabilities.update(metadata.provides)

        # Check requirements
        unsatisfied = {}
        for name, metadata in plugins.items():
            missing = []
            for required in metadata.requires:
                if required not in provided_capabilities:
                    missing.append(required)

            if missing:
                unsatisfied[name] = missing

        return unsatisfied


class PluginManager:
    """
    Central manager for plugin lifecycle and operations.

    Coordinates plugin discovery, loading, execution, and management
    across the entire framework. Enhanced with dependency resolution,
    versioning, and inter-plugin communication.
    """

    _instance: Optional['PluginManager'] = None

    def __init__(self, config: FrameworkConfig, message_broker=None, hot_reload_config: Optional[HotReloadConfig] = None):
        """Initialize the plugin manager."""
        if PluginManager._instance is None:
            PluginManager._instance = self

        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # Core components
        self.registry = PluginRegistry()
        self.loader = PluginLoader(
            self.registry,
            getattr(self.config.plugins, 'allowed_imports', [])
        )

        # Hot reload system
        self.hot_reload_manager = HotReloadManager(
            self,
            hot_reload_config or HotReloadConfig()
        )

        # Enhanced features
        self._dependency_resolver = DependencyResolver()
        self._communication_bus = PluginCommunicationBus()
        self._enhanced_metadata: Dict[str, EnhancedPluginMetadata] = {}
        self._loading_order: List[str] = []
        self._plugin_states: Dict[str, PluginState] = {}

        # Hot-reload tracking
        self._file_checksums: Dict[Path, str] = {}
        self._watch_task: Optional[asyncio.Task] = None

        # State
        self._is_initialized = False

    async def initialize(self) -> None:
        """Initialize the plugin manager."""
        if self._is_initialized:
            return

        self.logger.info("Initializing plugin manager...")

        try:
            # Start communication bus
            await self._communication_bus.start()

            # Discover plugins in configured directories
            await self.discover_plugins()

            # Load enabled plugins if auto-load is enabled
            if getattr(self.config.plugins, 'auto_load_plugins', True):
                await self.load_all_plugins()

            # Start hot reload system
            await self.hot_reload_manager.start()

            self._is_initialized = True
            self.logger.info("Plugin manager initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize plugin manager: {e}")
            raise

    async def discover_plugins(self) -> None:
        """Discover plugins in configured directories."""
        self.logger.info("Discovering plugins...")

        discovered = await self.loader.discover_plugins(
            self.config.plugins.plugin_directories
        )

        # Register discovered plugins
        for metadata in discovered:
            try:
                self.registry.register_plugin(metadata)
            except Exception as e:
                self.logger.error(f"Failed to register plugin {metadata.name}: {e}")

        stats = self.registry.get_registry_stats()
        self.logger.info(f"Discovery complete: {stats['total_plugins']} plugins found")

    async def load_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Load a specific plugin."""
        return await self.loader.load_plugin(plugin_name, config)

    async def load_all_plugins(self) -> None:
        """Load all enabled plugins."""
        enabled_plugins = self.registry.get_enabled_plugins()

        if not enabled_plugins:
            self.logger.info("No enabled plugins to load")
            return

        # Get plugins in dependency order
        plugin_names = list(enabled_plugins.keys())
        ordered_names = self.registry.get_dependency_order(plugin_names)

        self.logger.info(f"Loading {len(ordered_names)} plugins in dependency order")

        for plugin_name in ordered_names:
            try:
                await self.load_plugin(plugin_name)
            except Exception as e:
                self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
                # Continue loading other plugins

    async def unload_plugin(self, plugin_name: str) -> None:
        """Unload a specific plugin."""
        await self.loader.unload_plugin(plugin_name)

    async def reload_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Reload a plugin."""
        return await self.loader.reload_plugin(plugin_name, config)

    async def execute_plugin_request(self, plugin_name: str, request: PluginRequest) -> PluginResponse:
        """Execute a request on a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            # Try to load the plugin if not loaded
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        try:
            # Apply timeout if specified
            if request.timeout_seconds:
                response = await asyncio.wait_for(
                    plugin_instance.execute(request),
                    timeout=request.timeout_seconds
                )
            else:
                response = await plugin_instance.execute(request)

            return response

        except asyncio.TimeoutError:
            return PluginResponse(
                success=False,
                error=f"Plugin execution timed out after {request.timeout_seconds}s"
            )
        except Exception as e:
            return PluginResponse(
                success=False,
                error=str(e)
            )

    async def get_plugin_capabilities(self, plugin_name: str) -> List[PluginCapability]:
        """Get capabilities for a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        return await plugin_instance.get_capabilities()

    async def get_all_capabilities(self) -> Dict[str, List[PluginCapability]]:
        """Get capabilities for all loaded plugins."""
        capabilities: Dict[str, List[PluginCapability]] = {}

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities[plugin_name] = await plugin_instance.get_capabilities()
            except Exception as e:
                self.logger.error(f"Failed to get capabilities for {plugin_name}: {e}")
                capabilities[plugin_name] = []

        return capabilities

    async def find_plugins_by_capability(self, capability_name: str) -> List[str]:
        """Find plugins that provide a specific capability."""
        matching_plugins: List[str] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities = await plugin_instance.get_capabilities()
                for capability in capabilities:
                    if capability.name == capability_name:
                        matching_plugins.append(plugin_name)
                        break
            except Exception as e:
                self.logger.error(f"Error checking capabilities for {plugin_name}: {e}")

        return matching_plugins

    async def get_all_tools(self) -> List[Any]:
        """Get all tools from loaded plugins for agent integration."""
        tools: List[Any] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                # Check if plugin has tools method
                if hasattr(plugin_instance, "get_tools"):
                    # Help type checker: getattr returns a callable returning Awaitable[Sequence[Any]] or List[Any]
                    get_tools_fn = cast(Any, getattr(plugin_instance, "get_tools"))
                    plugin_tools = await get_tools_fn()
                    if plugin_tools:
                        tools.extend(list(plugin_tools))
                        self.logger.debug(f"Added {len(plugin_tools)} tools from {plugin_name}")
            except Exception as e:
                self.logger.error(f"Error getting tools from {plugin_name}: {e}")

        self.logger.info(f"Collected {len(tools)} tools from plugins")
        return tools

    def get_loaded_plugins(self) -> Dict[str, PluginInterface]:
        """Get all currently loaded plugins."""
        return self.loader.get_loaded_plugins()

    def get_plugin_registry(self) -> PluginRegistry:
        """Get the plugin registry."""
        return self.registry

    def get_plugin_metadata(self, plugin_name: str) -> Optional[PluginMetadata]:
        """Get metadata for a specific plugin."""
        return self.registry.get_plugin(plugin_name)

    def enable_plugin(self, plugin_name: str) -> None:
        """Enable a plugin."""
        self.registry.enable_plugin(plugin_name)

    def disable_plugin(self, plugin_name: str) -> None:
        """Disable a plugin."""
        self.registry.disable_plugin(plugin_name)

    async def get_plugin_status(self) -> Dict[str, Any]:
        """Get comprehensive plugin status information."""
        registry_stats = self.registry.get_registry_stats()
        loaded_plugins = self.loader.get_loaded_plugins()

        plugin_details: Dict[str, Any] = {}
        for name, metadata in self.registry.get_all_plugins().items():
            plugin_details[name] = {
                "metadata": metadata,
                "loaded": name in loaded_plugins,
                "enabled": metadata.is_enabled,
                "load_count": metadata.load_count,
                "error_count": metadata.error_count,
                "last_loaded": metadata.last_loaded
            }

        return {
            "registry_stats": registry_stats,
            "loaded_count": len(loaded_plugins),
            "plugin_details": plugin_details
        }

    async def shutdown(self) -> None:
        """Shutdown the plugin manager and cleanup resources."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down plugin manager...")

        try:
            # Stop hot-reload watching
            if self._watch_task:
                self._watch_task.cancel()

            # Stop hot reload system
            await self.hot_reload_manager.stop()

            # Cleanup all loaded plugins in reverse dependency order
            for plugin_name in reversed(self._loading_order):
                await self._deactivate_plugin(plugin_name)
                await self._cleanup_plugin(plugin_name)

            # Cleanup remaining loaded plugins
            await self.loader.cleanup_all()

            # Stop communication bus
            await self._communication_bus.stop()

            # Clear registry
            self.registry.clear()
            self._enhanced_metadata.clear()
            self._plugin_states.clear()

            self._is_initialized = False
            self.logger.info("Plugin manager shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during plugin manager shutdown: {e}")

    @property
    def is_initialized(self) -> bool:
        """Check if the plugin manager is initialized."""
        return self._is_initialized

    # Function calling interface

    async def call_plugin_function(self,
                                 function_name: str,
                                 arguments: Optional[Dict[str, Any]] = None,
                                 validate_args: bool = True) -> FunctionCallResult:
        """
        Call a plugin function by name.

        Args:
            function_name: Name of the function to call
            arguments: Function arguments
            validate_args: Whether to validate arguments

        Returns:
            Function call result
        """
        return await self.loader.function_registry.call_function(
            function_name, arguments, validate_args
        )

    def get_available_functions(self) -> Dict[str, Any]:
        """
        Get all available plugin functions.

        Returns:
            Dictionary of function metadata
        """
        functions = {}
        for name, metadata in self.loader.function_registry.get_all_functions().items():
            functions[name] = {
                'name': metadata.name,
                'plugin_name': metadata.plugin_name,
                'description': metadata.description,
                'parameters': [
                    {
                        'name': p.name,
                        'type': p.type.value,
                        'description': p.description,
                        'required': p.required,
                        'default': p.default
                    }
                    for p in metadata.parameters
                ],
                'return_type': metadata.return_type.value,
                'return_description': metadata.return_description,
                'examples': metadata.examples,
                'tags': metadata.tags,
                'deprecated': metadata.deprecated,
                'version': metadata.version
            }
        return functions

    def get_function_schema(self, function_name: str) -> Optional[Dict[str, Any]]:
        """
        Get OpenAPI-style schema for a function.

        Args:
            function_name: Name of the function

        Returns:
            Function schema or None if not found
        """
        return self.loader.function_registry.get_function_schema(function_name)

    def search_functions(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for functions by name or description.

        Args:
            query: Search query

        Returns:
            List of matching function metadata
        """
        results = self.loader.function_registry.search_functions(query)
        return [
            {
                'name': metadata.name,
                'plugin_name': metadata.plugin_name,
                'description': metadata.description,
                'tags': metadata.tags
            }
            for metadata in results
        ]

    # Hot reload interface

    def get_reload_history(self, limit: Optional[int] = None):
        """
        Get plugin reload history.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of reload events
        """
        return self.hot_reload_manager.get_reload_history(limit)

    def is_hot_reload_active(self) -> bool:
        """Check if hot reload system is active."""
        return self.hot_reload_manager.is_active

    # Enhanced plugin management methods

    async def _deactivate_plugin(self, plugin_name: str) -> None:
        """Deactivate a specific plugin."""
        if plugin_name in self.loader.get_loaded_plugins():
            plugin = self.loader.get_loaded_plugins()[plugin_name]
            if hasattr(plugin, 'cleanup'):
                await plugin.cleanup()
            self._plugin_states[plugin_name] = PluginState.LOADED
            self.logger.info(f"Deactivated plugin: {plugin_name}")

    async def _cleanup_plugin(self, plugin_name: str) -> None:
        """Cleanup a specific plugin."""
        if plugin_name in self.loader.get_loaded_plugins():
            # Plugin cleanup is handled by the loader
            self._plugin_states[plugin_name] = PluginState.UNLOADED
            self.logger.info(f"Cleaned up plugin: {plugin_name}")

    async def send_message(self,
                          sender: str,
                          recipient: Optional[str],
                          topic: str,
                          payload: Any,
                          message_type: MessageType = MessageType.EVENT) -> str:
        """Send a message through the communication bus."""
        message_id = hashlib.md5(f"{sender}{topic}{datetime.now()}".encode()).hexdigest()[:8]

        message = PluginMessage(
            message_id=message_id,
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            topic=topic,
            payload=payload
        )

        await self._communication_bus.send_message(message)
        return message_id

    async def _deliver_message_to_plugin(self, plugin_name: str, message: PluginMessage) -> None:
        """Deliver a message to a specific plugin."""
        loaded_plugins = self.loader.get_loaded_plugins()
        if plugin_name in loaded_plugins:
            plugin = loaded_plugins[plugin_name]
            try:
                if hasattr(plugin, 'handle_message'):
                    await plugin.handle_message(message)
            except Exception as e:
                self.logger.error(f"Error delivering message to {plugin_name}: {e}")

    def get_enhanced_plugin_info(self) -> Dict[str, Dict[str, Any]]:
        """Get enhanced information about all plugins."""
        info = {}
        loaded_plugins = self.loader.get_loaded_plugins()

        for name, metadata in self._enhanced_metadata.items():
            info[name] = {
                "state": self._plugin_states.get(name, PluginState.UNLOADED).value,
                "version": str(metadata.version),
                "description": metadata.description,
                "dependencies": [dep.name for dep in metadata.dependencies],
                "provides": metadata.provides,
                "requires": metadata.requires,
                "loaded": name in loaded_plugins
            }
        return info