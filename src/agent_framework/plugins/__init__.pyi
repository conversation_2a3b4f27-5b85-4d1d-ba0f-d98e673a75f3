"""
Enhanced plugin system for the agent framework.
"""

from .manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as PluginManager
from .loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as PluginLoader
from .registry import PluginRegistry as PluginRegistry
from .hot_reload import (
    HotReloadManager as HotReloadManager,
    HotReloadConfig as HotReloadConfig,
    ReloadEvent as ReloadEvent
)
from .function_registry import (
    FunctionRegistry as FunctionRegistry,
    FunctionMetadata as FunctionMetadata,
    FunctionCallResult as FunctionCallResult,
    ParameterType as ParameterType
)
from .model_integration import (
    ModelIntegration as ModelIntegration,
    ModelFunction as ModelFunction,
    FunctionCallRequest as FunctionCallRequest
)
from .enhanced_plugin import EnhancedPlugin as EnhancedPlugin
from .decorators import (
    exposed_function as exposed_function,
    parameter as parameter,
    returns as returns,
    example as example,
    string_param as string_param,
    int_param as int_param,
    float_param as float_param,
    bool_param as bool_param,
    array_param as array_param,
    object_param as object_param,
    validate_input as validate_input,
    validate_output as validate_output,
    cached as cached,
    rate_limited as rate_limited
)
from .utils import (
    extract_function_metadata as extract_function_metadata,
    is_exposed_function as is_exposed_function
)

__all__: list[str]
