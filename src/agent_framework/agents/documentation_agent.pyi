"""
Specialized agent for documentation tasks.
"""

from typing import Any, Dict, List, Optional
from ..core.types import Task, TaskResult
from ..core.multi_agent_types import AgentRoleConfig, AgentCapability
from .base_agent import BaseAgent

class DocumentationAgent(BaseAgent):
    """
    Specialized agent for documentation tasks.
    
    Capabilities:
    - Docstring generation
    - API documentation creation
    - README generation
    - Code commenting
    - Documentation analysis
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs: Any) -> None: ...
    
    async def generate_docstrings(self, code: str, style: str = "google") -> Dict[str, Any]:
        """Generate docstrings for functions and classes."""
        ...
    
    async def generate_api_documentation(self, code: str, format: str = "markdown") -> Dict[str, Any]:
        """Generate API documentation."""
        ...
    
    async def generate_readme(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate README file for a project."""
        ...
    
    async def add_code_comments(self, code: str, style: str = "inline") -> Dict[str, Any]:
        """Add explanatory comments to code."""
        ...
    
    async def analyze_documentation_coverage(self, code: str) -> Dict[str, Any]:
        """Analyze documentation coverage."""
        ...
    
    async def improve_existing_documentation(self, code: str, existing_docs: str) -> Dict[str, Any]:
        """Improve existing documentation."""
        ...
    
    async def generate_changelog(self, changes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate changelog from changes."""
        ...
    
    async def create_user_guide(self, code: str, examples: List[str] = None) -> Dict[str, Any]:
        """Create user guide documentation."""
        ...
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for documentation agent."""
        ...
