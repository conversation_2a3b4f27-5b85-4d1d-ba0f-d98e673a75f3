"""
Specialized agent for error detection tasks.
"""

from typing import Any, Dict, List, Optional
from ..core.types import Task, TaskResult
from ..core.multi_agent_types import AgentRoleConfig, AgentCapability
from .base_agent import BaseAgent

class ErrorDetectionAgent(BaseAgent):
    """
    Specialized agent for error detection tasks.
    
    Capabilities:
    - Static error detection
    - Runtime error prediction
    - Bug pattern recognition
    - Exception handling analysis
    - Error prevention suggestions
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs: Any) -> None: ...
    
    async def detect_syntax_errors(self, code: str) -> Dict[str, Any]:
        """Detect syntax errors in code."""
        ...
    
    async def detect_logical_errors(self, code: str) -> Dict[str, Any]:
        """Detect potential logical errors."""
        ...
    
    async def analyze_exception_handling(self, code: str) -> Dict[str, Any]:
        """Analyze exception handling patterns."""
        ...
    
    async def detect_memory_leaks(self, code: str) -> Dict[str, Any]:
        """Detect potential memory leaks."""
        ...
    
    async def detect_race_conditions(self, code: str) -> Dict[str, Any]:
        """Detect potential race conditions."""
        ...
    
    async def analyze_error_patterns(self, code: str) -> Dict[str, Any]:
        """Analyze common error patterns."""
        ...
    
    async def suggest_error_prevention(self, code: str) -> Dict[str, Any]:
        """Suggest error prevention measures."""
        ...
    
    async def validate_input_handling(self, code: str) -> Dict[str, Any]:
        """Validate input handling and sanitization."""
        ...
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for error detection agent."""
        ...
