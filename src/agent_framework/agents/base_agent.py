"""
Base agent implementation for the multi-agent framework.

This module provides MCP (Model Context Protocol) integration using AutoGen's official
autogen_ext.tools.mcp package. The integration allows agents to use tools from MCP servers
seamlessly with AutoGen agents.

MCP Integration Features:
- Automatic discovery and loading of tools from configured MCP servers
- Support for STDIO, SSE, and Streamable HTTP MCP transports  
- Graceful fallback when MCP tools are not available
- Proper error handling and logging for MCP operations

Prerequisites:
To use MCP functionality, install the MCP extension:
    pip install "autogen-ext[mcp]"

Usage:
The BaseAgent class automatically integrates with MCP servers configured in the
agent's role_config.mcp_servers list. Tools from these servers will be made
available to the AutoGen assistant agent.

Example MCP server configurations in your role config:
    role_config = AgentRoleConfig(
        name="my_agent",
        mcp_servers=["filesystem", "web_search", "database"],
        # ... other config
    )

The MCP connection manager should be configured with server details:
    # For STDIO servers (most common)
    MCPServerInfo(
        name="filesystem", 
        command="npx",
        args=["-y", "@modelcontextprotocol/server-filesystem", "/path/to/files"],
        transport=MCPTransportType.STDIO
    )

For more examples, see: https://microsoft.github.io/autogen/stable/reference/python/autogen_ext.tools.mcp.html
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Set
from uuid import UUID, uuid4

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient

try:
    from autogen_ext.tools.mcp import (
        mcp_server_tools, 
        StdioServerParams, 
        SseServerParams,
        StreamableHttpServerParams,
        StdioMcpToolAdapter,
        SseMcpToolAdapter,
        StreamableHttpMcpToolAdapter
    )
    MCP_AVAILABLE = True
except ImportError:
    # MCP tools are optional - gracefully handle when not available
    MCP_AVAILABLE = False
    mcp_server_tools = None
    StdioServerParams = None
    SseServerParams = None
    StreamableHttpServerParams = None
    StdioMcpToolAdapter = None
    SseMcpToolAdapter = None
    StreamableHttpMcpToolAdapter = None

from ..core.multi_agent_types import (
    AgentInfo, AgentStatus, AgentCapability, AgentInterface,
    AgentCommunicationMessage, AgentMetrics
)
from ..core.types import Task, TaskResult, TaskStatus
from ..core.config import ModelConfig, AgentRoleConfig
from ..core.model_client_factory import model_client_factory
from ..mcp.connection_manager import MCPConnectionManager


class BaseAgent(AgentInterface):
    """
    Base implementation for specialized agents in the multi-agent system.
    
    Provides common functionality for all agent types including:
    - Model client management
    - MCP server integration
    - Task execution
    - Inter-agent communication
    - Metrics tracking
    """
    
    def __init__(self,
                 role_config: AgentRoleConfig,
                 mcp_manager: Optional[MCPConnectionManager] = None,
                 global_model_config: Optional[ModelConfig] = None,
                 # Backward compatibility parameters
                 **kwargs):
        """
        Initialize the base agent.

        Args:
            role_config: Configuration for the agent role
            mcp_manager: MCP connection manager for tool access
            global_model_config: Global model configuration (fallback if agent doesn't have its own)
        """
        self.role_config = role_config
        self.mcp_manager = mcp_manager
        self.global_model_config = global_model_config
        self.logger = logging.getLogger(f"{__name__}.{role_config.name}")

        # Determine effective model configuration
        self._effective_model_config = self._resolve_model_config()

        # Agent information
        self._agent_info = AgentInfo(
            name=role_config.name,
            role=role_config.name,
            capabilities=set(AgentCapability(cap) for cap in role_config.capabilities),
            model_config=self._effective_model_config,
            mcp_servers=role_config.mcp_servers,
            max_concurrent_tasks=role_config.max_concurrent_tasks,
            priority=role_config.priority
        )

        # Core components
        self._model_client: Optional[ChatCompletionClient] = None
        self._assistant_agent: Optional[AssistantAgent] = None

        # State management
        self._is_initialized = False
        self._current_tasks: Set[UUID] = set()

        # Communication
        self._message_handlers: Dict[str, Any] = {}
        self._setup_default_message_handlers()

        # Handle backward compatibility
        self._handle_backward_compatibility(kwargs)
    
    def _handle_backward_compatibility(self, kwargs: Dict[str, Any]) -> None:
        """
        Handle backward compatibility for legacy initialization parameters.

        Args:
            kwargs: Additional keyword arguments from constructor
        """
        # Log any unused kwargs for debugging
        if kwargs:
            self.logger.debug(f"Unused initialization parameters: {list(kwargs.keys())}")

    def _resolve_model_config(self) -> Optional[ModelConfig]:
        """
        Resolve the effective model configuration for this agent.

        Priority order:
        1. Agent-specific model config (role_config.model_config)
        2. Global model config
        3. None (will use default)
        """
        if self.role_config.model_configuration:
            return self.role_config.model_configuration
        elif self.global_model_config:
            return self.global_model_config
        else:
            return None

    @property
    def agent_info(self) -> AgentInfo:
        """Get agent information."""
        return self._agent_info

    @property
    def effective_model_config(self) -> Optional[ModelConfig]:
        """Get the effective model configuration for this agent."""
        return self._effective_model_config
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the agent.

        Args:
            config: Initialization configuration (for future extensibility)
        """
        if self._is_initialized:
            return
        
        self.logger.info(f"Initializing agent: {self.role_config.name}")
        
        try:
            # Initialize model client
            await self._initialize_model_client()
            
            # Initialize assistant agent
            await self._initialize_assistant_agent()
            
            # Initialize MCP tools
            await self._initialize_mcp_tools()
            
            # Update status
            self._agent_info.status = AgentStatus.IDLE
            self._is_initialized = True
            
            self.logger.info(f"Agent {self.role_config.name} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent {self.role_config.name}: {e}")
            self._agent_info.status = AgentStatus.ERROR
            raise
    
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task."""
        if not self._is_initialized:
            raise RuntimeError("Agent not initialized")
        
        if len(self._current_tasks) >= self._agent_info.max_concurrent_tasks:
            raise RuntimeError("Agent at maximum task capacity")
        
        self.logger.info(f"Executing task: {task.name}")
        start_time = datetime.now()
        
        try:
            # Update status
            self._agent_info.status = AgentStatus.BUSY
            self._agent_info.current_task = task.id
            self._current_tasks.add(task.id)
            
            # Execute the task using the assistant agent
            result = await self._execute_task_with_assistant(task)
            
            # Update metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._update_metrics(task, result, execution_time)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e),
                execution_time=execution_time
            )
            
            await self._update_metrics(task, result, execution_time)
            return result
            
        finally:
            # Clean up
            self._current_tasks.discard(task.id)
            if not self._current_tasks:
                self._agent_info.status = AgentStatus.IDLE
                self._agent_info.current_task = None
    
    async def handle_message(self, message: AgentCommunicationMessage) -> Optional[AgentCommunicationMessage]:
        """Handle an incoming message from another agent."""
        self.logger.debug(f"Received message: {message.message_type} from {message.sender_id}")
        
        handler = self._message_handlers.get(message.message_type)
        if handler:
            try:
                return await handler(message)
            except Exception as e:
                self.logger.error(f"Error handling message {message.message_type}: {e}")
        else:
            self.logger.warning(f"No handler for message type: {message.message_type}")
        
        return None
    
    async def get_capabilities(self) -> Set[AgentCapability]:
        """Get the capabilities of this agent."""
        return self._agent_info.capabilities
    
    async def get_status(self) -> AgentStatus:
        """Get the current status of the agent."""
        return self._agent_info.status
    
    async def shutdown(self) -> None:
        """Shutdown the agent gracefully."""
        self.logger.info(f"Shutting down agent: {self.role_config.name}")
        
        # Cancel any running tasks
        self._current_tasks.clear()
        
        # Update status
        self._agent_info.status = AgentStatus.OFFLINE
        self._is_initialized = False
        
        self.logger.info(f"Agent {self.role_config.name} shut down")
    
    async def _initialize_model_client(self) -> None:
        """Initialize the model client using the factory."""
        if not self._effective_model_config:
            raise ValueError("No model configuration available for agent")

        try:
            # Use the model client factory to create the appropriate client
            self._model_client = model_client_factory.create_client(self._effective_model_config)
            self.logger.info(
                f"Initialized model client for provider: {self._effective_model_config.provider}, "
                f"model: {self._effective_model_config.model}"
            )
        except Exception as e:
            self.logger.error(f"Failed to initialize model client: {e}")
            raise ValueError(f"Failed to initialize model client: {e}") from e
    
    async def _initialize_assistant_agent(self) -> None:
        """Initialize the assistant agent."""
        if not self._model_client:
            raise RuntimeError("Model client not initialized")

        # Get available tools from MCP servers
        tools = []
        if self.mcp_manager and MCP_AVAILABLE:
            tools = await self._get_autogen_mcp_tools()

        # Create assistant agent
        self._assistant_agent = AssistantAgent(
            name=self.role_config.name,
            model_client=self._model_client,
            tools=tools,
            system_message=self.role_config.system_message,
            reflect_on_tool_use=True,
            model_client_stream=True
        )
    
    async def _initialize_mcp_tools(self) -> None:
        """Initialize MCP tools for this agent."""
        if not self.mcp_manager:
            return
        
        # Verify that all required MCP servers are available
        for server_name in self.role_config.mcp_servers:
            client = await self.mcp_manager.get_client(server_name)
            if not client:
                self.logger.warning(f"MCP server {server_name} not available")
    
    async def _get_autogen_mcp_tools(self) -> List[Any]:
        """Get AutoGen-compatible tools from MCP servers using the official autogen_ext.tools.mcp module."""
        tools = []
        
        if not self.mcp_manager or not MCP_AVAILABLE:
            if not MCP_AVAILABLE:
                self.logger.warning("MCP tools not available. Install with: pip install 'autogen-ext[mcp]'")
            return tools
        
        for server_name in self.role_config.mcp_servers:
            try:
                client = await self.mcp_manager.get_client(server_name)
                if not client:
                    self.logger.warning(f"MCP server {server_name} not available")
                    continue
                
                # Get server connection parameters from the MCP manager
                # This assumes your MCP manager can provide connection parameters
                server_params = await self._get_server_params_for_client(server_name, client)
                
                if server_params and mcp_server_tools is not None:
                    # Use the official autogen_ext.tools.mcp.mcp_server_tools function
                    server_tools = await mcp_server_tools(server_params)
                    tools.extend(server_tools)
                    self.logger.info(f"Loaded {len(server_tools)} tools from MCP server {server_name}")
                else:
                    self.logger.warning(f"Could not determine server parameters for {server_name}")
                    
            except Exception as e:
                self.logger.error(f"Failed to get tools from {server_name}: {e}")
        
        return tools

    async def _get_server_params_for_client(self, server_name: str, client) -> Optional[Any]:
        """
        Get server parameters for a given MCP client.
        
        This method extracts server configuration from the MCP connection manager
        and creates the appropriate server parameters object for use with autogen_ext.tools.mcp.
        
        Args:
            server_name: Name of the MCP server
            client: MCP client instance
            
        Returns:
            Server parameters object or None if not available
        """
        if not MCP_AVAILABLE:
            return None
            
        try:
            # Get server info from the MCP connection manager
            if not self.mcp_manager or not hasattr(self.mcp_manager, '_server_infos'):
                self.logger.warning(f"MCP connection manager not properly initialized")
                return None
                
            server_info = self.mcp_manager._server_infos.get(server_name)
            if not server_info:
                self.logger.warning(f"No server info found for {server_name}")
                return None
            
            # Based on the MCPServerInfo structure, we primarily support STDIO transport
            if server_info.transport.value == "stdio" and StdioServerParams:
                return StdioServerParams(
                    command=server_info.command,
                    args=server_info.args,
                    read_timeout_seconds=float(server_info.timeout_seconds)
                )
            elif server_info.transport.value == "sse" and SseServerParams:
                # If the server uses SSE transport, we would need URL and headers
                # This is a placeholder - you would need to extend MCPServerInfo to include URL/headers
                self.logger.warning(f"SSE transport not fully implemented for server {server_name}")
                return None
            elif server_info.transport.value == "websocket" and StreamableHttpServerParams:
                # Similar to SSE, websocket would need URL configuration
                self.logger.warning(f"WebSocket transport not fully implemented for server {server_name}")
                return None
            else:
                self.logger.warning(f"Unsupported transport type {server_info.transport} for server {server_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create server params for {server_name}: {e}")
            return None

    async def _execute_task_with_assistant(self, task: Task) -> TaskResult:
        """Execute a task using the assistant agent."""
        if not self._assistant_agent:
            raise RuntimeError("Assistant agent not initialized")
        
        try:
            # Prepare the task prompt
            prompt = self._prepare_task_prompt(task)
            
            # Execute with the assistant
            response = await self._assistant_agent.run(task=prompt)
            
            # Process the response
            result_data = self._process_assistant_response(response)
            
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.COMPLETED,
                result=result_data,
                execution_time=0.0  # Will be set by caller
            )
            
        except Exception as e:
            raise RuntimeError(f"Assistant execution failed: {e}")
    
    def _prepare_task_prompt(self, task: Task) -> str:
        """Prepare a prompt for the task."""
        prompt = f"Task: {task.name}\n"
        if task.description:
            prompt += f"Description: {task.description}\n"
        
        if task.parameters:
            prompt += "Parameters:\n"
            for key, value in task.parameters.items():
                prompt += f"- {key}: {value}\n"
        
        return prompt
    
    def _process_assistant_response(self, response) -> Any:
        """Process the response from the assistant agent."""
        # Extract the actual response content
        # This will depend on the specific response format from AutoGen
        if hasattr(response, 'content'):
            return response.content
        elif hasattr(response, 'messages') and response.messages:
            return response.messages[-1].get('content', str(response))
        else:
            return str(response)
    
    async def _update_metrics(self, task: Task, result: TaskResult, execution_time: float) -> None:
        """
        Update agent metrics based on task execution.

        Args:
            task: The executed task (used for future metric enhancements)
            result: Task execution result
            execution_time: Time taken to execute the task
        """
        metrics = self._agent_info.metrics
        
        if result.status == TaskStatus.COMPLETED:
            metrics.tasks_completed += 1
        else:
            metrics.tasks_failed += 1
        
        # Update execution time metrics
        total_tasks = metrics.tasks_completed + metrics.tasks_failed
        if total_tasks > 1:
            metrics.average_execution_time = (
                (metrics.average_execution_time * (total_tasks - 1) + execution_time) / total_tasks
            )
        else:
            metrics.average_execution_time = execution_time
        
        metrics.total_execution_time += execution_time
        metrics.last_activity = datetime.now()
        
        # Update success rate
        if total_tasks > 0:
            metrics.success_rate = metrics.tasks_completed / total_tasks
    
    def _setup_default_message_handlers(self) -> None:
        """Setup default message handlers."""
        self._message_handlers.update({
            "ping": self._handle_ping_message,
            "status_request": self._handle_status_request,
            "capability_request": self._handle_capability_request
        })
    
    async def _handle_ping_message(self, message: AgentCommunicationMessage) -> AgentCommunicationMessage:
        """Handle ping message."""
        return AgentCommunicationMessage(
            sender_id=self._agent_info.id,
            recipient_id=message.sender_id,
            message_type="pong",
            content={"timestamp": datetime.now().isoformat()},
            correlation_id=message.id
        )
    
    async def _handle_status_request(self, message: AgentCommunicationMessage) -> AgentCommunicationMessage:
        """Handle status request message."""
        return AgentCommunicationMessage(
            sender_id=self._agent_info.id,
            recipient_id=message.sender_id,
            message_type="status_response",
            content={
                "status": self._agent_info.status.value,
                "current_tasks": len(self._current_tasks),
                "metrics": self._agent_info.metrics.__dict__
            },
            correlation_id=message.id
        )
    
    async def _handle_capability_request(self, message: AgentCommunicationMessage) -> AgentCommunicationMessage:
        """Handle capability request message."""
        return AgentCommunicationMessage(
            sender_id=self._agent_info.id,
            recipient_id=message.sender_id,
            message_type="capability_response",
            content={
                "capabilities": [cap.value for cap in self._agent_info.capabilities],
                "mcp_servers": self.role_config.mcp_servers
            },
            correlation_id=message.id
        )
