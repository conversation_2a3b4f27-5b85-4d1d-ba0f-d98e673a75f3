"""
Specialized agent implementations for the multi-agent framework.
"""

from .base_agent import BaseAgent as BaseAgent
from .code_analysis_agent import CodeAnalysisAgent as CodeAnalysisAgent
from .testing_agent import TestingAgent as TestingAgent
from .documentation_agent import DocumentationAgent as DocumentationAgent
from .refactoring_agent import RefactoringAgent as RefactoringAgent
from .error_detection_agent import ErrorDetectionAgent as ErrorDetectionAgent
from .optimization_agent import OptimizationAgent as OptimizationAgent
from .integrated_agent import IntegratedAgent as IntegratedAgent

__all__: list[str]
