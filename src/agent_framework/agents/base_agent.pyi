"""
Base agent implementation for the multi-agent framework.
"""

import asyncio
from typing import Any, Dict, List, Optional, Union, Callable, Awaitable
from uuid import UUID
from datetime import datetime

from ..core.types import Task, TaskResult, AgentInterface
from ..core.multi_agent_types import (
    AgentStatus, AgentCapability, AgentInfo, AgentRoleConfig,
    AgentCommunicationMessage, MultiAgentEvent
)
from ..core.config import FrameworkConfig, ModelConfig

class BaseAgent(AgentInterface):
    """
    Base agent implementation with MCP integration and common functionality.
    
    Provides core agent capabilities including:
    - Task processing
    - MCP tool integration
    - Event handling
    - Status management
    - Metrics collection
    """
    
    def __init__(self, 
                 role_config: Optional[AgentRoleConfig] = None,
                 framework_config: Optional[FrameworkConfig] = None,
                 **kwargs: Any) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the agent."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the agent and cleanup resources."""
        ...
    
    async def process_task(self, task: Task) -> TaskResult:
        """Process a task and return the result."""
        ...
    
    async def handle_message(self, message: AgentCommunicationMessage) -> Optional[AgentCommunicationMessage]:
        """Handle an incoming message from another agent."""
        ...
    
    async def get_status(self) -> AgentStatus:
        """Get the current status of the agent."""
        ...
    
    async def get_capabilities(self) -> List[AgentCapability]:
        """Get the capabilities of this agent."""
        ...
    
    async def get_load_info(self) -> Dict[str, Any]:
        """Get load information for this agent."""
        ...
    
    async def can_handle_task(self, task: Task) -> bool:
        """Check if this agent can handle a specific task."""
        ...
    
    async def get_agent_info(self) -> AgentInfo:
        """Get comprehensive information about this agent."""
        ...
    
    async def update_status(self, status: AgentStatus) -> None:
        """Update the agent's status."""
        ...
    
    async def emit_event(self, event: MultiAgentEvent) -> None:
        """Emit an event to the multi-agent system."""
        ...
    
    def add_event_handler(self, event_type: str, handler: Callable[[MultiAgentEvent], Awaitable[None]]) -> None:
        """Add an event handler."""
        ...
    
    def remove_event_handler(self, event_type: str, handler: Callable[[MultiAgentEvent], Awaitable[None]]) -> None:
        """Remove an event handler."""
        ...
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for this agent."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check of the agent."""
        ...
    
    # Properties
    @property
    def agent_id(self) -> str: ...
    
    @property
    def agent_type(self) -> str: ...
    
    @property
    def name(self) -> str: ...
    
    @property
    def description(self) -> str: ...
    
    @property
    def version(self) -> str: ...
    
    @property
    def capabilities(self) -> List[AgentCapability]: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def current_status(self) -> AgentStatus: ...
    
    @property
    def role_config(self) -> Optional[AgentRoleConfig]: ...
    
    @property
    def framework_config(self) -> Optional[FrameworkConfig]: ...
