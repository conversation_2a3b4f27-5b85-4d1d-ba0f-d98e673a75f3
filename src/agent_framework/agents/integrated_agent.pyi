"""
Enhanced agent with integrated shared infrastructure.
"""

from typing import Any, Dict, List, Optional, TypeVar
from ..core.types import Task, TaskResult
from ..core.multi_agent_types import AgentRoleConfig, AgentCapability
from ..shared.base_processor import BaseProcessor
from ..shared.data_models import UnifiedTask
from .base_agent import BaseAgent

T = TypeVar('T')

class IntegratedAgent(BaseAgent, BaseProcessor[UnifiedTask, Any]):
    """
    Enhanced agent with integrated shared infrastructure.
    
    Combines the base agent functionality with shared processing patterns,
    caching, and improved communication capabilities.
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs: Any) -> None: ...
    
    async def process_unified_task(self, task: UnifiedTask) -> Dict[str, Any]:
        """Process a unified task using shared infrastructure."""
        ...
    
    async def process_with_caching(self, task: UnifiedTask) -> Dict[str, Any]:
        """Process task with caching support."""
        ...
    
    async def process_with_validation(self, task: UnifiedTask) -> Dict[str, Any]:
        """Process task with comprehensive validation."""
        ...
    
    async def get_processing_metrics(self) -> Dict[str, Any]:
        """Get processing metrics from shared infrastructure."""
        ...
    
    async def clear_cache(self) -> None:
        """Clear the agent's cache."""
        ...
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for integrated agent."""
        ...
