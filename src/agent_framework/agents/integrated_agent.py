"""
Integrated agent base class with shared infrastructure.

This module provides an enhanced base agent that integrates with
the shared infrastructure for better component coupling.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Set
from datetime import datetime

from .base_agent import BaseAgent
from ..shared.base_processor import BaseProcessor, ProcessingContext
from ..shared.base_cache import BaseCache
from ..shared.data_models import (
    UnifiedTask, StandardResult, CapabilityModel, ContextModel,
    TaskStatus, CapabilityType
)
from ..communication.enhanced_broker import Enhanced<PERSON><PERSON>ageBroker, TypedEvent
from ..plugins.manager import PluginManager
from ..context.manager import ContextManager
from ..utils.code_analysis_utils import analyze_code_quality, calculate_complexity
from ..utils.validation_utils import validate_python_syntax
from ..core.multi_agent_types import AgentCapability


class IntegratedAgent(BaseAgent, BaseProcessor[UnifiedTask, Any]):
    """
    Enhanced agent with integrated shared infrastructure.
    
    Combines the base agent functionality with shared processing patterns,
    caching, and improved communication capabilities.
    """
    
    def __init__(self, agent_id: str, capabilities: List[CapabilityModel],
                 message_broker: Optional[EnhancedMessageBroker] = None,
                 plugin_manager: Optional[PluginManager] = None,
                 context_manager: Optional[ContextManager] = None,
                 cache: Optional[BaseCache] = None):
        """
        Initialize the integrated agent.
        
        Args:
            agent_id: Unique agent identifier
            capabilities: List of agent capabilities
            message_broker: Enhanced message broker for communication
            plugin_manager: Plugin manager for accessing plugins
            context_manager: Context manager for shared state
            cache: Cache for performance optimization
        """
        # Initialize base classes
        from ..core.config import AgentRoleConfig, ModelConfig, ModelProvider
        model_config = ModelConfig(
            provider=ModelProvider.OPENAI,
            model_name="gpt-3.5-turbo",
            api_key="test-key"
        )
        role_config = AgentRoleConfig(
            name=agent_id,
            description=f"Integrated agent {agent_id}",
            system_message=f"You are {agent_id}, an integrated agent with capabilities: {[cap.name for cap in capabilities]}",
            capabilities=[cap.name for cap in capabilities],
            model_configuration=model_config
        )
        BaseAgent.__init__(self, role_config)
        BaseProcessor.__init__(self, agent_id)
        
        self.agent_id = agent_id
        self.capabilities = capabilities
        
        # Shared infrastructure
        self.message_broker = message_broker
        self.plugin_manager = plugin_manager
        self.context_manager = context_manager
        self.cache = cache
        
        # Agent state
        self._is_busy = False
        self._current_task: Optional[UnifiedTask] = None
        self._task_history: List[UnifiedTask] = []
        
        # Performance tracking
        self._tasks_completed = 0
        self._tasks_failed = 0
        self._average_execution_time = 0.0
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the integrated agent."""
        await super().initialize(config)
        
        # Subscribe to relevant events if message broker is available
        if self.message_broker:
            await self._setup_event_subscriptions()
        
        self.logger.info(f"Integrated agent {self.agent_id} initialized")
    
    async def _setup_event_subscriptions(self) -> None:
        """Set up event subscriptions for agent communication."""
        if not self.message_broker:
            return
        
        # Subscribe to task assignment events
        self.message_broker.subscribe(
            event_type="task_assignment",
            handler=self._handle_task_assignment,
            subscriber_id=self.agent_id,
            filter_func=lambda event: event.target == self.agent_id
        )
        
        # Subscribe to capability queries
        self.message_broker.subscribe(
            event_type="capability_query",
            handler=self._handle_capability_query,
            subscriber_id=self.agent_id
        )
        
        # Subscribe to collaboration requests
        self.message_broker.subscribe(
            event_type="collaboration_request",
            handler=self._handle_collaboration_request,
            subscriber_id=self.agent_id,
            filter_func=lambda event: self.agent_id in event.payload.get("target_agents", [])
        )
    
    async def _validate_input(self, task: UnifiedTask, context: ProcessingContext) -> bool:
        """Validate task input."""
        # Basic task validation
        if not task.name or not task.task_type:
            return False
        
        # Check if agent has required capabilities
        required_capability = self._get_required_capability(task.task_type)
        if required_capability and not self._has_capability(required_capability):
            return False
        
        # Validate task parameters based on type
        if task.task_type == "code_analysis" and "code" not in task.parameters:
            return False
        
        return True
    
    async def _process_data(self, task: UnifiedTask, context: ProcessingContext) -> Any:
        """Process the task using integrated infrastructure."""
        self._current_task = task
        self._is_busy = True
        
        try:
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # Check cache for previous results
            cache_key = self._generate_cache_key(task)
            if self.cache:
                cached_result = await self.cache.get(cache_key)
                if cached_result:
                    self.logger.debug(f"Using cached result for task {task.id}")
                    return cached_result
            
            # Get relevant context
            context_data = await self._get_task_context(task)
            context.add_metadata("agent_context", context_data)
            
            # Process task based on type
            result = await self._execute_task_by_type(task, context)
            
            # Cache the result
            if self.cache and result:
                await self.cache.set(cache_key, result, ttl=3600)  # 1 hour TTL
            
            # Update task status
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            
            # Update metrics
            self._tasks_completed += 1
            execution_time = task.duration_seconds or 0
            self._average_execution_time = (
                (self._average_execution_time * (self._tasks_completed - 1) + execution_time) /
                self._tasks_completed
            )
            
            # Publish completion event
            if self.message_broker:
                await self._publish_task_completion(task, result)
            
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            self._tasks_failed += 1
            
            # Publish failure event
            if self.message_broker:
                await self._publish_task_failure(task, str(e))
            
            raise
        
        finally:
            self._current_task = None
            self._is_busy = False
            self._task_history.append(task)
            
            # Trim history
            if len(self._task_history) > 100:
                self._task_history = self._task_history[-100:]
    
    async def _handle_error(self, error: Exception, task: UnifiedTask, 
                          context: ProcessingContext) -> Optional[Any]:
        """Handle processing errors with recovery attempts."""
        self.logger.error(f"Error processing task {task.id}: {error}")
        
        # Try to recover using plugins
        if self.plugin_manager and task.can_retry():
            try:
                # Attempt recovery using error detection plugin
                recovery_result = await self._attempt_error_recovery(task, error)
                if recovery_result:
                    return recovery_result
            except Exception as recovery_error:
                self.logger.error(f"Error recovery failed: {recovery_error}")
        
        return None
    
    async def _execute_task_by_type(self, task: UnifiedTask, context: ProcessingContext) -> Any:
        """Execute task based on its type."""
        task_type = task.task_type
        
        if task_type == "code_analysis":
            return await self._execute_code_analysis(task)
        elif task_type == "code_generation":
            return await self._execute_code_generation(task)
        elif task_type == "testing":
            return await self._execute_testing(task)
        elif task_type == "documentation":
            return await self._execute_documentation(task)
        elif task_type == "refactoring":
            return await self._execute_refactoring(task)
        elif task_type == "error_detection":
            return await self._execute_error_detection(task)
        else:
            # Try to delegate to appropriate plugin
            return await self._delegate_to_plugin(task)
    
    async def _execute_code_analysis(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute code analysis task."""
        code = task.parameters.get("code", "")
        
        # Use shared utilities
        syntax_result = validate_python_syntax(code)
        quality_metrics = analyze_code_quality(code)
        complexity_result = calculate_complexity(code)
        
        # Use plugin if available
        plugin_result = None
        if self.plugin_manager:
            try:
                plugin_result = await self._use_code_analysis_plugin(code)
            except Exception as e:
                self.logger.warning(f"Plugin analysis failed: {e}")
        
        return {
            "syntax_validation": syntax_result,
            "quality_metrics": quality_metrics.__dict__,
            "complexity_analysis": complexity_result,
            "plugin_analysis": plugin_result,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _execute_code_generation(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute code generation task."""
        # Delegate to code generation plugin
        if self.plugin_manager:
            return await self._use_code_generation_plugin(task.parameters)
        else:
            return {"error": "Code generation plugin not available"}
    
    async def _execute_testing(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute testing task."""
        # Use testing agent capabilities or delegate to plugin
        if self.plugin_manager:
            return await self._use_testing_plugin(task.parameters)
        else:
            return {"error": "Testing plugin not available"}
    
    async def _execute_documentation(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute documentation task."""
        # Delegate to documentation plugin
        if self.plugin_manager:
            return await self._use_documentation_plugin(task.parameters)
        else:
            return {"error": "Documentation plugin not available"}
    
    async def _execute_refactoring(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute refactoring task."""
        # Delegate to refactoring plugin
        if self.plugin_manager:
            return await self._use_refactoring_plugin(task.parameters)
        else:
            return {"error": "Refactoring plugin not available"}
    
    async def _execute_error_detection(self, task: UnifiedTask) -> Dict[str, Any]:
        """Execute error detection task."""
        # Delegate to error detection plugin
        if self.plugin_manager:
            return await self._use_error_detection_plugin(task.parameters)
        else:
            return {"error": "Error detection plugin not available"}
    
    async def _delegate_to_plugin(self, task: UnifiedTask) -> Any:
        """Delegate task to appropriate plugin."""
        if not self.plugin_manager:
            raise ValueError(f"No plugin manager available for task type: {task.task_type}")

        try:
            # Get all available plugins
            available_plugins = self.plugin_manager.registry.get_enabled_plugins()

            if not available_plugins:
                self.logger.warning("No plugins available for delegation")
                return {"error": "No plugins available", "task_type": task.task_type}

            # Find plugins that can handle this task type
            suitable_plugins = await self._find_suitable_plugins(task, available_plugins)

            if not suitable_plugins:
                self.logger.warning(f"No suitable plugins found for task type: {task.task_type}")
                return {"error": f"No suitable plugins for task type: {task.task_type}"}

            # Select the best plugin (highest capability score)
            best_plugin = max(suitable_plugins, key=lambda x: x['score'])
            plugin_name = best_plugin['name']

            self.logger.info(f"Delegating {task.task_type} task to plugin: {plugin_name}")

            # Create plugin request
            from ..core.types import PluginRequest
            plugin_request = PluginRequest(
                capability=task.task_type,
                parameters=task.parameters,
                context=task.context,
                timeout_seconds=30
            )

            # Execute the plugin request
            response = await self.plugin_manager.execute_plugin_request(plugin_name, plugin_request)

            if response.success:
                self.logger.info(f"Plugin {plugin_name} completed task successfully")
                return response.data
            else:
                self.logger.error(f"Plugin {plugin_name} failed: {response.error}")
                return {"error": response.error, "plugin": plugin_name}

        except Exception as e:
            self.logger.error(f"Plugin delegation failed: {e}")
            return {"error": str(e), "task_type": task.task_type}

    async def _find_suitable_plugins(self, task: UnifiedTask, available_plugins: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find plugins suitable for handling the given task."""
        suitable_plugins = []

        for plugin_name, plugin_metadata in available_plugins.items():
            try:
                # Get plugin capabilities
                capabilities = await self.plugin_manager.get_plugin_capabilities(plugin_name)

                # Calculate suitability score
                score = self._calculate_plugin_suitability(task, capabilities)

                if score > 0:
                    suitable_plugins.append({
                        'name': plugin_name,
                        'score': score,
                        'capabilities': [cap.name for cap in capabilities],
                        'metadata': plugin_metadata
                    })

            except Exception as e:
                self.logger.warning(f"Error evaluating plugin {plugin_name}: {e}")
                continue

        return suitable_plugins

    def _calculate_plugin_suitability(self, task: UnifiedTask, capabilities: List[Any]) -> float:
        """Calculate how suitable a plugin is for a given task."""
        score = 0.0
        task_type = task.task_type.lower()

        for capability in capabilities:
            capability_name = capability.name.lower() if hasattr(capability, 'name') else str(capability).lower()

            # Exact match gets highest score
            if capability_name == task_type:
                score += 10.0
            # Partial match gets medium score
            elif task_type in capability_name or capability_name in task_type:
                score += 5.0
            # Related capabilities get lower score
            elif self._are_capabilities_related(task_type, capability_name):
                score += 2.0

        return score

    def _are_capabilities_related(self, task_type: str, capability_name: str) -> bool:
        """Check if a task type and capability are related."""
        # Define related capability groups
        related_groups = {
            'code_analysis': ['analysis', 'quality', 'complexity', 'patterns'],
            'code_generation': ['generation', 'create', 'build', 'template'],
            'optimization': ['optimize', 'improve', 'performance', 'efficiency'],
            'documentation': ['document', 'docs', 'comment', 'readme'],
            'testing': ['test', 'unit', 'integration', 'coverage'],
            'debugging': ['debug', 'error', 'fix', 'troubleshoot']
        }

        for group_key, related_terms in related_groups.items():
            if task_type in group_key or group_key in task_type:
                return any(term in capability_name for term in related_terms)
            if capability_name in group_key or group_key in capability_name:
                return any(term in task_type for term in related_terms)

        return False

    async def _get_task_context(self, task: UnifiedTask) -> Dict[str, Any]:
        """Get relevant context for the task."""
        if not self.context_manager:
            return {}
        
        # Get context based on task type and parameters
        context_data = {}
        
        # Add session context if available
        if task.context.get("session_id"):
            session_context = await self.context_manager.get_context({
                "query_type": "session",
                "scope": task.context["session_id"]
            })
            context_data["session"] = session_context
        
        return context_data
    
    def _generate_cache_key(self, task: UnifiedTask) -> str:
        """Generate cache key for task."""
        if self.cache:
            return self.cache.generate_key(
                task.task_type,
                task.parameters,
                task.requirements
            )
        return ""
    
    def _get_required_capability(self, task_type: str) -> Optional[CapabilityType]:
        """Get required capability for task type."""
        capability_mapping = {
            "code_analysis": CapabilityType.CODE_ANALYSIS,
            "code_generation": CapabilityType.CODE_GENERATION,
            "testing": CapabilityType.TESTING,
            "documentation": CapabilityType.DOCUMENTATION,
            "refactoring": CapabilityType.REFACTORING,
            "error_detection": CapabilityType.ERROR_DETECTION,
            "optimization": CapabilityType.OPTIMIZATION
        }
        return capability_mapping.get(task_type)
    
    def _has_capability(self, capability_type: CapabilityType) -> bool:
        """Check if agent has a specific capability."""
        return any(cap.capability_type == capability_type for cap in self.capabilities)
    
    async def _handle_task_assignment(self, event: TypedEvent) -> None:
        """Handle task assignment event."""
        task_data = event.payload
        task = UnifiedTask(**task_data)
        
        try:
            result = await self.process(task)
            
            # Send response if this was a request
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.success_result(result)
                )
        except Exception as e:
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.error_result(str(e))
                )
    
    async def _handle_capability_query(self, event: TypedEvent) -> None:
        """Handle capability query event."""
        # Respond with agent capabilities
        if self.message_broker:
            response_event = TypedEvent(
                event_type="capability_response",
                payload={
                    "agent_id": self.agent_id,
                    "capabilities": [cap.__dict__ for cap in self.capabilities],
                    "is_busy": self._is_busy,
                    "performance_metrics": {
                        "tasks_completed": self._tasks_completed,
                        "tasks_failed": self._tasks_failed,
                        "average_execution_time": self._average_execution_time
                    }
                },
                source=self.agent_id,
                correlation_id=event.correlation_id
            )
            await self.message_broker.publish_event(response_event)
    
    async def _handle_collaboration_request(self, event: TypedEvent) -> None:
        """Handle collaboration request from other agents."""
        # This would implement agent-to-agent collaboration
        # For now, just log the request
        self.logger.info(f"Received collaboration request: {event.payload}")
    
    async def _publish_task_completion(self, task: UnifiedTask, result: Any) -> None:
        """Publish task completion event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="task_completed",
                payload={
                    "task_id": task.id,
                    "agent_id": self.agent_id,
                    "result": result,
                    "execution_time": task.duration_seconds
                },
                source=self.agent_id
            )
            await self.message_broker.publish_event(event)
    
    async def _publish_task_failure(self, task: UnifiedTask, error: str) -> None:
        """Publish task failure event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="task_failed",
                payload={
                    "task_id": task.id,
                    "agent_id": self.agent_id,
                    "error": error,
                    "execution_time": task.duration_seconds
                },
                source=self.agent_id
            )
            await self.message_broker.publish_event(event)
    
    # Plugin integration methods (simplified implementations)
    
    async def _use_code_analysis_plugin(self, code: str) -> Dict[str, Any]:
        """Use code analysis plugin."""
        # This would use the actual plugin manager
        return {"plugin_result": "Code analysis completed"}
    
    async def _use_code_generation_plugin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Use code generation plugin."""
        return {"generated_code": "# Generated code placeholder"}
    
    async def _use_testing_plugin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Use testing plugin."""
        return {"test_results": "Tests generated and executed"}
    
    async def _use_documentation_plugin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Use documentation plugin."""
        return {"documentation": "Documentation generated"}
    
    async def _use_refactoring_plugin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Use refactoring plugin."""
        return {"refactored_code": "# Refactored code placeholder"}
    
    async def _use_error_detection_plugin(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Use error detection plugin."""
        return {"errors_detected": []}
    
    async def _attempt_error_recovery(self, task: UnifiedTask, error: Exception) -> Optional[Any]:
        """Attempt to recover from error using plugins."""
        # This would implement error recovery logic
        return None
