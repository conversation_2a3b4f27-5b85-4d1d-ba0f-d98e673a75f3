"""
Base service class providing common service lifecycle and configuration patterns.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
from datetime import datetime

from .base_metrics import BaseMetrics
from .data_models import MetricsSnapshot


class ServiceState:
    """Service state enumeration."""
    UNINITIALIZED = "uninitialized"
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


class BaseService(ABC):
    """
    Base class for all framework services.
    
    Provides common lifecycle management, configuration handling,
    metrics collection, and error handling patterns.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the base service.
        
        Args:
            name: Service name for logging and identification
            config: Service configuration dictionary
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Service state
        self.state = ServiceState.UNINITIALIZED
        self.started_at: Optional[datetime] = None
        self.error_count = 0
        self.last_error: Optional[Exception] = None
        
        # Metrics
        self.metrics = BaseMetrics(f"{name}_service")
        
        # Lifecycle management
        self._shutdown_event = asyncio.Event()
        self._background_tasks: set = set()
    
    @abstractmethod
    async def _initialize_service(self) -> None:
        """
        Service-specific initialization logic.
        
        Subclasses must implement this method to perform
        their specific initialization tasks.
        """
        pass
    
    @abstractmethod
    async def _shutdown_service(self) -> None:
        """
        Service-specific shutdown logic.
        
        Subclasses must implement this method to perform
        their specific cleanup tasks.
        """
        pass
    
    async def initialize(self) -> None:
        """Initialize the service."""
        if self.state != ServiceState.UNINITIALIZED:
            self.logger.warning(f"Service {self.name} already initialized")
            return
        
        self.logger.info(f"Initializing service: {self.name}")
        self.state = ServiceState.INITIALIZING
        
        try:
            # Record start time
            self.started_at = datetime.now()
            
            # Initialize metrics
            await self.metrics.initialize()
            
            # Service-specific initialization
            await self._initialize_service()
            
            # Update state
            self.state = ServiceState.RUNNING
            self.logger.info(f"Service {self.name} initialized successfully")
            
            # Record initialization metric
            self.metrics.record_counter("service_initializations", 1.0)
            
        except Exception as e:
            self.state = ServiceState.ERROR
            self.last_error = e
            self.error_count += 1
            self.logger.error(f"Failed to initialize service {self.name}: {e}")
            raise
    
    async def shutdown(self) -> None:
        """Shutdown the service gracefully."""
        if self.state in [ServiceState.STOPPED, ServiceState.STOPPING]:
            self.logger.warning(f"Service {self.name} already stopped or stopping")
            return
        
        self.logger.info(f"Shutting down service: {self.name}")
        self.state = ServiceState.STOPPING
        
        try:
            # Signal shutdown to background tasks
            self._shutdown_event.set()
            
            # Wait for background tasks to complete
            if self._background_tasks:
                self.logger.info(f"Waiting for {len(self._background_tasks)} background tasks")
                await asyncio.gather(*self._background_tasks, return_exceptions=True)
            
            # Service-specific shutdown
            await self._shutdown_service()
            
            # Shutdown metrics
            await self.metrics.shutdown()
            
            # Update state
            self.state = ServiceState.STOPPED
            self.logger.info(f"Service {self.name} shut down successfully")
            
        except Exception as e:
            self.state = ServiceState.ERROR
            self.last_error = e
            self.error_count += 1
            self.logger.error(f"Error during shutdown of service {self.name}: {e}")
            raise
    
    def is_running(self) -> bool:
        """Check if the service is running."""
        return self.state == ServiceState.RUNNING
    
    def is_healthy(self) -> bool:
        """Check if the service is healthy."""
        return self.state == ServiceState.RUNNING and self.error_count == 0
    
    async def get_metrics(self) -> MetricsSnapshot:
        """Get current service metrics."""
        base_metrics = await self.metrics.get_snapshot()
        
        # Add service-specific metrics
        service_metrics = {
            "service_name": self.name,
            "service_state": self.state,
            "uptime_seconds": (
                (datetime.now() - self.started_at).total_seconds()
                if self.started_at else 0
            ),
            "error_count": self.error_count,
            "last_error": str(self.last_error) if self.last_error else None
        }
        
        return MetricsSnapshot(
            timestamp=datetime.now(),
            metrics={**base_metrics.metrics, **service_metrics}
        )
    
    def create_background_task(self, coro) -> asyncio.Task:
        """
        Create a background task that will be properly managed.
        
        Args:
            coro: Coroutine to run as background task
            
        Returns:
            Created task
        """
        task = asyncio.create_task(coro)
        self._background_tasks.add(task)
        task.add_done_callback(self._background_tasks.discard)
        return task
    
    async def wait_for_shutdown(self) -> None:
        """Wait for shutdown signal."""
        await self._shutdown_event.wait()
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """
        Update service configuration.
        
        Args:
            new_config: New configuration dictionary
        """
        self.config.update(new_config)
        self.logger.info(f"Configuration updated for service {self.name}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        return self.config.get(key, default)
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check.
        
        Returns:
            Health check results
        """
        return {
            "service": self.name,
            "state": self.state,
            "healthy": self.is_healthy(),
            "uptime_seconds": (
                (datetime.now() - self.started_at).total_seconds()
                if self.started_at else 0
            ),
            "error_count": self.error_count,
            "last_error": str(self.last_error) if self.last_error else None
        }
