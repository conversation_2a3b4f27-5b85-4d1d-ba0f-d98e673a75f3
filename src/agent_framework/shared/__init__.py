"""
Shared abstractions and utilities for the agent framework.

This module provides common base classes, interfaces, and utilities
that are used across multiple components of the framework.
"""

from .base_service import BaseService
from .base_processor import BaseProcessor
from .base_validator import BaseValidator
from .base_metrics import BaseMetrics
from .base_cache import BaseCache
from .data_models import (
    UnifiedTask, StandardResult, CapabilityModel, 
    ContextModel, MetricsSnapshot, UnifiedRequest
)

__all__ = [
    "BaseService",
    "BaseProcessor", 
    "BaseValidator",
    "BaseMetrics",
    "BaseCache",
    "UnifiedTask",
    "StandardResult",
    "CapabilityModel",
    "ContextModel",
    "MetricsSnapshot",
    "UnifiedRequest"
]
