"""
Shared abstractions and utilities for the agent framework.
"""

from .base_service import BaseService as BaseService
from .base_processor import BaseProcessor as BaseProcessor
from .base_validator import BaseValidator as BaseValidator
from .base_metrics import BaseMetrics as BaseMetrics
from .base_cache import BaseCache as BaseCache
from .data_models import (
    UnifiedTask as UnifiedTask,
    StandardResult as StandardResult,
    CapabilityModel as CapabilityModel,
    ContextModel as ContextModel,
    MetricsSnapshot as MetricsSnapshot,
    UnifiedRequest as UnifiedRequest
)

__all__: list[str]
