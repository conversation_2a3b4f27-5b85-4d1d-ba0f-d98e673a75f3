"""
Shared error handling framework to eliminate duplication.

Provides common error handling patterns, exception hierarchies,
and error recovery mechanisms used across the framework.
"""

import logging
import traceback
import functools
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import asyncio


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories."""
    VALIDATION = "validation"
    PROCESSING = "processing"
    NETWORK = "network"
    SECURITY = "security"
    CONFIGURATION = "configuration"
    RESOURCE = "resource"
    EXTERNAL = "external"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for errors."""
    component: str
    operation: str
    user_id: Optional[str] = None
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ErrorInfo:
    """Comprehensive error information."""
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    traceback_str: Optional[str] = None
    suggested_action: Optional[str] = None
    recovery_possible: bool = True
    error_code: Optional[str] = None


class AgentFrameworkError(Exception):
    """Base exception for the agent framework."""
    
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 category: ErrorCategory = ErrorCategory.UNKNOWN,
                 context: Optional[ErrorContext] = None,
                 suggested_action: Optional[str] = None,
                 error_code: Optional[str] = None):
        """Initialize the error."""
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.category = category
        self.context = context or ErrorContext("unknown", "unknown")
        self.suggested_action = suggested_action
        self.error_code = error_code
        self.timestamp = datetime.now()
    
    def to_error_info(self) -> ErrorInfo:
        """Convert to ErrorInfo object."""
        return ErrorInfo(
            error_type=self.__class__.__name__,
            message=self.message,
            severity=self.severity,
            category=self.category,
            context=self.context,
            traceback_str=traceback.format_exc(),
            suggested_action=self.suggested_action,
            error_code=self.error_code
        )


class ValidationError(AgentFrameworkError):
    """Error in input validation."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, 
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.VALIDATION,
            **kwargs
        )


class ProcessingError(AgentFrameworkError):
    """Error in processing operations."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.PROCESSING,
            **kwargs
        )


class NetworkError(AgentFrameworkError):
    """Error in network operations."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.NETWORK,
            **kwargs
        )


class SecurityError(AgentFrameworkError):
    """Security-related error."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.SECURITY,
            **kwargs
        )


class ConfigurationError(AgentFrameworkError):
    """Configuration-related error."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.CONFIGURATION,
            **kwargs
        )


class ResourceError(AgentFrameworkError):
    """Resource-related error (memory, disk, etc.)."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.RESOURCE,
            **kwargs
        )


class ExternalServiceError(AgentFrameworkError):
    """Error from external services."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.EXTERNAL,
            **kwargs
        )


class ErrorHandler:
    """
    Centralized error handling system.
    
    Provides consistent error handling, logging, and recovery
    mechanisms across the framework.
    """
    
    def __init__(self, component_name: str):
        """Initialize the error handler."""
        self.component_name = component_name
        self.logger = logging.getLogger(f"{__name__}.{component_name}")
        self._error_callbacks: List[Callable[[ErrorInfo], None]] = []
        self._recovery_strategies: Dict[Type[Exception], Callable] = {}
    
    def add_error_callback(self, callback: Callable[[ErrorInfo], None]) -> None:
        """Add an error callback."""
        self._error_callbacks.append(callback)
    
    def register_recovery_strategy(self, 
                                  exception_type: Type[Exception], 
                                  strategy: Callable) -> None:
        """Register a recovery strategy for an exception type."""
        self._recovery_strategies[exception_type] = strategy
    
    def handle_error(self, 
                    error: Exception, 
                    context: Optional[ErrorContext] = None,
                    suppress: bool = False) -> Optional[ErrorInfo]:
        """
        Handle an error with logging and callbacks.
        
        Args:
            error: The exception to handle
            context: Error context information
            suppress: Whether to suppress re-raising the exception
            
        Returns:
            ErrorInfo object if error was handled
        """
        # Create error info
        if isinstance(error, AgentFrameworkError):
            error_info = error.to_error_info()
            if context:
                error_info.context = context
        else:
            error_info = ErrorInfo(
                error_type=error.__class__.__name__,
                message=str(error),
                severity=ErrorSeverity.HIGH,
                category=self._categorize_error(error),
                context=context or ErrorContext(self.component_name, "unknown"),
                traceback_str=traceback.format_exc()
            )
        
        # Log error
        self._log_error(error_info)
        
        # Call error callbacks
        for callback in self._error_callbacks:
            try:
                callback(error_info)
            except Exception as callback_error:
                self.logger.error(f"Error callback failed: {callback_error}")
        
        # Attempt recovery
        recovery_result = self._attempt_recovery(error, error_info)
        if recovery_result:
            error_info.recovery_possible = True
            error_info.suggested_action = "Recovery attempted"
        
        if not suppress:
            raise error
        
        return error_info
    
    def _categorize_error(self, error: Exception) -> ErrorCategory:
        """Categorize an error based on its type."""
        error_type = type(error)
        
        # Network errors
        if any(name in error_type.__name__.lower() for name in ['network', 'connection', 'timeout', 'http']):
            return ErrorCategory.NETWORK
        
        # Validation errors
        if any(name in error_type.__name__.lower() for name in ['validation', 'value', 'type']):
            return ErrorCategory.VALIDATION
        
        # Resource errors
        if any(name in error_type.__name__.lower() for name in ['memory', 'disk', 'resource']):
            return ErrorCategory.RESOURCE
        
        # Security errors
        if any(name in error_type.__name__.lower() for name in ['security', 'permission', 'auth']):
            return ErrorCategory.SECURITY
        
        return ErrorCategory.UNKNOWN
    
    def _log_error(self, error_info: ErrorInfo) -> None:
        """Log error information."""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.ERROR)
        
        log_message = (
            f"[{error_info.category.value.upper()}] {error_info.error_type}: {error_info.message}"
        )
        
        extra_info = {
            'error_code': error_info.error_code,
            'component': error_info.context.component,
            'operation': error_info.context.operation,
            'user_id': error_info.context.user_id,
            'correlation_id': error_info.context.correlation_id
        }
        
        self.logger.log(log_level, log_message, extra=extra_info)
        
        if error_info.traceback_str and error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.debug(f"Traceback: {error_info.traceback_str}")
    
    def _attempt_recovery(self, error: Exception, error_info: ErrorInfo) -> bool:
        """Attempt to recover from an error."""
        error_type = type(error)
        
        # Check for specific recovery strategy
        if error_type in self._recovery_strategies:
            try:
                self._recovery_strategies[error_type](error, error_info)
                return True
            except Exception as recovery_error:
                self.logger.error(f"Recovery strategy failed: {recovery_error}")
        
        # Check for parent class recovery strategies
        for registered_type, strategy in self._recovery_strategies.items():
            if issubclass(error_type, registered_type):
                try:
                    strategy(error, error_info)
                    return True
                except Exception as recovery_error:
                    self.logger.error(f"Recovery strategy failed: {recovery_error}")
        
        return False


def error_handler(component: str, 
                 operation: str = "unknown",
                 suppress: bool = False,
                 recovery_strategy: Optional[Callable] = None):
    """
    Decorator for automatic error handling.
    
    Args:
        component: Component name for context
        operation: Operation name for context
        suppress: Whether to suppress exceptions
        recovery_strategy: Optional recovery function
    """
    def decorator(func):
        handler = ErrorHandler(component)
        
        if recovery_strategy:
            handler.register_recovery_strategy(Exception, recovery_strategy)
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                context = ErrorContext(component, operation)
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    return handler.handle_error(e, context, suppress)
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                context = ErrorContext(component, operation)
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    return handler.handle_error(e, context, suppress)
            return sync_wrapper
    
    return decorator


def safe_execute(func: Callable, 
                *args, 
                default_return: Any = None,
                component: str = "unknown",
                operation: str = "unknown",
                **kwargs) -> Any:
    """
    Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Default return value on error
        component: Component name for context
        operation: Operation name for context
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    handler = ErrorHandler(component)
    context = ErrorContext(component, operation)
    
    try:
        if asyncio.iscoroutinefunction(func):
            # For async functions, return a coroutine
            async def async_safe_execute():
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    handler.handle_error(e, context, suppress=True)
                    return default_return
            return async_safe_execute()
        else:
            return func(*args, **kwargs)
    except Exception as e:
        handler.handle_error(e, context, suppress=True)
        return default_return


# Global error handler instance
global_error_handler = ErrorHandler("global")
