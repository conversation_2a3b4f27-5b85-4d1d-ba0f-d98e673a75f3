"""
Unified data models for the agent framework.

This module provides standardized data structures used across
all components of the framework.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Generic, TypeVar
from dataclasses import dataclass, field
from enum import Enum

# Type variables for generic models
T = TypeVar('T')
R = TypeVar('R')


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CapabilityType(Enum):
    """Types of capabilities."""
    CODE_ANALYSIS = "code_analysis"
    CODE_GENERATION = "code_generation"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    REFACTORING = "refactoring"
    ERROR_DETECTION = "error_detection"
    OPTIMIZATION = "optimization"
    FILE_OPERATIONS = "file_operations"
    WEB_SEARCH = "web_search"
    DATABASE_OPERATIONS = "database_operations"


@dataclass
class UnifiedTask:
    """
    Unified task model used across all components.
    
    Provides a standardized way to represent tasks with
    comprehensive metadata and tracking capabilities.
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    task_type: str = ""
    priority: TaskPriority = TaskPriority.NORMAL
    status: TaskStatus = TaskStatus.PENDING
    
    # Task data
    parameters: Dict[str, Any] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    requirements: List[str] = field(default_factory=list)
    
    # Execution metadata
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    timeout_seconds: Optional[int] = None
    
    # Tracking
    assigned_to: Optional[str] = None  # Agent or component ID
    parent_task_id: Optional[str] = None
    subtask_ids: List[str] = field(default_factory=list)
    
    # Dependencies
    depends_on: List[str] = field(default_factory=list)
    blocks: List[str] = field(default_factory=list)
    
    # Execution history
    execution_attempts: int = 0
    max_retries: int = 3
    retry_delay_seconds: int = 1
    
    # Tags and labels
    tags: List[str] = field(default_factory=list)
    labels: Dict[str, str] = field(default_factory=dict)
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the task."""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def add_label(self, key: str, value: str) -> None:
        """Add a label to the task."""
        self.labels[key] = value
    
    def is_expired(self) -> bool:
        """Check if the task has expired."""
        if self.timeout_seconds is None or self.started_at is None:
            return False
        
        elapsed = (datetime.now() - self.started_at).total_seconds()
        return elapsed > self.timeout_seconds
    
    def can_retry(self) -> bool:
        """Check if the task can be retried."""
        return self.execution_attempts < self.max_retries
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get task duration in seconds."""
        if self.started_at is None:
            return None
        
        end_time = self.completed_at or datetime.now()
        return (end_time - self.started_at).total_seconds()


@dataclass
class StandardResult(Generic[T]):
    """
    Standardized result format used across all components.
    
    Provides consistent structure for operation results with
    comprehensive error handling and metadata.
    """
    success: bool
    result: Optional[T] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Execution metadata
    execution_time: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    warnings: List[str] = field(default_factory=list)
    
    # Tracing information
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    
    def add_warning(self, warning: str) -> None:
        """Add a warning to the result."""
        self.warnings.append(warning)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the result."""
        self.metadata[key] = value
    
    @classmethod
    def success_result(cls, result: T, execution_time: Optional[float] = None, 
                      metadata: Optional[Dict[str, Any]] = None) -> 'StandardResult[T]':
        """Create a successful result."""
        return cls(
            success=True,
            result=result,
            execution_time=execution_time,
            metadata=metadata or {}
        )
    
    @classmethod
    def error_result(cls, error: str, error_code: Optional[str] = None,
                    error_details: Optional[Dict[str, Any]] = None,
                    execution_time: Optional[float] = None) -> 'StandardResult[T]':
        """Create an error result."""
        return cls(
            success=False,
            error=error,
            error_code=error_code,
            error_details=error_details,
            execution_time=execution_time
        )


@dataclass
class CapabilityModel:
    """
    Standardized capability representation.
    
    Describes what a component (agent, plugin, etc.) can do.
    """
    name: str
    capability_type: CapabilityType
    description: str
    version: str = "1.0.0"
    
    # Input/output specifications
    input_schema: Optional[Dict[str, Any]] = None
    output_schema: Optional[Dict[str, Any]] = None
    
    # Capability metadata
    supported_languages: List[str] = field(default_factory=list)
    supported_formats: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    
    # Performance characteristics
    estimated_duration_seconds: Optional[float] = None
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # Configuration
    configuration_schema: Optional[Dict[str, Any]] = None
    default_configuration: Dict[str, Any] = field(default_factory=dict)
    
    # Quality metrics
    accuracy_score: Optional[float] = None
    reliability_score: Optional[float] = None
    
    def is_compatible_with(self, other: 'CapabilityModel') -> bool:
        """Check if this capability is compatible with another."""
        # Basic compatibility check based on type and dependencies
        if self.capability_type == other.capability_type:
            return True
        
        # Check if output of one matches input requirements of another
        # This would need more sophisticated schema matching in practice
        return False


@dataclass
class ContextModel:
    """
    Unified context data structure.
    
    Represents context information that can be shared
    across components and sessions.
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    context_type: str = ""
    scope: str = "global"  # global, session, task, component
    
    # Context data
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Lifecycle
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    
    # Relationships
    parent_context_id: Optional[str] = None
    related_context_ids: List[str] = field(default_factory=list)
    
    # Access control
    owner: Optional[str] = None
    access_level: str = "public"  # public, private, restricted
    
    def update_data(self, key: str, value: Any) -> None:
        """Update context data."""
        self.data[key] = value
        self.updated_at = datetime.now()
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """Get context data."""
        return self.data.get(key, default)
    
    def is_expired(self) -> bool:
        """Check if context has expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def merge_context(self, other: 'ContextModel') -> None:
        """Merge another context into this one."""
        self.data.update(other.data)
        self.metadata.update(other.metadata)
        self.updated_at = datetime.now()


@dataclass
class MetricsSnapshot:
    """
    Snapshot of metrics at a point in time.
    
    Provides a standardized way to capture and share
    metrics across components.
    """
    timestamp: datetime
    metrics: Dict[str, Any]
    source: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    def get_metric(self, name: str, default: Any = None) -> Any:
        """Get a specific metric value."""
        return self.metrics.get(name, default)
    
    def add_metric(self, name: str, value: Any) -> None:
        """Add a metric to the snapshot."""
        self.metrics[name] = value
    
    def add_tag(self, key: str, value: str) -> None:
        """Add a tag to the snapshot."""
        self.tags[key] = value


@dataclass
class UnifiedRequest:
    """
    Unified request model for component communication.
    
    Provides a standardized way to make requests between
    components with proper context and metadata.
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    request_type: str = ""
    target: str = ""  # Target component/agent/plugin
    
    # Request data
    payload: Dict[str, Any] = field(default_factory=dict)
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Context
    context: Optional[ContextModel] = None
    task_id: Optional[str] = None
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    
    # Execution options
    timeout_seconds: Optional[int] = None
    priority: TaskPriority = TaskPriority.NORMAL
    async_execution: bool = False
    
    # Tracking
    created_at: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    
    def add_header(self, key: str, value: str) -> None:
        """Add a header to the request."""
        self.headers[key] = value
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the request."""
        self.metadata[key] = value


# Utility functions for working with unified models

def create_task_from_dict(data: Dict[str, Any]) -> UnifiedTask:
    """Create a UnifiedTask from a dictionary."""
    # Convert string enums to proper enum values
    if 'priority' in data and isinstance(data['priority'], str):
        data['priority'] = TaskPriority(data['priority'])
    
    if 'status' in data and isinstance(data['status'], str):
        data['status'] = TaskStatus(data['status'])
    
    # Convert datetime strings
    for field_name in ['created_at', 'started_at', 'completed_at']:
        if field_name in data and isinstance(data[field_name], str):
            data[field_name] = datetime.fromisoformat(data[field_name])
    
    return UnifiedTask(**data)


def task_to_dict(task: UnifiedTask) -> Dict[str, Any]:
    """Convert a UnifiedTask to a dictionary."""
    result = {}
    
    for field_name, field_value in task.__dict__.items():
        if isinstance(field_value, Enum):
            result[field_name] = field_value.value
        elif isinstance(field_value, datetime):
            result[field_name] = field_value.isoformat()
        else:
            result[field_name] = field_value
    
    return result
