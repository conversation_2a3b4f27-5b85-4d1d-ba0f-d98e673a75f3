"""
Database connection pooling for the agent framework.

Provides connection pooling for PostgreSQL, SQLite, and other databases
with automatic connection management, health checks, and monitoring.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Union, AsyncContextManager
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum
import time

try:
    import asyncpg
    ASYNCPG_AVAILABLE = True
except ImportError:
    ASYNCPG_AVAILABLE = False

try:
    import aiosqlite
    AIOSQLITE_AVAILABLE = True
except ImportError:
    AIOSQLITE_AVAILABLE = False


class DatabaseType(Enum):
    """Supported database types."""
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"


@dataclass
class DatabaseConfig:
    """Database configuration."""
    database_type: DatabaseType
    connection_string: str
    min_connections: int = 5
    max_connections: int = 20
    connection_timeout: float = 30.0
    command_timeout: float = 60.0
    max_inactive_connection_lifetime: float = 300.0  # 5 minutes
    health_check_interval: float = 60.0  # 1 minute


@dataclass
class PoolStats:
    """Connection pool statistics."""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_queries: int = 0
    failed_queries: int = 0
    average_query_time: float = 0.0
    last_health_check: Optional[float] = None


class DatabasePool:
    """
    Database connection pool with health monitoring and automatic recovery.
    
    Supports PostgreSQL and SQLite with connection pooling, health checks,
    and comprehensive error handling.
    """
    
    def __init__(self, config: DatabaseConfig):
        """Initialize the database pool."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Pool state
        self._pool = None
        self._is_initialized = False
        self._is_healthy = True
        
        # Statistics
        self._stats = PoolStats()
        self._query_times = []
        
        # Health monitoring
        self._health_check_task: Optional[asyncio.Task] = None
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate database configuration."""
        if not self.config.connection_string:
            raise ValueError("Connection string is required")
        
        if self.config.database_type == DatabaseType.POSTGRESQL and not ASYNCPG_AVAILABLE:
            raise ImportError("asyncpg is required for PostgreSQL support")
        
        if self.config.database_type == DatabaseType.SQLITE and not AIOSQLITE_AVAILABLE:
            raise ImportError("aiosqlite is required for SQLite support")
        
        if self.config.min_connections < 1:
            raise ValueError("min_connections must be at least 1")
        
        if self.config.max_connections < self.config.min_connections:
            raise ValueError("max_connections must be >= min_connections")
    
    async def initialize(self) -> None:
        """Initialize the connection pool."""
        if self._is_initialized:
            return
        
        self.logger.info(f"Initializing {self.config.database_type.value} connection pool")
        
        try:
            if self.config.database_type == DatabaseType.POSTGRESQL:
                await self._initialize_postgresql_pool()
            elif self.config.database_type == DatabaseType.SQLITE:
                await self._initialize_sqlite_pool()
            
            self._is_initialized = True
            self._is_healthy = True
            
            # Start health monitoring
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.logger.info("Database pool initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database pool: {e}")
            self._is_healthy = False
            raise
    
    async def _initialize_postgresql_pool(self) -> None:
        """Initialize PostgreSQL connection pool."""
        self._pool = await asyncpg.create_pool(
            self.config.connection_string,
            min_size=self.config.min_connections,
            max_size=self.config.max_connections,
            command_timeout=self.config.command_timeout,
            max_inactive_connection_lifetime=self.config.max_inactive_connection_lifetime
        )
        
        # Update stats
        self._stats.total_connections = self.config.min_connections
        self._stats.idle_connections = self.config.min_connections
    
    async def _initialize_sqlite_pool(self) -> None:
        """Initialize SQLite connection pool (simulated)."""
        # SQLite doesn't have true connection pooling, but we can simulate it
        # by managing a pool of connection objects
        self._pool = []
        for _ in range(self.config.min_connections):
            conn = await aiosqlite.connect(self.config.connection_string)
            self._pool.append(conn)
        
        # Update stats
        self._stats.total_connections = len(self._pool)
        self._stats.idle_connections = len(self._pool)
    
    async def shutdown(self) -> None:
        """Shutdown the connection pool."""
        if not self._is_initialized:
            return
        
        self.logger.info("Shutting down database pool")
        
        # Stop health monitoring
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Close pool
        if self._pool:
            if self.config.database_type == DatabaseType.POSTGRESQL:
                await self._pool.close()
            elif self.config.database_type == DatabaseType.SQLITE:
                for conn in self._pool:
                    await conn.close()
        
        self._is_initialized = False
        self._is_healthy = False
        
        self.logger.info("Database pool shutdown complete")
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncContextManager:
        """Get a database connection from the pool."""
        if not self._is_initialized:
            raise RuntimeError("Database pool not initialized")
        
        if not self._is_healthy:
            raise RuntimeError("Database pool is unhealthy")
        
        connection = None
        start_time = time.time()
        
        try:
            if self.config.database_type == DatabaseType.POSTGRESQL:
                connection = await self._pool.acquire(timeout=self.config.connection_timeout)
            elif self.config.database_type == DatabaseType.SQLITE:
                # For SQLite, we'll use a simple round-robin approach
                if self._pool:
                    connection = self._pool.pop(0)
                    self._pool.append(connection)
                else:
                    connection = await aiosqlite.connect(self.config.connection_string)
            
            # Update stats
            self._stats.active_connections += 1
            self._stats.idle_connections = max(0, self._stats.idle_connections - 1)
            
            yield connection
            
        except Exception as e:
            self.logger.error(f"Error getting database connection: {e}")
            self._stats.failed_connections += 1
            raise
        
        finally:
            # Return connection to pool
            if connection:
                try:
                    if self.config.database_type == DatabaseType.POSTGRESQL:
                        await self._pool.release(connection)
                    # SQLite connections are already back in the pool
                    
                    # Update stats
                    self._stats.active_connections = max(0, self._stats.active_connections - 1)
                    self._stats.idle_connections += 1
                    
                    # Track query time
                    query_time = time.time() - start_time
                    self._query_times.append(query_time)
                    
                    # Keep only last 100 query times for average calculation
                    if len(self._query_times) > 100:
                        self._query_times.pop(0)
                    
                    self._stats.average_query_time = sum(self._query_times) / len(self._query_times)
                    
                except Exception as e:
                    self.logger.error(f"Error returning connection to pool: {e}")
    
    async def execute_query(self, query: str, *args) -> Any:
        """Execute a query using a pooled connection."""
        async with self.get_connection() as conn:
            try:
                if self.config.database_type == DatabaseType.POSTGRESQL:
                    result = await conn.fetch(query, *args)
                elif self.config.database_type == DatabaseType.SQLITE:
                    cursor = await conn.execute(query, args)
                    result = await cursor.fetchall()
                
                self._stats.total_queries += 1
                return result
                
            except Exception as e:
                self._stats.failed_queries += 1
                self.logger.error(f"Query execution failed: {e}")
                raise
    
    async def _health_check_loop(self) -> None:
        """Background health check loop."""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                self._is_healthy = False
    
    async def _perform_health_check(self) -> None:
        """Perform health check on the database pool."""
        try:
            # Simple connectivity test
            async with self.get_connection() as conn:
                if self.config.database_type == DatabaseType.POSTGRESQL:
                    await conn.fetchval("SELECT 1")
                elif self.config.database_type == DatabaseType.SQLITE:
                    cursor = await conn.execute("SELECT 1")
                    await cursor.fetchone()
            
            self._is_healthy = True
            self._stats.last_health_check = time.time()
            
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            self._is_healthy = False
    
    def get_stats(self) -> PoolStats:
        """Get current pool statistics."""
        return self._stats
    
    def is_healthy(self) -> bool:
        """Check if the pool is healthy."""
        return self._is_healthy and self._is_initialized
