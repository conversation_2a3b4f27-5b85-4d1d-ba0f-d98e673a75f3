"""
Base metrics class providing standardized metrics collection patterns.
"""

import asyncio
import time
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional, Callable, Awaitable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """Represents a single metric value."""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class MetricsSnapshot:
    """Snapshot of metrics at a point in time."""
    timestamp: datetime
    metrics: Dict[str, Any]


class BaseMetrics:
    """
    Base metrics collection class.
    
    Provides standardized patterns for collecting, storing,
    and reporting metrics across all framework components.
    """
    
    def __init__(self, namespace: str, max_history: int = 1000):
        """
        Initialize metrics collector.
        
        Args:
            namespace: Namespace for metrics (e.g., "agent_executor")
            max_history: Maximum number of historical values to keep
        """
        self.namespace = namespace
        self.max_history = max_history
        
        # Metric storage
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        
        # Metric metadata
        self._metric_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Custom collectors
        self._custom_collectors: Dict[str, Callable[[], Any]] = {}
        
        # State
        self._is_initialized = False
        self._collection_enabled = True
    
    async def initialize(self) -> None:
        """Initialize the metrics collector."""
        if self._is_initialized:
            return
        
        self._is_initialized = True
        self._collection_enabled = True
    
    async def shutdown(self) -> None:
        """Shutdown the metrics collector."""
        self._collection_enabled = False
        self._is_initialized = False
    
    def record_counter(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None) -> None:
        """
        Record a counter metric (cumulative value).
        
        Args:
            name: Metric name
            value: Value to add to counter
            tags: Optional tags for the metric
        """
        if not self._collection_enabled:
            return
        
        full_name = f"{self.namespace}.{name}"
        self._counters[full_name] += value
        
        # Store metadata
        self._metric_metadata[full_name] = {
            "type": MetricType.COUNTER,
            "tags": tags or {},
            "last_updated": datetime.now()
        }
    
    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """
        Record a gauge metric (current value).
        
        Args:
            name: Metric name
            value: Current value
            tags: Optional tags for the metric
        """
        if not self._collection_enabled:
            return
        
        full_name = f"{self.namespace}.{name}"
        self._gauges[full_name] = value
        
        # Store metadata
        self._metric_metadata[full_name] = {
            "type": MetricType.GAUGE,
            "tags": tags or {},
            "last_updated": datetime.now()
        }
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """
        Record a histogram metric (distribution of values).
        
        Args:
            name: Metric name
            value: Value to add to histogram
            tags: Optional tags for the metric
        """
        if not self._collection_enabled:
            return
        
        full_name = f"{self.namespace}.{name}"
        self._histograms[full_name].append(value)
        
        # Store metadata
        self._metric_metadata[full_name] = {
            "type": MetricType.HISTOGRAM,
            "tags": tags or {},
            "last_updated": datetime.now()
        }
    
    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
        """
        Record a timer metric (execution duration).
        
        Args:
            name: Metric name
            duration: Duration in seconds
            tags: Optional tags for the metric
        """
        if not self._collection_enabled:
            return
        
        full_name = f"{self.namespace}.{name}"
        self._timers[full_name].append(duration)
        
        # Store metadata
        self._metric_metadata[full_name] = {
            "type": MetricType.TIMER,
            "tags": tags or {},
            "last_updated": datetime.now()
        }
    
    def start_timer(self, name: str, tags: Optional[Dict[str, str]] = None) -> 'TimerContext':
        """
        Start a timer context for measuring execution time.
        
        Args:
            name: Timer name
            tags: Optional tags for the metric
            
        Returns:
            Timer context manager
        """
        return TimerContext(self, name, tags)
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter by 1."""
        self.record_counter(name, 1.0, tags)
    
    def decrement_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Decrement a counter by 1."""
        self.record_counter(name, -1.0, tags)
    
    def add_custom_collector(self, name: str, collector: Callable[[], Any]) -> None:
        """
        Add a custom metric collector.
        
        Args:
            name: Collector name
            collector: Function that returns metric value
        """
        self._custom_collectors[name] = collector
    
    def remove_custom_collector(self, name: str) -> None:
        """Remove a custom metric collector."""
        self._custom_collectors.pop(name, None)
    
    async def get_snapshot(self) -> MetricsSnapshot:
        """
        Get current metrics snapshot.
        
        Returns:
            Current metrics snapshot
        """
        metrics = {}
        
        # Add counters
        for name, value in self._counters.items():
            metrics[name] = value
        
        # Add gauges
        for name, value in self._gauges.items():
            metrics[name] = value
        
        # Add histogram statistics
        for name, values in self._histograms.items():
            if values:
                metrics[f"{name}.count"] = len(values)
                metrics[f"{name}.min"] = min(values)
                metrics[f"{name}.max"] = max(values)
                metrics[f"{name}.avg"] = sum(values) / len(values)
                
                # Calculate percentiles
                sorted_values = sorted(values)
                metrics[f"{name}.p50"] = self._percentile(sorted_values, 50)
                metrics[f"{name}.p95"] = self._percentile(sorted_values, 95)
                metrics[f"{name}.p99"] = self._percentile(sorted_values, 99)
        
        # Add timer statistics
        for name, durations in self._timers.items():
            if durations:
                metrics[f"{name}.count"] = len(durations)
                metrics[f"{name}.min_ms"] = min(durations) * 1000
                metrics[f"{name}.max_ms"] = max(durations) * 1000
                metrics[f"{name}.avg_ms"] = (sum(durations) / len(durations)) * 1000
                
                # Calculate percentiles
                sorted_durations = sorted(durations)
                metrics[f"{name}.p50_ms"] = self._percentile(sorted_durations, 50) * 1000
                metrics[f"{name}.p95_ms"] = self._percentile(sorted_durations, 95) * 1000
                metrics[f"{name}.p99_ms"] = self._percentile(sorted_durations, 99) * 1000
        
        # Add custom metrics
        for name, collector in self._custom_collectors.items():
            try:
                if asyncio.iscoroutinefunction(collector):
                    value = await collector()
                else:
                    value = collector()
                metrics[f"{self.namespace}.{name}"] = value
            except Exception:
                # Ignore errors in custom collectors
                pass
        
        return MetricsSnapshot(
            timestamp=datetime.now(),
            metrics=metrics
        )
    
    def get_counter(self, name: str) -> float:
        """Get current counter value."""
        full_name = f"{self.namespace}.{name}"
        return self._counters.get(full_name, 0.0)
    
    def get_gauge(self, name: str) -> Optional[float]:
        """Get current gauge value."""
        full_name = f"{self.namespace}.{name}"
        return self._gauges.get(full_name)
    
    def get_histogram_stats(self, name: str) -> Optional[Dict[str, float]]:
        """Get histogram statistics."""
        full_name = f"{self.namespace}.{name}"
        values = self._histograms.get(full_name)
        
        if not values:
            return None
        
        sorted_values = sorted(values)
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "p50": self._percentile(sorted_values, 50),
            "p95": self._percentile(sorted_values, 95),
            "p99": self._percentile(sorted_values, 99)
        }
    
    def get_timer_stats(self, name: str) -> Optional[Dict[str, float]]:
        """Get timer statistics."""
        full_name = f"{self.namespace}.{name}"
        durations = self._timers.get(full_name)
        
        if not durations:
            return None
        
        sorted_durations = sorted(durations)
        return {
            "count": len(durations),
            "min_ms": min(durations) * 1000,
            "max_ms": max(durations) * 1000,
            "avg_ms": (sum(durations) / len(durations)) * 1000,
            "p50_ms": self._percentile(sorted_durations, 50) * 1000,
            "p95_ms": self._percentile(sorted_durations, 95) * 1000,
            "p99_ms": self._percentile(sorted_durations, 99) * 1000
        }
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        self._counters.clear()
        self._gauges.clear()
        self._histograms.clear()
        self._timers.clear()
        self._metric_metadata.clear()
    
    def _percentile(self, sorted_values: List[float], percentile: float) -> float:
        """Calculate percentile from sorted values."""
        if not sorted_values:
            return 0.0
        
        index = (percentile / 100.0) * (len(sorted_values) - 1)
        lower_index = int(index)
        upper_index = min(lower_index + 1, len(sorted_values) - 1)
        
        if lower_index == upper_index:
            return sorted_values[lower_index]
        
        # Linear interpolation
        weight = index - lower_index
        return sorted_values[lower_index] * (1 - weight) + sorted_values[upper_index] * weight


class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, metrics: BaseMetrics, name: str, tags: Optional[Dict[str, str]] = None):
        self.metrics = metrics
        self.name = name
        self.tags = tags
        self.start_time: Optional[float] = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.metrics.record_timer(self.name, duration, self.tags)
