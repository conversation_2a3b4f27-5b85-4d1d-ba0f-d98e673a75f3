"""
Base validator class providing common validation patterns.
"""

import re
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass
from enum import Enum


class ValidationSeverity(Enum):
    """Validation issue severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationIssue:
    """Represents a validation issue."""
    field: str
    message: str
    severity: ValidationSeverity
    code: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


@dataclass
class ValidationResult:
    """Result of validation operation."""
    is_valid: bool
    issues: List[ValidationIssue]
    
    @property
    def errors(self) -> List[ValidationIssue]:
        """Get only error-level issues."""
        return [issue for issue in self.issues if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]]
    
    @property
    def warnings(self) -> List[ValidationIssue]:
        """Get only warning-level issues."""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.WARNING]
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0


class BaseValidator(ABC):
    """
    Base class for all validators.
    
    Provides common validation patterns and utilities.
    """
    
    def __init__(self, name: str):
        """
        Initialize the validator.
        
        Args:
            name: Validator name for identification
        """
        self.name = name
        self.issues: List[ValidationIssue] = []
    
    def reset(self) -> None:
        """Reset validation state."""
        self.issues.clear()
    
    def add_issue(self, field: str, message: str, severity: ValidationSeverity = ValidationSeverity.ERROR,
                  code: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Add a validation issue.
        
        Args:
            field: Field name that has the issue
            message: Human-readable error message
            severity: Issue severity level
            code: Optional error code
            context: Optional additional context
        """
        issue = ValidationIssue(
            field=field,
            message=message,
            severity=severity,
            code=code,
            context=context
        )
        self.issues.append(issue)
    
    def add_error(self, field: str, message: str, code: Optional[str] = None) -> None:
        """Add an error-level issue."""
        self.add_issue(field, message, ValidationSeverity.ERROR, code)
    
    def add_warning(self, field: str, message: str, code: Optional[str] = None) -> None:
        """Add a warning-level issue."""
        self.add_issue(field, message, ValidationSeverity.WARNING, code)
    
    def add_info(self, field: str, message: str, code: Optional[str] = None) -> None:
        """Add an info-level issue."""
        self.add_issue(field, message, ValidationSeverity.INFO, code)
    
    @abstractmethod
    async def validate(self, data: Any) -> ValidationResult:
        """
        Validate the provided data.
        
        Args:
            data: Data to validate
            
        Returns:
            Validation result
        """
        pass
    
    # Common validation utilities
    
    def validate_required(self, data: Dict[str, Any], field: str) -> bool:
        """
        Validate that a required field is present and not None.
        
        Args:
            data: Data dictionary
            field: Field name to check
            
        Returns:
            True if valid, False otherwise
        """
        if field not in data or data[field] is None:
            self.add_error(field, f"Field '{field}' is required")
            return False
        return True
    
    def validate_type(self, data: Dict[str, Any], field: str, expected_type: type) -> bool:
        """
        Validate field type.
        
        Args:
            data: Data dictionary
            field: Field name to check
            expected_type: Expected type
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and data[field] is not None:
            if not isinstance(data[field], expected_type):
                self.add_error(field, f"Field '{field}' must be of type {expected_type.__name__}")
                return False
        return True
    
    def validate_string_length(self, data: Dict[str, Any], field: str, 
                             min_length: Optional[int] = None, 
                             max_length: Optional[int] = None) -> bool:
        """
        Validate string length.
        
        Args:
            data: Data dictionary
            field: Field name to check
            min_length: Minimum length (optional)
            max_length: Maximum length (optional)
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and isinstance(data[field], str):
            length = len(data[field])
            
            if min_length is not None and length < min_length:
                self.add_error(field, f"Field '{field}' must be at least {min_length} characters")
                return False
            
            if max_length is not None and length > max_length:
                self.add_error(field, f"Field '{field}' must be at most {max_length} characters")
                return False
        
        return True
    
    def validate_regex(self, data: Dict[str, Any], field: str, pattern: str, 
                      message: Optional[str] = None) -> bool:
        """
        Validate field against regex pattern.
        
        Args:
            data: Data dictionary
            field: Field name to check
            pattern: Regex pattern
            message: Custom error message
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and isinstance(data[field], str):
            if not re.match(pattern, data[field]):
                error_msg = message or f"Field '{field}' does not match required pattern"
                self.add_error(field, error_msg)
                return False
        return True
    
    def validate_choices(self, data: Dict[str, Any], field: str, choices: List[Any]) -> bool:
        """
        Validate field value is in allowed choices.
        
        Args:
            data: Data dictionary
            field: Field name to check
            choices: List of allowed values
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and data[field] not in choices:
            self.add_error(field, f"Field '{field}' must be one of: {choices}")
            return False
        return True
    
    def validate_range(self, data: Dict[str, Any], field: str, 
                      min_value: Optional[Union[int, float]] = None,
                      max_value: Optional[Union[int, float]] = None) -> bool:
        """
        Validate numeric field is within range.
        
        Args:
            data: Data dictionary
            field: Field name to check
            min_value: Minimum value (optional)
            max_value: Maximum value (optional)
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and isinstance(data[field], (int, float)):
            value = data[field]
            
            if min_value is not None and value < min_value:
                self.add_error(field, f"Field '{field}' must be at least {min_value}")
                return False
            
            if max_value is not None and value > max_value:
                self.add_error(field, f"Field '{field}' must be at most {max_value}")
                return False
        
        return True
    
    def validate_list_items(self, data: Dict[str, Any], field: str, 
                           item_validator: Callable[[Any], bool]) -> bool:
        """
        Validate each item in a list field.
        
        Args:
            data: Data dictionary
            field: Field name to check
            item_validator: Function to validate each item
            
        Returns:
            True if all items are valid, False otherwise
        """
        if field in data and isinstance(data[field], list):
            all_valid = True
            for i, item in enumerate(data[field]):
                if not item_validator(item):
                    self.add_error(f"{field}[{i}]", f"Invalid item at index {i}")
                    all_valid = False
            return all_valid
        return True
    
    def validate_nested_object(self, data: Dict[str, Any], field: str, 
                              validator: 'BaseValidator') -> bool:
        """
        Validate a nested object using another validator.
        
        Args:
            data: Data dictionary
            field: Field name to check
            validator: Validator to use for nested object
            
        Returns:
            True if valid, False otherwise
        """
        if field in data and data[field] is not None:
            # Note: This would need to be async in practice
            # For now, we'll add a placeholder
            self.add_warning(field, "Nested validation not implemented in sync context")
            return True
        return True
    
    def get_result(self) -> ValidationResult:
        """
        Get the validation result.
        
        Returns:
            ValidationResult with current issues
        """
        has_errors = any(issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL] 
                        for issue in self.issues)
        
        return ValidationResult(
            is_valid=not has_errors,
            issues=self.issues.copy()
        )
