"""
Base processor class providing common processing patterns for agents and plugins.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from datetime import datetime
import time

from .base_metrics import BaseMetrics
from .data_models import UnifiedTask, StandardResult, MetricsSnapshot

T = TypeVar('T')
R = TypeVar('R')


class ProcessingContext:
    """Context information for processing operations."""
    
    def __init__(self, task_id: str, user_id: Optional[str] = None, session_id: Optional[str] = None):
        self.task_id = task_id
        self.user_id = user_id
        self.session_id = session_id
        self.started_at = datetime.now()
        self.metadata: Dict[str, Any] = {}
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the context."""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata from the context."""
        return self.metadata.get(key, default)


class BaseProcessor(ABC, Generic[T, R]):
    """
    Base class for all processing components (agents, plugins, etc.).
    
    Provides common patterns for input validation, processing,
    error handling, and metrics collection.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the base processor.
        
        Args:
            name: Processor name for logging and identification
            config: Processor configuration
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Metrics
        self.metrics = BaseMetrics(f"{name}_processor")
        
        # Processing state
        self._is_initialized = False
        self._processing_count = 0
        self._total_processed = 0
        self._total_errors = 0
    
    @abstractmethod
    async def _validate_input(self, input_data: T, context: ProcessingContext) -> bool:
        """
        Validate input data.
        
        Args:
            input_data: Input data to validate
            context: Processing context
            
        Returns:
            True if valid, False otherwise
        """
        pass
    
    @abstractmethod
    async def _process_data(self, input_data: T, context: ProcessingContext) -> R:
        """
        Process the input data.
        
        Args:
            input_data: Input data to process
            context: Processing context
            
        Returns:
            Processed result
        """
        pass
    
    @abstractmethod
    async def _handle_error(self, error: Exception, input_data: T, context: ProcessingContext) -> Optional[R]:
        """
        Handle processing errors.
        
        Args:
            error: The error that occurred
            input_data: Input data that caused the error
            context: Processing context
            
        Returns:
            Optional recovery result
        """
        pass
    
    async def initialize(self) -> None:
        """Initialize the processor."""
        if self._is_initialized:
            return
        
        self.logger.info(f"Initializing processor: {self.name}")
        
        try:
            # Initialize metrics
            await self.metrics.initialize()
            
            # Processor-specific initialization
            await self._initialize_processor()
            
            self._is_initialized = True
            self.logger.info(f"Processor {self.name} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize processor {self.name}: {e}")
            raise
    
    async def _initialize_processor(self) -> None:
        """Processor-specific initialization. Override if needed."""
        pass
    
    async def process(self, input_data: T, context: Optional[ProcessingContext] = None) -> StandardResult[R]:
        """
        Process input data with comprehensive error handling and metrics.
        
        Args:
            input_data: Data to process
            context: Optional processing context
            
        Returns:
            Standard result with processing outcome
        """
        if not self._is_initialized:
            raise RuntimeError(f"Processor {self.name} not initialized")
        
        # Create context if not provided
        if context is None:
            context = ProcessingContext(task_id=f"{self.name}_{int(time.time())}")
        
        start_time = time.time()
        self._processing_count += 1
        
        try:
            # Record processing start
            self.metrics.record_counter("processing_started", 1.0)
            
            # Validate input
            self.logger.debug(f"Validating input for {context.task_id}")
            is_valid = await self._validate_input(input_data, context)
            
            if not is_valid:
                self._total_errors += 1
                self.metrics.record_counter("validation_errors", 1.0)
                return StandardResult(
                    success=False,
                    error="Input validation failed",
                    metadata={"context": context.metadata}
                )
            
            # Process data
            self.logger.debug(f"Processing data for {context.task_id}")
            result = await self._process_data(input_data, context)
            
            # Record success
            processing_time = time.time() - start_time
            self._total_processed += 1
            self.metrics.record_timer("processing_time", processing_time)
            self.metrics.record_counter("processing_completed", 1.0)
            
            self.logger.debug(f"Processing completed for {context.task_id} in {processing_time:.3f}s")
            
            return StandardResult(
                success=True,
                result=result,
                execution_time=processing_time,
                metadata={
                    "context": context.metadata,
                    "processor": self.name
                }
            )
            
        except Exception as e:
            # Handle error
            processing_time = time.time() - start_time
            self._total_errors += 1
            self.metrics.record_counter("processing_errors", 1.0)
            self.metrics.record_timer("error_processing_time", processing_time)
            
            self.logger.error(f"Error processing {context.task_id}: {e}")
            
            # Try error recovery
            try:
                recovery_result = await self._handle_error(e, input_data, context)
                if recovery_result is not None:
                    self.metrics.record_counter("error_recoveries", 1.0)
                    return StandardResult(
                        success=True,
                        result=recovery_result,
                        execution_time=processing_time,
                        metadata={
                            "context": context.metadata,
                            "processor": self.name,
                            "recovered_from_error": str(e)
                        }
                    )
            except Exception as recovery_error:
                self.logger.error(f"Error recovery failed for {context.task_id}: {recovery_error}")
            
            return StandardResult(
                success=False,
                error=str(e),
                execution_time=processing_time,
                metadata={
                    "context": context.metadata,
                    "processor": self.name
                }
            )
        
        finally:
            self._processing_count -= 1
    
    async def batch_process(self, input_batch: List[T], 
                          max_concurrent: int = 5) -> List[StandardResult[R]]:
        """
        Process a batch of inputs concurrently.
        
        Args:
            input_batch: List of inputs to process
            max_concurrent: Maximum concurrent processing tasks
            
        Returns:
            List of processing results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(input_data: T) -> StandardResult[R]:
            async with semaphore:
                return await self.process(input_data)
        
        tasks = [process_with_semaphore(input_data) for input_data in input_batch]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(StandardResult(
                    success=False,
                    error=str(result),
                    metadata={"batch_index": i}
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def get_metrics(self) -> MetricsSnapshot:
        """Get processor metrics."""
        base_metrics = await self.metrics.get_snapshot()
        
        processor_metrics = {
            "processor_name": self.name,
            "current_processing": self._processing_count,
            "total_processed": self._total_processed,
            "total_errors": self._total_errors,
            "error_rate": (
                self._total_errors / max(self._total_processed, 1)
                if self._total_processed > 0 else 0
            )
        }
        
        return MetricsSnapshot(
            timestamp=datetime.now(),
            metrics={**base_metrics.metrics, **processor_metrics}
        )
    
    async def shutdown(self) -> None:
        """Shutdown the processor."""
        self.logger.info(f"Shutting down processor: {self.name}")
        
        # Wait for current processing to complete
        while self._processing_count > 0:
            await asyncio.sleep(0.1)
        
        # Shutdown metrics
        await self.metrics.shutdown()
        
        self._is_initialized = False
        self.logger.info(f"Processor {self.name} shut down successfully")
