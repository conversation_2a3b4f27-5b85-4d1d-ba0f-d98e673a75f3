"""
Unified AST analysis engine to eliminate code duplication.

Provides a centralized, extensible AST analysis system with pluggable
analyzers and common patterns for code analysis across the framework.
"""

import ast
import logging
from typing import Dict, List, Any, Optional, Type, Callable, Union
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import inspect


class AnalysisType(Enum):
    """Types of AST analysis."""
    COMPLEXITY = "complexity"
    PATTERNS = "patterns"
    FUNCTIONS = "functions"
    CLASSES = "classes"
    IMPORTS = "imports"
    SECURITY = "security"
    PERFORMANCE = "performance"
    ERRORS = "errors"


@dataclass
class AnalysisResult:
    """Result of AST analysis."""
    analysis_type: AnalysisType
    data: Any
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FunctionInfo:
    """Information about a function."""
    name: str
    line_number: int
    complexity: int = 0
    parameters: List[str] = field(default_factory=list)
    docstring: Optional[str] = None
    decorators: List[str] = field(default_factory=list)
    return_type: Optional[str] = None
    is_async: bool = False


@dataclass
class ClassInfo:
    """Information about a class."""
    name: str
    line_number: int
    methods: List[FunctionInfo] = field(default_factory=list)
    base_classes: List[str] = field(default_factory=list)
    docstring: Optional[str] = None
    decorators: List[str] = field(default_factory=list)


@dataclass
class PatternInfo:
    """Information about a detected pattern."""
    pattern_type: str
    name: str
    line_number: int
    confidence: float
    description: str
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseASTAnalyzer(ABC):
    """Base class for AST analyzers."""
    
    def __init__(self, name: str):
        """Initialize the analyzer."""
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Analyze the AST and return results."""
        pass
    
    @property
    @abstractmethod
    def analysis_type(self) -> AnalysisType:
        """Return the type of analysis this analyzer performs."""
        pass


class ComplexityAnalyzer(BaseASTAnalyzer):
    """Analyzer for cyclomatic complexity."""
    
    def __init__(self) -> None:
        super().__init__("complexity")
    
    @property
    def analysis_type(self) -> AnalysisType:
        return AnalysisType.COMPLEXITY
    
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Calculate cyclomatic complexity."""
        visitor = ComplexityVisitor()
        visitor.visit(tree)
        
        return AnalysisResult(
            analysis_type=self.analysis_type,
            data={
                "total_complexity": visitor.complexity,
                "function_complexities": visitor.function_complexities,
                "class_complexities": visitor.class_complexities
            },
            metadata={"lines_of_code": len(source_code.splitlines())}
        )


class PatternAnalyzer(BaseASTAnalyzer):
    """Analyzer for design patterns and anti-patterns."""
    
    def __init__(self) -> None:
        super().__init__("patterns")
        self._pattern_detectors = self._initialize_pattern_detectors()
    
    @property
    def analysis_type(self) -> AnalysisType:
        return AnalysisType.PATTERNS
    
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Detect patterns in the AST."""
        patterns = []
        
        for detector in self._pattern_detectors:
            detected = detector(tree, source_code)
            patterns.extend(detected)
        
        return AnalysisResult(
            analysis_type=self.analysis_type,
            data=patterns,
            metadata={"total_patterns": len(patterns)}
        )
    
    def _initialize_pattern_detectors(self) -> List[Callable]:
        """Initialize pattern detection functions."""
        return [
            self._detect_singleton_pattern,
            self._detect_factory_pattern,
            self._detect_observer_pattern,
            self._detect_god_class_antipattern,
            self._detect_long_method_antipattern,
            self._detect_duplicate_code
        ]
    
    def _detect_singleton_pattern(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect singleton pattern."""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Look for singleton indicators
                has_instance_var = False
                has_new_method = False
                
                for item in node.body:
                    if isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name) and target.id == "_instance":
                                has_instance_var = True
                    elif isinstance(item, ast.FunctionDef) and item.name == "__new__":
                        has_new_method = True
                
                if has_instance_var and has_new_method:
                    patterns.append(PatternInfo(
                        pattern_type="design_pattern",
                        name="Singleton",
                        line_number=node.lineno,
                        confidence=0.8,
                        description=f"Singleton pattern detected in class {node.name}"
                    ))
        
        return patterns
    
    def _detect_factory_pattern(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect factory pattern."""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Look for factory method indicators
                if (node.name.startswith("create_") or 
                    node.name.startswith("make_") or
                    node.name.endswith("_factory")):
                    
                    # Check if it returns different types based on parameters
                    has_conditional_return = False
                    for child in ast.walk(node):
                        if isinstance(child, ast.If) and any(
                            isinstance(grandchild, ast.Return) 
                            for grandchild in ast.walk(child)
                        ):
                            has_conditional_return = True
                            break
                    
                    if has_conditional_return:
                        patterns.append(PatternInfo(
                            pattern_type="design_pattern",
                            name="Factory",
                            line_number=node.lineno,
                            confidence=0.7,
                            description=f"Factory pattern detected in function {node.name}"
                        ))
        
        return patterns
    
    def _detect_observer_pattern(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect observer pattern."""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                has_observers_list = False
                has_notify_method = False
                has_subscribe_method = False
                
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        if item.name in ["notify", "notify_observers"]:
                            has_notify_method = True
                        elif item.name in ["subscribe", "add_observer", "register"]:
                            has_subscribe_method = True
                    elif isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name) and "observer" in target.id.lower():
                                has_observers_list = True
                
                if has_observers_list and has_notify_method and has_subscribe_method:
                    patterns.append(PatternInfo(
                        pattern_type="design_pattern",
                        name="Observer",
                        line_number=node.lineno,
                        confidence=0.8,
                        description=f"Observer pattern detected in class {node.name}"
                    ))
        
        return patterns
    
    def _detect_god_class_antipattern(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect god class anti-pattern."""
        patterns = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                method_count = len([n for n in node.body if isinstance(n, ast.FunctionDef)])
                line_count = len([n for n in ast.walk(node)])
                
                if method_count > 20 or line_count > 500:
                    patterns.append(PatternInfo(
                        pattern_type="anti_pattern",
                        name="God Class",
                        line_number=node.lineno,
                        confidence=0.9,
                        description=f"God class detected: {node.name} has {method_count} methods",
                        metadata={"method_count": method_count, "line_count": line_count}
                    ))
        
        return patterns
    
    def _detect_long_method_antipattern(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect long method anti-pattern."""
        patterns = []
        source_lines = source_code.splitlines()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Calculate method length
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    method_length = node.end_lineno - node.lineno + 1
                else:
                    # Fallback calculation
                    method_length = len([n for n in ast.walk(node) if hasattr(n, 'lineno')])
                
                if method_length > 50:
                    patterns.append(PatternInfo(
                        pattern_type="anti_pattern",
                        name="Long Method",
                        line_number=node.lineno,
                        confidence=0.8,
                        description=f"Long method detected: {node.name} has {method_length} lines",
                        metadata={"method_length": method_length}
                    ))
        
        return patterns
    
    def _detect_duplicate_code(self, tree: ast.AST, source_code: str) -> List[PatternInfo]:
        """Detect duplicate code blocks."""
        patterns = []
        
        # Simple duplicate detection based on AST structure
        function_bodies: Dict[tuple, str] = {}
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Create a simplified representation of the function body
                body_repr = []
                for stmt in node.body:
                    if isinstance(stmt, ast.Expr) and isinstance(stmt.value, ast.Constant):
                        continue  # Skip docstrings
                    body_repr.append(type(stmt).__name__)
                
                body_signature = tuple(body_repr)
                if len(body_signature) > 3:  # Only check substantial functions
                    if body_signature in function_bodies:
                        patterns.append(PatternInfo(
                            pattern_type="code_smell",
                            name="Duplicate Code",
                            line_number=node.lineno,
                            confidence=0.7,
                            description=f"Duplicate code detected in {node.name}",
                            metadata={
                                "duplicate_of": function_bodies[body_signature],
                                "body_signature": body_signature
                            }
                        ))
                    else:
                        function_bodies[body_signature] = node.name
        
        return patterns


class FunctionAnalyzer(BaseASTAnalyzer):
    """Analyzer for function information."""
    
    def __init__(self) -> None:
        super().__init__("functions")
    
    @property
    def analysis_type(self) -> AnalysisType:
        return AnalysisType.FUNCTIONS
    
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Extract function information."""
        visitor = FunctionVisitor()
        visitor.visit(tree)
        
        return AnalysisResult(
            analysis_type=self.analysis_type,
            data=visitor.functions,
            metadata={"function_count": len(visitor.functions)}
        )


class ClassAnalyzer(BaseASTAnalyzer):
    """Analyzer for class information."""
    
    def __init__(self) -> None:
        super().__init__("classes")
    
    @property
    def analysis_type(self) -> AnalysisType:
        return AnalysisType.CLASSES
    
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Extract class information."""
        visitor = ClassVisitor()
        visitor.visit(tree)
        
        return AnalysisResult(
            analysis_type=self.analysis_type,
            data=visitor.classes,
            metadata={"class_count": len(visitor.classes)}
        )


class ImportAnalyzer(BaseASTAnalyzer):
    """Analyzer for import dependencies."""
    
    def __init__(self) -> None:
        super().__init__("imports")
    
    @property
    def analysis_type(self) -> AnalysisType:
        return AnalysisType.IMPORTS
    
    def analyze(self, tree: ast.AST, source_code: str) -> AnalysisResult:
        """Extract import information."""
        visitor = ImportVisitor()
        visitor.visit(tree)
        
        return AnalysisResult(
            analysis_type=self.analysis_type,
            data={
                "imports": visitor.imports,
                "from_imports": visitor.from_imports,
                "all_imports": visitor.imports + visitor.from_imports
            },
            metadata={"import_count": len(visitor.imports + visitor.from_imports)}
        )


# AST Visitor classes (shared implementations)
class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor for calculating cyclomatic complexity."""
    
    def __init__(self) -> None:
        self.complexity = 1  # Base complexity
        self.function_complexities: Dict[str, int] = {}
        self.class_complexities: Dict[str, int] = {}
        self.current_function: Optional[str] = None
        self.current_class: Optional[str] = None
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        old_function = self.current_function
        old_complexity = self.complexity

        self.current_function = node.name
        self.complexity = 1  # Reset for this function

        self.generic_visit(node)

        self.function_complexities[node.name] = self.complexity

        # Restore previous state
        self.current_function = old_function
        self.complexity = old_complexity + self.function_complexities[node.name]

    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        # Handle async functions the same way as regular functions
        old_function = self.current_function
        old_complexity = self.complexity

        self.current_function = node.name
        self.complexity = 1  # Reset for this function

        self.generic_visit(node)

        self.function_complexities[node.name] = self.complexity

        # Restore previous state
        self.current_function = old_function
        self.complexity = old_complexity + self.function_complexities[node.name]

    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        old_class = self.current_class
        old_complexity = self.complexity

        self.current_class = node.name
        self.complexity = 0  # Reset for this class

        self.generic_visit(node)

        self.class_complexities[node.name] = self.complexity

        # Restore previous state
        self.current_class = old_class
        self.complexity = old_complexity + self.class_complexities[node.name]

    def visit_If(self, node: ast.If) -> None:
        self.complexity += 1
        self.generic_visit(node)

    def visit_While(self, node: ast.While) -> None:
        self.complexity += 1
        self.generic_visit(node)

    def visit_For(self, node: ast.For) -> None:
        self.complexity += 1
        self.generic_visit(node)

    def visit_ExceptHandler(self, node: ast.ExceptHandler) -> None:
        self.complexity += 1
        self.generic_visit(node)

    def visit_With(self, node: ast.With) -> None:
        self.complexity += 1
        self.generic_visit(node)


class FunctionVisitor(ast.NodeVisitor):
    """AST visitor for extracting function information."""
    
    def __init__(self) -> None:
        self.functions: List[FunctionInfo] = []
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self._process_function(node, is_async=False)
        self.generic_visit(node)

    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self._process_function(node, is_async=True)
        self.generic_visit(node)

    def _process_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef], is_async: bool = False) -> None:
        # Extract parameters
        parameters = [arg.arg for arg in node.args.args]
        
        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            docstring = node.body[0].value.value
        
        # Extract decorators
        decorators = []
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                decorators.append(decorator.id)
            elif isinstance(decorator, ast.Attribute):
                decorators.append(f"{decorator.attr}")
        
        # Calculate complexity
        complexity_visitor = ComplexityVisitor()
        complexity_visitor.visit(node)
        
        function_info = FunctionInfo(
            name=node.name,
            line_number=node.lineno,
            complexity=complexity_visitor.complexity,
            parameters=parameters,
            docstring=docstring,
            decorators=decorators,
            is_async=is_async
        )
        
        self.functions.append(function_info)


class ClassVisitor(ast.NodeVisitor):
    """AST visitor for extracting class information."""
    
    def __init__(self) -> None:
        self.classes: List[ClassInfo] = []
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        # Extract base classes
        base_classes = []
        for base in node.bases:
            if isinstance(base, ast.Name):
                base_classes.append(base.id)
            elif isinstance(base, ast.Attribute):
                base_classes.append(f"{base.attr}")
        
        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            docstring = node.body[0].value.value
        
        # Extract decorators
        decorators = []
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                decorators.append(decorator.id)
        
        # Extract methods
        methods = []
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                function_visitor = FunctionVisitor()
                function_visitor.visit(item)
                if function_visitor.functions:
                    methods.append(function_visitor.functions[0])
        
        class_info = ClassInfo(
            name=node.name,
            line_number=node.lineno,
            methods=methods,
            base_classes=base_classes,
            docstring=docstring,
            decorators=decorators
        )
        
        self.classes.append(class_info)
        self.generic_visit(node)


class ImportVisitor(ast.NodeVisitor):
    """AST visitor for extracting import information."""
    
    def __init__(self) -> None:
        self.imports: List[str] = []
        self.from_imports: List[str] = []
    
    def visit_Import(self, node: ast.Import) -> None:
        for alias in node.names:
            self.imports.append(alias.name)

    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        module = node.module or ""
        for alias in node.names:
            self.from_imports.append(f"{module}.{alias.name}")


class ASTAnalysisEngine:
    """
    Unified AST analysis engine.
    
    Provides a centralized system for analyzing Python AST with
    pluggable analyzers and consistent interfaces.
    """
    
    def __init__(self) -> None:
        """Initialize the analysis engine."""
        self.logger = logging.getLogger(__name__)
        self._analyzers: Dict[AnalysisType, BaseASTAnalyzer] = {}
        
        # Register default analyzers
        self._register_default_analyzers()
    
    def _register_default_analyzers(self) -> None:
        """Register default analyzers."""
        default_analyzers = [
            ComplexityAnalyzer(),
            PatternAnalyzer(),
            FunctionAnalyzer(),
            ClassAnalyzer(),
            ImportAnalyzer()
        ]
        
        for analyzer in default_analyzers:
            self.register_analyzer(analyzer)
    
    def register_analyzer(self, analyzer: BaseASTAnalyzer) -> None:
        """Register an analyzer."""
        self._analyzers[analyzer.analysis_type] = analyzer
        self.logger.info(f"Registered analyzer: {analyzer.name}")
    
    def unregister_analyzer(self, analysis_type: AnalysisType) -> None:
        """Unregister an analyzer."""
        if analysis_type in self._analyzers:
            del self._analyzers[analysis_type]
            self.logger.info(f"Unregistered analyzer: {analysis_type.value}")
    
    def analyze(self, 
                source_code: str, 
                analysis_types: Optional[List[AnalysisType]] = None) -> Dict[AnalysisType, AnalysisResult]:
        """
        Analyze source code with specified analyzers.
        
        Args:
            source_code: Python source code to analyze
            analysis_types: List of analysis types to run (None for all)
            
        Returns:
            Dictionary of analysis results by type
        """
        try:
            tree = ast.parse(source_code)
        except SyntaxError as e:
            self.logger.error(f"Syntax error in source code: {e}")
            return {}
        
        # Determine which analyzers to run
        if analysis_types is None:
            analyzers_to_run = list(self._analyzers.values())
        else:
            analyzers_to_run = [
                self._analyzers[analysis_type]
                for analysis_type in analysis_types
                if analysis_type in self._analyzers
            ]
        
        # Run analyzers
        results = {}
        for analyzer in analyzers_to_run:
            try:
                result = analyzer.analyze(tree, source_code)
                results[analyzer.analysis_type] = result
            except Exception as e:
                self.logger.error(f"Analyzer {analyzer.name} failed: {e}")
        
        return results
    
    def get_available_analyzers(self) -> List[AnalysisType]:
        """Get list of available analysis types."""
        return list(self._analyzers.keys())


# Global instance
ast_analysis_engine = ASTAnalysisEngine()


# Convenience functions for backward compatibility
def detect_patterns(code: str) -> Dict[str, List[Dict[str, Any]]]:
    """Detect patterns in code (backward compatibility)."""
    results = ast_analysis_engine.analyze(code, [AnalysisType.PATTERNS])
    if AnalysisType.PATTERNS in results:
        patterns_data = results[AnalysisType.PATTERNS].data
        # Group by pattern type
        grouped: Dict[str, List[Dict[str, Any]]] = {"design_patterns": [], "anti_patterns": [], "code_smells": []}
        for pattern in patterns_data:
            grouped[pattern.pattern_type + "s"].append({
                "pattern": pattern.name,
                "line": pattern.line_number,
                "confidence": pattern.confidence,
                "description": pattern.description,
                **pattern.metadata
            })
        return grouped
    return {"design_patterns": [], "anti_patterns": [], "code_smells": []}


def extract_functions(code: str) -> List[FunctionInfo]:
    """Extract function information (backward compatibility)."""
    results = ast_analysis_engine.analyze(code, [AnalysisType.FUNCTIONS])
    if AnalysisType.FUNCTIONS in results:
        data = results[AnalysisType.FUNCTIONS].data
        return data if isinstance(data, list) else []
    return []


def extract_classes(code: str) -> List[ClassInfo]:
    """Extract class information (backward compatibility)."""
    results = ast_analysis_engine.analyze(code, [AnalysisType.CLASSES])
    if AnalysisType.CLASSES in results:
        data = results[AnalysisType.CLASSES].data
        return data if isinstance(data, list) else []
    return []


def get_dependencies(code: str) -> Dict[str, List[str]]:
    """Extract dependencies (backward compatibility)."""
    results = ast_analysis_engine.analyze(code, [AnalysisType.IMPORTS])
    if AnalysisType.IMPORTS in results:
        import_data = results[AnalysisType.IMPORTS].data
        return {
            "imports": import_data.get("imports", []),
            "from_imports": import_data.get("from_imports", [])
        }
    return {"imports": [], "from_imports": []}
