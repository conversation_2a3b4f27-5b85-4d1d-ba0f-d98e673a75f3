"""
Test execution engine for the agent framework.

Provides capabilities for:
- Running generated tests
- Collecting test results
- Coverage analysis
- Test report generation
"""

import asyncio
import logging
import subprocess
import tempfile
import os
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime

from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class TestResult:
    """Result of test execution."""
    test_name: str
    status: str  # passed, failed, skipped, error
    duration: float
    error_message: Optional[str] = None
    traceback: Optional[str] = None
    output: str = ""


@dataclass
class TestSuite:
    """A collection of tests."""
    name: str
    tests: List[TestResult] = field(default_factory=list)
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    error_tests: int = 0
    total_duration: float = 0.0
    coverage_percentage: float = 0.0


@dataclass
class TestExecutionReport:
    """Comprehensive test execution report."""
    suites: List[TestSuite] = field(default_factory=list)
    total_tests: int = 0
    total_passed: int = 0
    total_failed: int = 0
    total_skipped: int = 0
    total_errors: int = 0
    total_duration: float = 0.0
    overall_coverage: float = 0.0
    execution_time: datetime = field(default_factory=datetime.now)


class TestExecutor:
    """
    Test execution engine for running and analyzing tests.
    """
    
    def __init__(self):
        """Initialize the test executor."""
        self.logger = logging.getLogger(__name__)
        self._temp_dir: Optional[Path] = None
        
    async def execute_tests(self, 
                           test_code: str, 
                           source_code: str = "",
                           framework: str = "pytest",
                           coverage: bool = True) -> TestExecutionReport:
        """
        Execute test code and return comprehensive results.
        
        Args:
            test_code: The test code to execute
            source_code: The source code being tested
            framework: Testing framework to use (pytest or unittest)
            coverage: Whether to collect coverage information
            
        Returns:
            Test execution report
        """
        self.logger.info(f"Executing tests using {framework}")
        
        try:
            # Create temporary directory for test execution
            self._temp_dir = Path(tempfile.mkdtemp(prefix="agent_tests_"))
            
            # Write test and source files
            test_file = self._temp_dir / "test_generated.py"
            source_file = self._temp_dir / "source_code.py"
            
            test_file.write_text(test_code)
            if source_code:
                source_file.write_text(source_code)
            
            # Execute tests based on framework
            if framework == "pytest":
                report = await self._execute_pytest(test_file, coverage)
            else:
                report = await self._execute_unittest(test_file, coverage)
            
            return report
            
        except Exception as e:
            self.logger.error(f"Test execution failed: {e}")
            return TestExecutionReport(
                suites=[TestSuite(
                    name="execution_error",
                    tests=[TestResult(
                        test_name="execution",
                        status="error",
                        duration=0.0,
                        error_message=str(e)
                    )]
                )]
            )
        finally:
            # Clean up temporary directory
            if self._temp_dir and self._temp_dir.exists():
                import shutil
                shutil.rmtree(self._temp_dir, ignore_errors=True)
    
    async def _execute_pytest(self, test_file: Path, coverage: bool) -> TestExecutionReport:
        """Execute tests using pytest."""
        cmd = ["python", "-m", "pytest", str(test_file), "-v", "--tb=short"]
        
        if coverage:
            cmd.extend(["--cov=.", "--cov-report=json"])
        
        try:
            # Run pytest
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self._temp_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            # Parse pytest output
            return self._parse_pytest_output(
                stdout.decode(), 
                stderr.decode(), 
                process.returncode,
                coverage
            )
            
        except Exception as e:
            self.logger.error(f"Pytest execution failed: {e}")
            return TestExecutionReport()
    
    async def _execute_unittest(self, test_file: Path, coverage: bool) -> TestExecutionReport:
        """Execute tests using unittest."""
        cmd = ["python", "-m", "unittest", "-v"]
        
        if coverage:
            cmd = ["python", "-m", "coverage", "run", "-m", "unittest", "-v"]
        
        try:
            # Run unittest
            process = await asyncio.create_subprocess_exec(
                *cmd,
                cwd=self._temp_dir,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            # Parse unittest output
            return self._parse_unittest_output(
                stdout.decode(), 
                stderr.decode(), 
                process.returncode,
                coverage
            )
            
        except Exception as e:
            self.logger.error(f"Unittest execution failed: {e}")
            return TestExecutionReport()
    
    def _parse_pytest_output(self, stdout: str, stderr: str, returncode: int, coverage: bool) -> TestExecutionReport:
        """Parse pytest output to extract test results."""
        suite = TestSuite(name="pytest_suite")
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Parse individual test results
            if '::' in line and any(status in line for status in ['PASSED', 'FAILED', 'SKIPPED', 'ERROR']):
                parts = line.split()
                if len(parts) >= 2:
                    test_name = parts[0].split('::')[-1]
                    status = parts[1].lower()
                    
                    # Extract duration if available
                    duration = 0.0
                    for part in parts:
                        if 's' in part and part.replace('.', '').replace('s', '').isdigit():
                            try:
                                duration = float(part.replace('s', ''))
                            except ValueError:
                                pass
                    
                    test_result = TestResult(
                        test_name=test_name,
                        status=status,
                        duration=duration,
                        output=line
                    )
                    
                    suite.tests.append(test_result)
                    suite.total_tests += 1
                    
                    if status == 'passed':
                        suite.passed_tests += 1
                    elif status == 'failed':
                        suite.failed_tests += 1
                    elif status == 'skipped':
                        suite.skipped_tests += 1
                    else:
                        suite.error_tests += 1
        
        # Parse coverage if available
        if coverage:
            suite.coverage_percentage = self._extract_coverage_from_output(stdout)
        
        # Calculate totals
        suite.total_duration = sum(test.duration for test in suite.tests)
        
        report = TestExecutionReport(
            suites=[suite],
            total_tests=suite.total_tests,
            total_passed=suite.passed_tests,
            total_failed=suite.failed_tests,
            total_skipped=suite.skipped_tests,
            total_errors=suite.error_tests,
            total_duration=suite.total_duration,
            overall_coverage=suite.coverage_percentage
        )
        
        return report
    
    def _parse_unittest_output(self, stdout: str, stderr: str, returncode: int, coverage: bool) -> TestExecutionReport:
        """Parse unittest output to extract test results."""
        suite = TestSuite(name="unittest_suite")
        
        lines = stdout.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Parse test results from unittest output
            if line.startswith('test_') and any(status in line for status in ['ok', 'FAIL', 'ERROR', 'SKIP']):
                parts = line.split()
                if len(parts) >= 2:
                    test_name = parts[0]
                    
                    # Determine status
                    if 'ok' in line:
                        status = 'passed'
                    elif 'FAIL' in line:
                        status = 'failed'
                    elif 'ERROR' in line:
                        status = 'error'
                    elif 'SKIP' in line:
                        status = 'skipped'
                    else:
                        status = 'unknown'
                    
                    test_result = TestResult(
                        test_name=test_name,
                        status=status,
                        duration=0.0,  # unittest doesn't provide timing by default
                        output=line
                    )
                    
                    suite.tests.append(test_result)
                    suite.total_tests += 1
                    
                    if status == 'passed':
                        suite.passed_tests += 1
                    elif status == 'failed':
                        suite.failed_tests += 1
                    elif status == 'skipped':
                        suite.skipped_tests += 1
                    else:
                        suite.error_tests += 1
        
        # Parse summary line
        for line in lines:
            if 'Ran' in line and 'test' in line:
                # Extract test count from "Ran X tests in Y.Zs"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part.isdigit():
                        suite.total_tests = int(part)
                        break
        
        # Parse coverage if available
        if coverage:
            suite.coverage_percentage = self._extract_coverage_from_output(stdout + stderr)
        
        report = TestExecutionReport(
            suites=[suite],
            total_tests=suite.total_tests,
            total_passed=suite.passed_tests,
            total_failed=suite.failed_tests,
            total_skipped=suite.skipped_tests,
            total_errors=suite.error_tests,
            total_duration=0.0,
            overall_coverage=suite.coverage_percentage
        )
        
        return report
    
    def _extract_coverage_from_output(self, output: str) -> float:
        """Extract coverage percentage from test output."""
        lines = output.split('\n')
        
        for line in lines:
            # Look for coverage percentage
            if '%' in line and any(word in line.lower() for word in ['coverage', 'total']):
                # Extract percentage
                import re
                match = re.search(r'(\d+)%', line)
                if match:
                    return float(match.group(1))
        
        return 0.0
    
    def generate_test_report(self, report: TestExecutionReport) -> str:
        """Generate a human-readable test report."""
        lines = []
        lines.append("=" * 60)
        lines.append("TEST EXECUTION REPORT")
        lines.append("=" * 60)
        lines.append(f"Execution Time: {report.execution_time}")
        lines.append(f"Total Tests: {report.total_tests}")
        lines.append(f"Passed: {report.total_passed}")
        lines.append(f"Failed: {report.total_failed}")
        lines.append(f"Skipped: {report.total_skipped}")
        lines.append(f"Errors: {report.total_errors}")
        lines.append(f"Duration: {report.total_duration:.2f}s")
        lines.append(f"Coverage: {report.overall_coverage:.1f}%")
        lines.append("")
        
        for suite in report.suites:
            lines.append(f"Suite: {suite.name}")
            lines.append("-" * 40)
            
            for test in suite.tests:
                status_symbol = {
                    'passed': '✓',
                    'failed': '✗',
                    'skipped': '⊝',
                    'error': '⚠'
                }.get(test.status, '?')
                
                lines.append(f"{status_symbol} {test.test_name} ({test.duration:.3f}s)")
                
                if test.error_message:
                    lines.append(f"  Error: {test.error_message}")
            
            lines.append("")
        
        return "\n".join(lines)
