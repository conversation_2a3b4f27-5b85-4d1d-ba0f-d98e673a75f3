"""
Task executor for asynchronous task processing with priority queuing.
"""

import asyncio
from typing import Dict, List, Optional, Callable, Any, Set
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass
from uuid import UUID

from ..core.config import FrameworkConfig
from ..core.types import Task, TaskResult, TaskStatus, TaskPriority, TaskHandler

@dataclass
class TaskValidationResult:
    """Result of task validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]

@dataclass
class ExecutorMetrics:
    """Metrics for task executor."""
    total_tasks_processed: int
    successful_tasks: int
    failed_tasks: int
    cancelled_tasks: int
    average_execution_time: float
    current_queue_size: int
    active_tasks: int
    uptime_seconds: float

class TaskExecutor:
    """
    Task executor for asynchronous task processing with priority queuing.
    
    Provides:
    - Priority-based task queuing
    - Concurrent task execution
    - Comprehensive error handling
    - Task validation and monitoring
    - Resource management
    - Retry mechanisms
    """
    
    def __init__(self, config: FrameworkConfig) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the task executor."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the task executor."""
        ...
    
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task with comprehensive validation and error handling."""
        ...
    
    async def execute_task_async(self, task: Task) -> UUID:
        """Execute a task asynchronously and return task ID."""
        ...
    
    async def submit_task(self, task: Task) -> UUID:
        """Submit a task to the execution queue."""
        ...
    
    async def get_task_result(self, task_id: UUID) -> Optional[TaskResult]:
        """Get the result of a task."""
        ...
    
    async def cancel_task(self, task_id: UUID) -> bool:
        """Cancel a running or queued task."""
        ...
    
    async def get_task_status(self, task_id: UUID) -> Optional[TaskStatus]:
        """Get the status of a task."""
        ...
    
    async def list_active_tasks(self) -> List[Task]:
        """List all currently active tasks."""
        ...
    
    async def list_queued_tasks(self) -> List[Task]:
        """List all queued tasks."""
        ...
    
    async def wait_for_task(self, task_id: UUID, timeout: Optional[float] = None) -> Optional[TaskResult]:
        """Wait for a task to complete."""
        ...
    
    async def wait_for_all_tasks(self, timeout: Optional[float] = None) -> List[TaskResult]:
        """Wait for all active tasks to complete."""
        ...
    
    def register_task_handler(self, task_type: str, handler: TaskHandler) -> None:
        """Register a handler for a specific task type."""
        ...
    
    def unregister_task_handler(self, task_type: str) -> None:
        """Unregister a task handler."""
        ...
    
    async def validate_task(self, task: Task) -> TaskValidationResult:
        """Validate a task before execution."""
        ...
    
    async def get_metrics(self) -> ExecutorMetrics:
        """Get executor metrics."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of the executor."""
        ...
    
    def add_task_event_handler(self, event_type: str, handler: Callable[[Task, TaskResult], None]) -> None:
        """Add an event handler for task events."""
        ...
    
    def remove_task_event_handler(self, event_type: str, handler: Callable[[Task, TaskResult], None]) -> None:
        """Remove a task event handler."""
        ...
    
    @asynccontextmanager
    async def task_context(self, task: Task):
        """Context manager for task execution."""
        ...
    
    # Properties
    @property
    def config(self) -> FrameworkConfig: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def is_running(self) -> bool: ...
    
    @property
    def queue_size(self) -> int: ...
    
    @property
    def active_task_count(self) -> int: ...
    
    @property
    def max_concurrent_tasks(self) -> int: ...
