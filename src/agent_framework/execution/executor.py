"""
Task executor for asynchronous task processing with priority queuing.

Enhanced with robust validation, comprehensive error handling, and monitoring.
"""

import asyncio
import logging
import time
import traceback
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

from ..core.config import FrameworkConfig
from ..core.types import Task, TaskResult, TaskStatus, TaskPriority


@dataclass
class TaskExecutionMetrics:
    """Metrics for task execution monitoring."""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    average_execution_time: float = 0.0
    peak_concurrent_tasks: int = 0
    queue_size_history: List[int] = field(default_factory=list)
    error_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class TaskValidationResult:
    """Result of task validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class TaskExecutor:
    """
    Asynchronous task executor with priority queuing and error handling.

    Manages concurrent execution of tasks with proper resource management
    and error recovery.
    """

    def __init__(self, config: FrameworkConfig, message_broker=None):
        """Initialize the task executor with enhanced capabilities."""
        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # Task management
        self._task_queue: asyncio.PriorityQueue = asyncio.PriorityQueue(
            maxsize=config.execution.queue_max_size
        )
        self._active_tasks: Dict[str, asyncio.Task] = {}
        self._task_results: Dict[str, TaskResult] = {}
        self._task_history: Dict[str, Task] = {}

        # Event-driven task completion
        self._task_completion_events: Dict[str, asyncio.Event] = {}
        self._task_completion_futures: Dict[str, asyncio.Future] = {}

        # Worker management
        self._workers: List[asyncio.Task] = []
        self._is_running = False
        self._shutdown_event = asyncio.Event()

        # Enhanced monitoring and metrics
        self._metrics = TaskExecutionMetrics()
        self._execution_times: List[float] = []
        self._error_counts: Dict[str, int] = {}

        # Validation and error handling
        self._task_validators: List[Callable[[Task], TaskValidationResult]] = []
        self._error_handlers: Dict[str, Callable] = {}
        self._retry_strategies: Dict[str, Callable] = {}

        # Performance monitoring
        self._performance_thresholds = {
            'max_execution_time': config.execution.task_timeout_seconds,
            'max_queue_size': config.execution.queue_max_size * 0.8,
            'max_error_rate': 0.1
        }

        self._is_initialized = False

        # Register default validators
        self._register_default_validators()

        # Register default error handlers
        self._register_default_error_handlers()

    def _register_default_validators(self) -> None:
        """Register default task validators."""
        self._task_validators.extend([
            self._validate_task_basic,
            self._validate_task_parameters,
            self._validate_task_dependencies,
            self._validate_task_timeout
        ])

    def _register_default_error_handlers(self) -> None:
        """Register default error handlers."""
        self._error_handlers.update({
            'timeout': self._handle_timeout_error,
            'validation': self._handle_validation_error,
            'execution': self._handle_execution_error,
            'dependency': self._handle_dependency_error
        })

    def add_task_validator(self, validator: Callable[[Task], TaskValidationResult]) -> None:
        """Add a custom task validator."""
        self._task_validators.append(validator)

    def add_error_handler(self, error_type: str, handler: Callable) -> None:
        """Add a custom error handler."""
        self._error_handlers[error_type] = handler

    def _validate_task_basic(self, task: Task) -> TaskValidationResult:
        """Validate basic task properties."""
        errors = []
        warnings = []

        if not task.name:
            errors.append("Task name is required")

        if not task.task_type:
            warnings.append("Task type not specified, using 'generic'")
            task.task_type = "generic"

        if task.priority not in TaskPriority:
            errors.append(f"Invalid task priority: {task.priority}")

        return TaskValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _validate_task_parameters(self, task: Task) -> TaskValidationResult:
        """Validate task parameters."""
        errors = []
        warnings = []

        # Check for required parameters based on task type
        required_params = self._get_required_parameters(task.task_type)
        for param in required_params:
            if param not in task.parameters:
                errors.append(f"Required parameter '{param}' missing for task type '{task.task_type}'")

        # Validate parameter types
        for key, value in task.parameters.items():
            if not self._validate_parameter_type(key, value, task.task_type):
                warnings.append(f"Parameter '{key}' may have incorrect type")

        return TaskValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _validate_task_dependencies(self, task: Task) -> TaskValidationResult:
        """Validate task dependencies."""
        errors = []
        warnings = []

        # Check for circular dependencies
        if self._has_circular_dependency(task):
            errors.append("Circular dependency detected")

        # Check if dependencies exist
        for dep_id in task.dependencies:
            if str(dep_id) not in self._task_history and str(dep_id) not in self._task_results:
                warnings.append(f"Dependency task {dep_id} not found in history")

        return TaskValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _validate_task_timeout(self, task: Task) -> TaskValidationResult:
        """Validate task timeout settings."""
        errors = []
        warnings = []

        if task.timeout_seconds is not None:
            if task.timeout_seconds <= 0:
                errors.append("Task timeout must be positive")
            elif task.timeout_seconds > self.config.execution.task_timeout_seconds:
                warnings.append(f"Task timeout ({task.timeout_seconds}s) exceeds system limit")

        return TaskValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def _get_required_parameters(self, task_type: str) -> List[str]:
        """Get required parameters for a task type."""
        required_params_map = {
            'code_analysis': ['code'],
            'refactoring': ['code', 'target'],
            'test_generation': ['code'],
            'generic': []
        }
        return required_params_map.get(task_type, [])

    def _validate_parameter_type(self, param_name: str, param_value: Any, task_type: str) -> bool:
        """Validate parameter type for a given task type."""
        # Basic type validation - can be extended
        if param_name == 'code' and not isinstance(param_value, str):
            return False
        if param_name == 'timeout' and not isinstance(param_value, (int, float)):
            return False
        return True

    def _has_circular_dependency(self, task: Task) -> bool:
        """Check if task has circular dependencies."""
        # Simple implementation - can be enhanced with graph traversal
        visited = set()

        def check_deps(task_id):
            if task_id in visited:
                return True
            visited.add(task_id)

            # Check dependencies of this task
            if str(task_id) in self._task_history:
                dep_task = self._task_history[str(task_id)]
                for dep_id in dep_task.dependencies:
                    if check_deps(dep_id):
                        return True

            visited.remove(task_id)
            return False

        return check_deps(task.id)

    async def _handle_timeout_error(self, task: Task, error: Exception) -> TaskResult:
        """Handle timeout errors."""
        self.logger.warning(f"Task {task.name} timed out")
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=f"Task timed out after {task.timeout_seconds or self.config.execution.task_timeout_seconds}s",
            metadata={'error_type': 'timeout', 'retry_recommended': True}
        )

    async def _handle_validation_error(self, task: Task, error: Exception) -> TaskResult:
        """Handle validation errors."""
        self.logger.error(f"Task {task.name} validation failed: {error}")
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=f"Validation failed: {str(error)}",
            metadata={'error_type': 'validation', 'retry_recommended': False}
        )

    async def _handle_execution_error(self, task: Task, error: Exception) -> TaskResult:
        """Handle execution errors."""
        self.logger.error(f"Task {task.name} execution failed: {error}")
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=f"Execution failed: {str(error)}",
            metadata={'error_type': 'execution', 'retry_recommended': True}
        )

    async def _handle_dependency_error(self, task: Task, error: Exception) -> TaskResult:
        """Handle dependency errors."""
        self.logger.error(f"Task {task.name} dependency error: {error}")
        return TaskResult(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error=f"Dependency error: {str(error)}",
            metadata={'error_type': 'dependency', 'retry_recommended': True}
        )

    async def initialize(self) -> None:
        """Initialize the task executor."""
        if self._is_initialized:
            return

        self.logger.info("Initializing task executor...")

        # Start worker tasks
        self._is_running = True
        max_workers = self.config.execution.max_concurrent_tasks

        for i in range(max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self._workers.append(worker)

        self._is_initialized = True
        self.logger.info(f"Task executor initialized with {max_workers} workers")

    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task with comprehensive validation and error handling."""
        if not self._is_initialized:
            raise RuntimeError("Task executor not initialized")

        self.logger.info(f"Queuing task: {task.name} ({task.id})")

        # Step 1: Comprehensive task validation
        validation_result = await self._validate_task_comprehensive(task)
        if not validation_result.is_valid:
            self.logger.error(f"Task validation failed: {validation_result.errors}")
            return await self._handle_validation_error(task, Exception("; ".join(validation_result.errors)))

        # Log warnings if any
        for warning in validation_result.warnings:
            self.logger.warning(f"Task validation warning: {warning}")

        # Step 2: Check system capacity
        if not await self._check_system_capacity():
            self.logger.warning("System at capacity, task may be delayed")

        # Step 3: Store task in history for dependency tracking
        self._task_history[str(task.id)] = task

        # Step 4: Update task status and metrics
        task.status = TaskStatus.PENDING
        self._metrics.total_tasks += 1

        # Step 5: Add to queue with priority
        priority = self._get_task_priority(task)
        try:
            await asyncio.wait_for(
                self._task_queue.put((priority, task)),
                timeout=5.0  # Prevent indefinite blocking
            )
        except asyncio.TimeoutError:
            self.logger.error(f"Failed to queue task {task.name}: queue full")
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error="Task queue is full",
                metadata={'error_type': 'queue_full'}
            )

        # Step 6: Update queue metrics
        self._update_queue_metrics()

        # Step 7: Wait for task completion with monitoring
        return await self._wait_for_task_completion_enhanced(task)

    async def _validate_task_comprehensive(self, task: Task) -> TaskValidationResult:
        """Run comprehensive validation on a task."""
        all_errors = []
        all_warnings = []

        # Run all registered validators
        for validator in self._task_validators:
            try:
                result = validator(task)
                all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)
            except Exception as e:
                all_errors.append(f"Validator error: {str(e)}")

        return TaskValidationResult(
            is_valid=len(all_errors) == 0,
            errors=all_errors,
            warnings=all_warnings
        )

    async def _check_system_capacity(self) -> bool:
        """Check if system has capacity for new tasks."""
        # Check queue size
        queue_size = self._task_queue.qsize()
        if queue_size >= self._performance_thresholds['max_queue_size']:
            return False

        # Check active tasks
        active_count = len(self._active_tasks)
        if active_count >= self.config.execution.max_concurrent_tasks:
            return False

        # Check error rate
        if self._metrics.error_rate > self._performance_thresholds['max_error_rate']:
            return False

        return True

    def _update_queue_metrics(self) -> None:
        """Update queue size metrics."""
        queue_size = self._task_queue.qsize()
        self._metrics.queue_size_history.append(queue_size)

        # Keep only last 100 entries
        if len(self._metrics.queue_size_history) > 100:
            self._metrics.queue_size_history.pop(0)

        # Update peak concurrent tasks
        active_count = len(self._active_tasks)
        if active_count > self._metrics.peak_concurrent_tasks:
            self._metrics.peak_concurrent_tasks = active_count

    async def _wait_for_task_completion_enhanced(self, task: Task) -> TaskResult:
        """Wait for task completion with event-driven approach."""
        task_id = str(task.id)
        start_time = time.time()

        # Set timeout
        timeout = task.timeout_seconds or self.config.execution.task_timeout_seconds

        try:
            # Create completion event if it doesn't exist
            if task_id not in self._task_completion_events:
                self._task_completion_events[task_id] = asyncio.Event()

            # Wait for completion with timeout
            try:
                await asyncio.wait_for(
                    self._task_completion_events[task_id].wait(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                # Cancel the task on timeout
                if task_id in self._active_tasks:
                    self._active_tasks[task_id].cancel()
                return await self._handle_timeout_error(task, Exception("Task timeout"))

            # Task completed, return result
            if task_id in self._task_results:
                result = self._task_results.pop(task_id)
                self._update_completion_metrics(result)

                # Clean up completion event
                self._task_completion_events.pop(task_id, None)

                return result
            else:
                # Task failed to produce result
                return await self._handle_execution_error(
                    task,
                    Exception("Task completed but no result found")
                )

        except Exception as e:
            self.logger.error(f"Error waiting for task completion: {e}")
            # Clean up completion event
            self._task_completion_events.pop(task_id, None)
            return await self._handle_execution_error(task, e)

    def _update_completion_metrics(self, result: TaskResult) -> None:
        """Update metrics when a task completes."""
        if result.status == TaskStatus.COMPLETED:
            self._metrics.completed_tasks += 1
            if result.execution_time:
                self._execution_times.append(result.execution_time)
                # Update average execution time
                self._metrics.average_execution_time = sum(self._execution_times) / len(self._execution_times)
        elif result.status == TaskStatus.FAILED:
            self._metrics.failed_tasks += 1
        elif result.status == TaskStatus.CANCELLED:
            self._metrics.cancelled_tasks += 1

        # Update error rate
        total_completed = self._metrics.completed_tasks + self._metrics.failed_tasks + self._metrics.cancelled_tasks
        if total_completed > 0:
            self._metrics.error_rate = (self._metrics.failed_tasks + self._metrics.cancelled_tasks) / total_completed

        self._metrics.last_updated = datetime.now()

    def _get_task_priority(self, task: Task) -> int:
        """Get numeric priority for task (lower number = higher priority)."""
        priority_map = {
            TaskPriority.CRITICAL: 1,
            TaskPriority.HIGH: 2,
            TaskPriority.NORMAL: 3,
            TaskPriority.LOW: 4
        }
        return priority_map.get(task.priority, 3)

    async def _wait_for_task_completion(self, task: Task) -> TaskResult:
        """Wait for a task to complete and return its result."""
        timeout = task.timeout_seconds or self.config.execution.task_timeout_seconds

        try:
            # Wait for task completion with timeout
            start_time = time.time()
            while str(task.id) not in self._task_results:
                if time.time() - start_time > timeout:
                    # Cancel the task if it's still active
                    if str(task.id) in self._active_tasks:
                        self._active_tasks[str(task.id)].cancel()

                    return TaskResult(
                        task_id=task.id,
                        status=TaskStatus.FAILED,
                        error=f"Task timed out after {timeout} seconds"
                    )

                await asyncio.sleep(0.1)

            return self._task_results[str(task.id)]

        except Exception as e:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )

    async def _worker(self, worker_name: str) -> None:
        """Worker coroutine that processes tasks from the queue."""
        self.logger.debug(f"Worker {worker_name} started")

        while self._is_running:
            try:
                # Get task from queue with timeout
                try:
                    priority, task = await asyncio.wait_for(
                        self._task_queue.get(),
                        timeout=1.0
                    )
                    # Log priority for debugging if needed
                    self.logger.debug(f"Worker {worker_name} got task with priority {priority}")
                except asyncio.TimeoutError:
                    continue

                # Execute the task
                await self._execute_single_task(task, worker_name)

            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")

        self.logger.debug(f"Worker {worker_name} stopped")

    async def _execute_single_task(self, task: Task, worker_name: str) -> None:
        """Execute a single task with enhanced error handling and monitoring."""
        task_id = str(task.id)
        # Ensure task_result is always bound before the finally block
        task_result: TaskResult = TaskResult(
            task_id=task.id,
            status=TaskStatus.FAILED,
            error="Task did not produce a result"
        )

        start_time = time.time()  # Initialize start_time early

        try:
            self.logger.info(f"Worker {worker_name} executing task: {task.name}")

            # Update task status and metrics
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()

            # Create task execution coroutine with timeout
            execution_task = asyncio.create_task(self._run_task_logic_enhanced(task))
            self._active_tasks[task_id] = execution_task

            # Execute the task with timeout
            timeout = task.timeout_seconds or self.config.execution.task_timeout_seconds

            try:
                result = await asyncio.wait_for(execution_task, timeout=timeout)
                execution_time = time.time() - start_time

                # Create successful result with enhanced metadata
                task_result = TaskResult(
                    task_id=task.id,
                    status=TaskStatus.COMPLETED,
                    result=result,
                    execution_time=execution_time,
                    metadata={
                        'worker': worker_name,
                        'start_time': task.started_at.isoformat() if task.started_at else None,
                        'task_type': task.task_type,
                        'priority': task.priority.name
                    }
                )

                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()

                self.logger.info(f"Task {task.name} completed successfully in {execution_time:.2f}s")

            except asyncio.TimeoutError:
                # Task timed out
                execution_task.cancel()
                execution_time = time.time() - start_time

                task_result = TaskResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    error=f"Task timed out after {timeout}s",
                    execution_time=execution_time,
                    metadata={
                        'error_type': 'timeout',
                        'worker': worker_name,
                        'timeout_seconds': timeout
                    }
                )
                task.status = TaskStatus.FAILED

                self.logger.warning(f"Task {task.name} timed out after {timeout}s")

        except asyncio.CancelledError:
            # Task was cancelled
            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.CANCELLED,
                error="Task was cancelled",
                metadata={
                    'error_type': 'cancelled',
                    'worker': worker_name
                }
            )
            task.status = TaskStatus.CANCELLED

            self.logger.info(f"Task {task.name} was cancelled")

        except Exception as e:
            # Task failed with unexpected error
            execution_time = time.time() - start_time if 'start_time' in locals() else 0
            error_details = traceback.format_exc()

            self.logger.error(f"Task {task.name} failed with error: {e}")
            self.logger.debug(f"Task {task.name} error details: {error_details}")

            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e),
                execution_time=execution_time,
                metadata={
                    'error_type': 'execution_error',
                    'worker': worker_name,
                    'error_details': error_details,
                    'retry_recommended': self._should_retry_task(task, e)
                }
            )
            task.status = TaskStatus.FAILED

            # Update error tracking
            error_type = type(e).__name__
            self._error_counts[error_type] = self._error_counts.get(error_type, 0) + 1

        finally:
            # Clean up and store result
            if task_id in self._active_tasks:
                del self._active_tasks[task_id]

            self._task_results[task_id] = task_result

            # Signal task completion event
            if task_id in self._task_completion_events:
                self._task_completion_events[task_id].set()

            # Clean up old results to prevent memory leaks
            await self._cleanup_old_results()

    async def _run_task_logic_enhanced(self, task: Task) -> str:
        """Run task logic with enhanced monitoring and validation."""
        # Pre-execution validation
        if not self._validate_task_execution_context(task):
            raise ValueError("Task execution context validation failed")

        # Run the actual task logic
        result = await self._run_task_logic(task)

        # Post-execution validation
        if not self._validate_task_result(result, task):
            raise ValueError("Task result validation failed")

        return result

    def _validate_task_execution_context(self, task: Task) -> bool:
        """Validate the context before task execution."""
        # Check if required parameters are present
        required_params = self._get_required_parameters(task.task_type)
        for param in required_params:
            if param not in task.parameters:
                self.logger.error(f"Missing required parameter: {param}")
                return False

        # Note: We don't check concurrent task limits here because the task
        # has already been queued and the worker system manages concurrency
        # The concurrent task check is done at submission time in execute_task()

        return True

    def _validate_task_result(self, result: Any, task: Task) -> bool:
        """Validate task execution result."""
        if result is None:
            self.logger.warning(f"Task {task.name} returned None result")
            return False

        # Task-specific result validation
        if task.task_type == "code_analysis" and not isinstance(result, str):
            return False

        return True

    def _should_retry_task(self, task: Task, error: Exception) -> bool:
        """Determine if a task should be retried based on the error."""
        # Don't retry validation errors
        if isinstance(error, ValueError):
            return False

        # Don't retry if max retries reached
        if task.retry_count >= task.max_retries:
            return False

        # Retry timeout errors and temporary failures
        if isinstance(error, (asyncio.TimeoutError, ConnectionError)):
            return True

        # Don't retry by default
        return False

    async def _run_task_logic(self, task: Task) -> str:
        """Run the actual task logic."""
        # This is a placeholder implementation
        # In a real system, this would dispatch to appropriate handlers
        # based on task type

        if task.task_type == "code_analysis":
            return await self._run_code_analysis_task(task)
        elif task.task_type == "refactoring":
            return await self._run_refactoring_task(task)
        elif task.task_type == "test_generation":
            return await self._run_test_generation_task(task)
        else:
            # Generic task execution
            await asyncio.sleep(0.1)  # Simulate work
            return f"Task {task.name} completed successfully"

    async def _run_code_analysis_task(self, task: Task) -> str:
        """Run a code analysis task."""
        # Reference task minimally to avoid 'unused parameter' diagnostics in strict linters
        _ = task
        await asyncio.sleep(0.5)  # Simulate analysis work
        return "Code analysis completed"

    async def _run_refactoring_task(self, task: Task) -> str:
        """Run a refactoring task."""
        _ = task
        await asyncio.sleep(1.0)  # Simulate refactoring work
        return "Refactoring suggestions generated"

    async def _run_test_generation_task(self, task: Task) -> str:
        """Run a test generation task."""
        _ = task
        await asyncio.sleep(0.8)  # Simulate test generation work
        return "Unit tests generated"

    async def _cleanup_old_results(self) -> None:
        """Clean up old task results to prevent memory leaks."""
        # Keep only the last 1000 results
        if len(self._task_results) > 1000:
            # Remove oldest results
            sorted_results = sorted(
                self._task_results.items(),
                key=lambda x: x[1].created_at
            )

            for task_id, _ in sorted_results[:-1000]:
                del self._task_results[task_id]

    async def get_active_task_count(self) -> int:
        """Get the number of currently active tasks."""
        return len(self._active_tasks)

    async def get_queue_size(self) -> int:
        """Get the current queue size."""
        return self._task_queue.qsize()

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel an active task."""
        if task_id in self._active_tasks:
            self._active_tasks[task_id].cancel()
            return True
        return False

    async def shutdown(self) -> None:
        """Shutdown the task executor."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down task executor...")

        # Stop accepting new tasks
        self._is_running = False
        self._shutdown_event.set()

        # Cancel all active tasks
        for task_id, task in self._active_tasks.items():
            # Ensure both variables are 'used' to satisfy strict linters and translations
            _ = task_id
            task.cancel()

        # Wait for workers to finish
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)

        # Clear state
        self._active_tasks.clear()
        self._task_results.clear()
        self._workers.clear()

        self._is_initialized = False
        self.logger.info("Task executor shutdown complete")

    # Enhanced monitoring and metrics methods

    def get_metrics(self) -> TaskExecutionMetrics:
        """Get current execution metrics."""
        return self._metrics

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            'is_running': self._is_running,
            'active_tasks': len(self._active_tasks),
            'queue_size': self._task_queue.qsize(),
            'total_workers': len(self._workers),
            'metrics': {
                'total_tasks': self._metrics.total_tasks,
                'completed_tasks': self._metrics.completed_tasks,
                'failed_tasks': self._metrics.failed_tasks,
                'cancelled_tasks': self._metrics.cancelled_tasks,
                'error_rate': self._metrics.error_rate,
                'average_execution_time': self._metrics.average_execution_time,
                'peak_concurrent_tasks': self._metrics.peak_concurrent_tasks
            },
            'error_counts': dict(self._error_counts),
            'performance_thresholds': self._performance_thresholds,
            'last_updated': self._metrics.last_updated.isoformat()
        }

    def reset_metrics(self) -> None:
        """Reset all metrics to initial state."""
        self._metrics = TaskExecutionMetrics()
        self._execution_times.clear()
        self._error_counts.clear()
        self.logger.info("Task execution metrics reset")

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        health_status = {
            'status': 'healthy',
            'issues': [],
            'warnings': []
        }

        # Check if executor is running
        if not self._is_running:
            health_status['issues'].append('Task executor is not running')
            health_status['status'] = 'unhealthy'

        # Check queue size
        queue_size = self._task_queue.qsize()
        if queue_size >= self._performance_thresholds['max_queue_size']:
            health_status['warnings'].append(f'Queue size ({queue_size}) approaching limit')
            if queue_size >= self.config.execution.queue_max_size * 0.95:
                health_status['status'] = 'degraded'

        # Check error rate
        if self._metrics.error_rate > self._performance_thresholds['max_error_rate']:
            health_status['issues'].append(f'Error rate ({self._metrics.error_rate:.2%}) exceeds threshold')
            health_status['status'] = 'degraded'

        # Check worker status
        dead_workers = [w for w in self._workers if w.done()]
        if dead_workers:
            health_status['issues'].append(f'{len(dead_workers)} workers have died')
            health_status['status'] = 'degraded'

        # Check average execution time
        if (self._metrics.average_execution_time >
            self._performance_thresholds['max_execution_time'] * 0.8):
            health_status['warnings'].append('Average execution time is high')

        return health_status

    def configure_performance_thresholds(self, **thresholds) -> None:
        """Configure performance monitoring thresholds."""
        for key, value in thresholds.items():
            if key in self._performance_thresholds:
                self._performance_thresholds[key] = value
                self.logger.info(f"Updated performance threshold {key} to {value}")
            else:
                self.logger.warning(f"Unknown performance threshold: {key}")

    async def graceful_shutdown_with_timeout(self, timeout_seconds: float = 30.0) -> bool:
        """Gracefully shutdown with a timeout, returning success status."""
        try:
            await asyncio.wait_for(self.shutdown(), timeout=timeout_seconds)
            return True
        except asyncio.TimeoutError:
            self.logger.error(f"Graceful shutdown timed out after {timeout_seconds}s")
            # Force shutdown
            for worker in self._workers:
                if not worker.done():
                    worker.cancel()
            return False