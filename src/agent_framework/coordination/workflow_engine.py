"""
Workflow engine for orchestrating complex multi-agent workflows.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from ..core.types import Task, TaskResult, TaskStatus, TaskPriority


class WorkflowStatus(Enum):
    """Status of a workflow execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowStep:
    """A single step in a workflow."""
    id: str
    name: str
    task_template: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    agents: List[str] = field(default_factory=list)
    timeout_seconds: float = 300.0
    retry_count: int = 0
    max_retries: int = 3
    condition: Optional[Callable] = None  # Condition to execute this step


@dataclass
class Workflow:
    """A workflow definition."""
    id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    global_timeout_seconds: float = 1800.0  # 30 minutes
    created_at: datetime = field(default_factory=datetime.now)


class WorkflowEngine:
    """
    Engine for executing complex multi-agent workflows.
    """
    
    def __init__(self):
        """Initialize the workflow engine."""
        self.logger = logging.getLogger(__name__)
        
        # Workflow tracking
        self._workflows: Dict[str, Workflow] = {}
        self._executions: Dict[str, Dict[str, Any]] = {}
        
        # Step execution tracking
        self._step_results: Dict[str, Dict[str, TaskResult]] = {}  # execution_id -> step_id -> result
        
    def register_workflow(self, workflow: Workflow):
        """Register a workflow definition."""
        self._workflows[workflow.id] = workflow
        self.logger.info(f"Registered workflow: {workflow.name}")
    
    async def execute_workflow(self, 
                              workflow_id: str, 
                              execution_id: str,
                              initial_context: Dict[str, Any] = None) -> str:
        """
        Execute a workflow.
        
        Args:
            workflow_id: ID of the workflow to execute
            execution_id: Unique ID for this execution
            initial_context: Initial context data for the workflow
            
        Returns:
            The execution ID
        """
        if workflow_id not in self._workflows:
            raise ValueError(f"Unknown workflow: {workflow_id}")
        
        if execution_id in self._executions:
            raise ValueError(f"Execution {execution_id} already exists")
        
        workflow = self._workflows[workflow_id]
        
        # Initialize execution state
        self._executions[execution_id] = {
            'workflow_id': workflow_id,
            'status': WorkflowStatus.PENDING,
            'started_at': datetime.now(),
            'context': initial_context or {},
            'completed_steps': set(),
            'failed_steps': set(),
            'current_step': None
        }
        
        self._step_results[execution_id] = {}
        
        self.logger.info(f"Starting workflow execution {execution_id} for workflow {workflow.name}")
        
        # Start execution task
        asyncio.create_task(self._execute_workflow_steps(execution_id))
        
        return execution_id
    
    async def _execute_workflow_steps(self, execution_id: str):
        """Execute workflow steps."""
        try:
            execution = self._executions[execution_id]
            workflow = self._workflows[execution['workflow_id']]
            
            execution['status'] = WorkflowStatus.RUNNING
            
            # Create dependency graph
            dependency_graph = self._build_dependency_graph(workflow.steps)
            
            # Execute steps in dependency order
            while True:
                # Find steps that can be executed (dependencies satisfied)
                ready_steps = self._find_ready_steps(
                    workflow.steps, 
                    execution['completed_steps'],
                    execution['failed_steps']
                )
                
                if not ready_steps:
                    # Check if all steps are completed
                    if len(execution['completed_steps']) == len(workflow.steps):
                        execution['status'] = WorkflowStatus.COMPLETED
                        execution['completed_at'] = datetime.now()
                        self.logger.info(f"Workflow execution {execution_id} completed successfully")
                        break
                    else:
                        # Some steps failed or are blocked
                        execution['status'] = WorkflowStatus.FAILED
                        execution['completed_at'] = datetime.now()
                        self.logger.error(f"Workflow execution {execution_id} failed - no more steps can execute")
                        break
                
                # Execute ready steps concurrently
                step_tasks = []
                for step in ready_steps:
                    task = asyncio.create_task(self._execute_step(execution_id, step))
                    step_tasks.append(task)
                
                # Wait for all steps to complete
                await asyncio.gather(*step_tasks, return_exceptions=True)
                
                # Small delay before checking for next steps
                await asyncio.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"Error executing workflow {execution_id}: {e}")
            execution['status'] = WorkflowStatus.FAILED
            execution['error'] = str(e)
            execution['completed_at'] = datetime.now()
    
    def _build_dependency_graph(self, steps: List[WorkflowStep]) -> Dict[str, List[str]]:
        """Build dependency graph for workflow steps."""
        graph = {}
        for step in steps:
            graph[step.id] = step.dependencies.copy()
        return graph
    
    def _find_ready_steps(self, 
                         steps: List[WorkflowStep], 
                         completed_steps: set, 
                         failed_steps: set) -> List[WorkflowStep]:
        """Find steps that are ready to execute."""
        ready_steps = []
        
        for step in steps:
            # Skip if already completed or failed
            if step.id in completed_steps or step.id in failed_steps:
                continue
            
            # Check if all dependencies are satisfied
            dependencies_satisfied = all(
                dep_id in completed_steps for dep_id in step.dependencies
            )
            
            if dependencies_satisfied:
                # Check condition if present
                if step.condition is None or step.condition():
                    ready_steps.append(step)
        
        return ready_steps
    
    async def _execute_step(self, execution_id: str, step: WorkflowStep):
        """Execute a single workflow step."""
        try:
            execution = self._executions[execution_id]
            execution['current_step'] = step.id
            
            self.logger.info(f"Executing step {step.name} in workflow {execution_id}")
            
            # Create task from template
            task = self._create_task_from_template(step, execution['context'])
            
            # Execute the task (this would integrate with the task executor)
            # For now, simulate task execution
            await asyncio.sleep(0.1)  # Simulate work
            
            # Create mock result
            result = TaskResult(
                task_id=task.id,
                status=TaskStatus.COMPLETED,
                result=f"Step {step.name} completed successfully",
                execution_time=0.1
            )
            
            # Store result
            self._step_results[execution_id][step.id] = result
            execution['completed_steps'].add(step.id)
            
            # Update context with step result
            execution['context'][f'step_{step.id}_result'] = result.result
            
            self.logger.info(f"Step {step.name} completed successfully")
            
        except Exception as e:
            self.logger.error(f"Step {step.name} failed: {e}")
            
            execution = self._executions[execution_id]
            execution['failed_steps'].add(step.id)
            
            # Create failure result
            result = TaskResult(
                task_id=step.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
            
            self._step_results[execution_id][step.id] = result
            
            # Check if we should retry
            if step.retry_count < step.max_retries:
                step.retry_count += 1
                self.logger.info(f"Retrying step {step.name} (attempt {step.retry_count})")
                
                # Remove from failed steps to allow retry
                execution['failed_steps'].discard(step.id)
                
                # Schedule retry
                await asyncio.sleep(1.0)  # Brief delay before retry
                await self._execute_step(execution_id, step)
    
    def _create_task_from_template(self, step: WorkflowStep, context: Dict[str, Any]) -> Task:
        """Create a task from a step template."""
        # Replace template variables in task parameters
        task_params = {}
        for key, value in step.task_template.get('parameters', {}).items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # Template variable
                var_name = value[2:-1]
                task_params[key] = context.get(var_name, value)
            else:
                task_params[key] = value
        
        return Task(
            name=step.task_template.get('name', step.name),
            task_type=step.task_template.get('type', 'generic'),
            priority=TaskPriority(step.task_template.get('priority', 'NORMAL')),
            parameters=task_params,
            timeout_seconds=step.timeout_seconds
        )
    
    def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a workflow execution."""
        if execution_id not in self._executions:
            return None
        
        execution = dict(self._executions[execution_id])
        execution['status'] = execution['status'].value  # Convert enum to string
        
        # Add step results
        execution['step_results'] = {}
        if execution_id in self._step_results:
            for step_id, result in self._step_results[execution_id].items():
                execution['step_results'][step_id] = {
                    'status': result.status.value,
                    'result': result.result,
                    'error': result.error,
                    'execution_time': result.execution_time
                }
        
        return execution
    
    def cancel_execution(self, execution_id: str) -> bool:
        """Cancel a workflow execution."""
        if execution_id not in self._executions:
            return False
        
        execution = self._executions[execution_id]
        if execution['status'] in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED]:
            return False
        
        execution['status'] = WorkflowStatus.CANCELLED
        execution['completed_at'] = datetime.now()
        
        self.logger.info(f"Cancelled workflow execution {execution_id}")
        return True
    
    def get_workflow_definitions(self) -> Dict[str, Dict[str, Any]]:
        """Get all registered workflow definitions."""
        definitions = {}
        for workflow_id, workflow in self._workflows.items():
            definitions[workflow_id] = {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'steps': [
                    {
                        'id': step.id,
                        'name': step.name,
                        'dependencies': step.dependencies,
                        'agents': step.agents
                    }
                    for step in workflow.steps
                ],
                'created_at': workflow.created_at.isoformat()
            }
        return definitions
