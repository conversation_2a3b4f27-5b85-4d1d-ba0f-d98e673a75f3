"""
Circuit breaker pattern implementation for resilience.

Provides circuit breaker, retry, timeout, and bulkhead patterns
for protecting against cascading failures and improving system resilience.
"""

import time
import asyncio
import logging
from typing import Any, Callable, Optional, Dict, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from functools import wraps
import random

try:
    from tenacity import (
        retry, stop_after_attempt, wait_exponential, 
        retry_if_exception_type, before_sleep_log
    )
    TENACITY_AVAILABLE = True
except ImportError:
    TENACITY_AVAILABLE = False


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, calls fail fast
    HALF_OPEN = "half_open"  # Testing if service has recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    expected_exception: type = Exception
    success_threshold: int = 3  # For half-open state
    timeout: float = 30.0
    name: str = "default"


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    timeouts: int = 0
    circuit_opens: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    current_state: CircuitState = CircuitState.CLOSED
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate."""
        if self.total_calls == 0:
            return 0.0
        return self.failed_calls / self.total_calls
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_calls == 0:
            return 0.0
        return self.successful_calls / self.total_calls


class CircuitBreakerError(Exception):
    """Circuit breaker specific exception."""
    pass


class CircuitBreakerOpenError(CircuitBreakerError):
    """Exception raised when circuit breaker is open."""
    pass


class CircuitBreaker:
    """
    Circuit breaker implementation with automatic recovery.
    
    Features:
    - Automatic failure detection and circuit opening
    - Configurable failure thresholds and timeouts
    - Half-open state for testing recovery
    - Comprehensive statistics and monitoring
    - Integration with metrics and logging
    """
    
    def __init__(self, config: CircuitBreakerConfig):
        """Initialize circuit breaker."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
        
        # State management
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time: Optional[float] = None
        self._next_attempt_time: Optional[float] = None
        
        # Statistics
        self._stats = CircuitBreakerStats()
        
        # Thread safety
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        async with self._lock:
            # Check circuit state
            await self._update_state()
            
            if self._state == CircuitState.OPEN:
                self._stats.total_calls += 1
                raise CircuitBreakerOpenError(
                    f"Circuit breaker '{self.config.name}' is open"
                )
            
            # Execute function
            start_time = time.time()
            try:
                # Apply timeout
                result = await asyncio.wait_for(
                    func(*args, **kwargs) if asyncio.iscoroutinefunction(func) 
                    else asyncio.create_task(asyncio.to_thread(func, *args, **kwargs)),
                    timeout=self.config.timeout
                )
                
                # Record success
                await self._record_success()
                return result
                
            except asyncio.TimeoutError:
                await self._record_timeout()
                raise
            except self.config.expected_exception as e:
                await self._record_failure()
                raise
            except Exception as e:
                # Unexpected exception - don't count as failure
                self._stats.total_calls += 1
                raise
    
    async def _update_state(self) -> None:
        """Update circuit breaker state based on current conditions."""
        current_time = time.time()
        
        if self._state == CircuitState.OPEN:
            # Check if recovery timeout has passed
            if (self._next_attempt_time and 
                current_time >= self._next_attempt_time):
                self._state = CircuitState.HALF_OPEN
                self._success_count = 0
                self.logger.info(f"Circuit breaker '{self.config.name}' moved to HALF_OPEN")
        
        elif self._state == CircuitState.HALF_OPEN:
            # Check if we've had enough successes to close
            if self._success_count >= self.config.success_threshold:
                self._state = CircuitState.CLOSED
                self._failure_count = 0
                self.logger.info(f"Circuit breaker '{self.config.name}' moved to CLOSED")
    
    async def _record_success(self) -> None:
        """Record a successful call."""
        self._stats.total_calls += 1
        self._stats.successful_calls += 1
        self._stats.last_success_time = datetime.now()
        
        if self._state == CircuitState.HALF_OPEN:
            self._success_count += 1
        elif self._state == CircuitState.CLOSED:
            # Reset failure count on success
            self._failure_count = 0
    
    async def _record_failure(self) -> None:
        """Record a failed call."""
        self._stats.total_calls += 1
        self._stats.failed_calls += 1
        self._stats.last_failure_time = datetime.now()
        self._last_failure_time = time.time()
        
        self._failure_count += 1
        
        # Check if we should open the circuit
        if (self._state == CircuitState.CLOSED and 
            self._failure_count >= self.config.failure_threshold):
            await self._open_circuit()
        elif self._state == CircuitState.HALF_OPEN:
            # Any failure in half-open state reopens the circuit
            await self._open_circuit()
    
    async def _record_timeout(self) -> None:
        """Record a timeout."""
        self._stats.total_calls += 1
        self._stats.failed_calls += 1
        self._stats.timeouts += 1
        self._stats.last_failure_time = datetime.now()
        self._last_failure_time = time.time()
        
        self._failure_count += 1
        
        # Timeouts count as failures for circuit breaking
        if (self._state == CircuitState.CLOSED and 
            self._failure_count >= self.config.failure_threshold):
            await self._open_circuit()
        elif self._state == CircuitState.HALF_OPEN:
            await self._open_circuit()
    
    async def _open_circuit(self) -> None:
        """Open the circuit breaker."""
        self._state = CircuitState.OPEN
        self._stats.circuit_opens += 1
        self._stats.current_state = CircuitState.OPEN
        self._next_attempt_time = time.time() + self.config.recovery_timeout
        
        self.logger.warning(
            f"Circuit breaker '{self.config.name}' opened after "
            f"{self._failure_count} failures"
        )
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get circuit breaker statistics."""
        stats = self._stats
        stats.current_state = self._state
        return stats
    
    def reset(self) -> None:
        """Reset circuit breaker to closed state."""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time = None
        self._next_attempt_time = None
        self.logger.info(f"Circuit breaker '{self.config.name}' reset")
    
    @property
    def state(self) -> CircuitState:
        """Get current circuit state."""
        return self._state


class CircuitBreakerManager:
    """
    Manager for multiple circuit breakers.
    
    Provides centralized management, monitoring, and configuration
    of circuit breakers across the application.
    """
    
    def __init__(self):
        """Initialize circuit breaker manager."""
        self.logger = logging.getLogger(__name__)
        self._circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._default_config = CircuitBreakerConfig()
    
    def create_circuit_breaker(self, 
                              name: str, 
                              config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """Create and register a circuit breaker."""
        if config is None:
            config = CircuitBreakerConfig(name=name)
        else:
            config.name = name
        
        circuit_breaker = CircuitBreaker(config)
        self._circuit_breakers[name] = circuit_breaker
        
        self.logger.info(f"Created circuit breaker: {name}")
        return circuit_breaker
    
    def get_circuit_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """Get circuit breaker by name."""
        return self._circuit_breakers.get(name)
    
    def remove_circuit_breaker(self, name: str) -> bool:
        """Remove circuit breaker."""
        if name in self._circuit_breakers:
            del self._circuit_breakers[name]
            self.logger.info(f"Removed circuit breaker: {name}")
            return True
        return False
    
    def get_all_stats(self) -> Dict[str, CircuitBreakerStats]:
        """Get statistics for all circuit breakers."""
        return {
            name: cb.get_stats() 
            for name, cb in self._circuit_breakers.items()
        }
    
    def reset_all(self) -> None:
        """Reset all circuit breakers."""
        for cb in self._circuit_breakers.values():
            cb.reset()
        self.logger.info("Reset all circuit breakers")
    
    def circuit_breaker(self, 
                       name: str, 
                       config: Optional[CircuitBreakerConfig] = None):
        """Decorator for applying circuit breaker to functions."""
        def decorator(func):
            # Create circuit breaker if it doesn't exist
            if name not in self._circuit_breakers:
                self.create_circuit_breaker(name, config)
            
            cb = self._circuit_breakers[name]
            
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await cb.call(func, *args, **kwargs)
            
            return wrapper
        return decorator


class RetryManager:
    """
    Retry manager with exponential backoff and jitter.
    
    Provides configurable retry logic with circuit breaker integration.
    """
    
    def __init__(self):
        """Initialize retry manager."""
        self.logger = logging.getLogger(__name__)
    
    def retry_with_backoff(self,
                          max_attempts: int = 3,
                          base_delay: float = 1.0,
                          max_delay: float = 60.0,
                          exponential_base: float = 2.0,
                          jitter: bool = True,
                          retry_exceptions: tuple = (Exception,)):
        """Decorator for retry with exponential backoff."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(max_attempts):
                    try:
                        return await func(*args, **kwargs)
                    except retry_exceptions as e:
                        last_exception = e
                        
                        if attempt == max_attempts - 1:
                            # Last attempt failed
                            break
                        
                        # Calculate delay
                        delay = min(
                            base_delay * (exponential_base ** attempt),
                            max_delay
                        )
                        
                        # Add jitter
                        if jitter:
                            delay *= (0.5 + random.random() * 0.5)
                        
                        self.logger.warning(
                            f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}"
                        )
                        
                        await asyncio.sleep(delay)
                
                # All attempts failed
                raise last_exception
            
            return wrapper
        return decorator


class BulkheadManager:
    """
    Bulkhead pattern implementation for resource isolation.
    
    Provides semaphore-based resource isolation to prevent
    resource exhaustion from affecting other operations.
    """
    
    def __init__(self):
        """Initialize bulkhead manager."""
        self.logger = logging.getLogger(__name__)
        self._semaphores: Dict[str, asyncio.Semaphore] = {}
    
    def create_bulkhead(self, name: str, max_concurrent: int) -> asyncio.Semaphore:
        """Create a bulkhead semaphore."""
        semaphore = asyncio.Semaphore(max_concurrent)
        self._semaphores[name] = semaphore
        self.logger.info(f"Created bulkhead '{name}' with {max_concurrent} slots")
        return semaphore
    
    def get_bulkhead(self, name: str) -> Optional[asyncio.Semaphore]:
        """Get bulkhead semaphore by name."""
        return self._semaphores.get(name)
    
    def bulkhead(self, name: str, max_concurrent: int):
        """Decorator for applying bulkhead pattern."""
        def decorator(func):
            # Create bulkhead if it doesn't exist
            if name not in self._semaphores:
                self.create_bulkhead(name, max_concurrent)
            
            semaphore = self._semaphores[name]
            
            @wraps(func)
            async def wrapper(*args, **kwargs):
                async with semaphore:
                    return await func(*args, **kwargs)
            
            return wrapper
        return decorator


# Global instances
circuit_breaker_manager = CircuitBreakerManager()
retry_manager = RetryManager()
bulkhead_manager = BulkheadManager()


# Convenience decorators
def circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None):
    """Convenience decorator for circuit breaker."""
    return circuit_breaker_manager.circuit_breaker(name, config)


def retry_with_backoff(**kwargs):
    """Convenience decorator for retry with backoff."""
    return retry_manager.retry_with_backoff(**kwargs)


def bulkhead(name: str, max_concurrent: int):
    """Convenience decorator for bulkhead pattern."""
    return bulkhead_manager.bulkhead(name, max_concurrent)
