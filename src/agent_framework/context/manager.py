"""
Context manager for intelligent codebase understanding and context retention.
"""

import logging
from typing import Any, Dict, List
from uuid import uuid4

from ..core.config import FrameworkConfig
from ..core.types import ContextQuery, Context


class ContextManager:
    """
    Context manager for intelligent codebase understanding and context retention.

    Provides context storage, retrieval, and analysis capabilities
    for maintaining session state and codebase understanding.
    """

    def __init__(self, config: FrameworkConfig, message_broker=None):
        """Initialize the context manager."""
        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # In-memory context store (would be replaced with persistent storage)
        self._context_store: Dict[str, Any] = {}
        self._session_contexts: Dict[str, Dict[str, Any]] = {}

        self._is_initialized = False

    async def initialize(self) -> None:
        """Initialize the context manager."""
        if self._is_initialized:
            return

        self.logger.info("Initializing context manager...")

        # Initialize context storage backend
        await self._initialize_storage()

        self._is_initialized = True
        self.logger.info("Context manager initialized")

    async def _initialize_storage(self) -> None:
        """Initialize the context storage backend."""
        # For now, use in-memory storage
        # In production, this would initialize SQLite, PostgreSQL, etc.
        self.logger.info("Using in-memory context storage")

    async def get_context(self, query: ContextQuery) -> Context:
        """Retrieve context information based on a query."""
        self.logger.debug(f"Getting context for query: {query.query_type}")

        query_id = str(uuid4())
        results = []

        try:
            if query.query_type == "codebase":
                results = await self._get_codebase_context(query)
            elif query.query_type == "session":
                results = await self._get_session_context(query)
            elif query.query_type == "global":
                results = await self._get_global_context(query)
            else:
                self.logger.warning(f"Unknown query type: {query.query_type}")

            return Context(
                query_id=query_id,
                results=results[:query.max_results],
                metadata={
                    "query_type": query.query_type,
                    "total_results": len(results),
                    "scope": query.scope
                }
            )

        except Exception as e:
            self.logger.error(f"Error retrieving context: {e}")
            return Context(
                query_id=query_id,
                results=[],
                metadata={"error": str(e)}
            )

    async def _get_codebase_context(self, query: ContextQuery) -> List[Dict[str, Any]]:
        """Get codebase-related context."""
        # Placeholder implementation
        # In production, this would analyze code structure, dependencies, etc.
        return [
            {
                "type": "codebase_info",
                "content": "Codebase context placeholder",
                "relevance": 0.8
            }
        ]

    async def _get_session_context(self, query: ContextQuery) -> List[Dict[str, Any]]:
        """Get session-specific context."""
        session_id = query.parameters.get("session_id", "default")
        session_context = self._session_contexts.get(session_id, {})

        results = []
        for key, value in session_context.items():
            results.append({
                "type": "session_data",
                "key": key,
                "content": value,
                "relevance": 1.0
            })

        return results

    async def _get_global_context(self, query: ContextQuery) -> List[Dict[str, Any]]:
        """Get global context information."""
        results = []
        for key, value in self._context_store.items():
            results.append({
                "type": "global_data",
                "key": key,
                "content": value,
                "relevance": 0.5
            })

        return results

    async def store_context(self, key: str, value: Any, scope: str = "global") -> None:
        """Store context information."""
        if scope == "global":
            self._context_store[key] = value
        else:
            # Session-specific storage
            if scope not in self._session_contexts:
                self._session_contexts[scope] = {}
            self._session_contexts[scope][key] = value

        self.logger.debug(f"Stored context: {key} in scope {scope}")

    async def clear_context(self, scope: str = "global") -> None:
        """Clear context for a specific scope."""
        if scope == "global":
            self._context_store.clear()
        elif scope in self._session_contexts:
            del self._session_contexts[scope]

        self.logger.info(f"Cleared context for scope: {scope}")

    async def get_context_stats(self) -> Dict[str, Any]:
        """Get context storage statistics."""
        return {
            "global_entries": len(self._context_store),
            "session_count": len(self._session_contexts),
            "total_session_entries": sum(
                len(ctx) for ctx in self._session_contexts.values()
            )
        }

    async def shutdown(self) -> None:
        """Shutdown the context manager."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down context manager...")

        # Clear all context data
        self._context_store.clear()
        self._session_contexts.clear()

        self._is_initialized = False
        self.logger.info("Context manager shutdown complete")