"""
Context manager for intelligent codebase understanding and context retention.
"""

from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4
from dataclasses import dataclass
from datetime import datetime

from ..core.config import FrameworkConfig

@dataclass
class ContextQuery:
    """Query for context information."""
    query_type: str  # 'codebase', 'session', 'global'
    query: str
    scope: Optional[str]
    max_results: int
    filters: Dict[str, Any]

@dataclass
class ContextResult:
    """Result from context query."""
    content: str
    relevance_score: float
    source: str
    metadata: Dict[str, Any]
    timestamp: datetime

@dataclass
class Context:
    """Context information container."""
    query_id: str
    results: List[ContextResult]
    metadata: Dict[str, Any]

class ContextManager:
    """
    Context manager for intelligent codebase understanding and context retention.

    Provides context storage, retrieval, and analysis capabilities
    for maintaining session state and codebase understanding.
    """
    
    def __init__(self, config: FrameworkConfig, message_broker: Any = None) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the context manager."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the context manager."""
        ...
    
    async def get_context(self, query: ContextQuery) -> Context:
        """Retrieve context information based on a query."""
        ...
    
    async def store_context(self, 
                           context_type: str,
                           content: str,
                           metadata: Optional[Dict[str, Any]] = None) -> str:
        """Store context information."""
        ...
    
    async def update_context(self, context_id: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Update existing context."""
        ...
    
    async def delete_context(self, context_id: str) -> bool:
        """Delete context information."""
        ...
    
    async def search_context(self, 
                            query: str,
                            context_type: Optional[str] = None,
                            max_results: int = 10) -> List[ContextResult]:
        """Search for context information."""
        ...
    
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """Get context for a specific session."""
        ...
    
    async def update_session_context(self, session_id: str, context: Dict[str, Any]) -> None:
        """Update session context."""
        ...
    
    async def clear_session_context(self, session_id: str) -> None:
        """Clear session context."""
        ...
    
    async def get_codebase_context(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get context for a specific file."""
        ...
    
    async def update_codebase_context(self, file_path: str, context: Dict[str, Any]) -> None:
        """Update codebase context."""
        ...
    
    async def analyze_code_relationships(self, file_paths: List[str]) -> Dict[str, Any]:
        """Analyze relationships between code files."""
        ...
    
    async def get_context_statistics(self) -> Dict[str, Any]:
        """Get context storage statistics."""
        ...
    
    async def cleanup_old_context(self, days: int = 30) -> int:
        """Cleanup old context entries."""
        ...
    
    # Properties
    @property
    def config(self) -> FrameworkConfig: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def context_count(self) -> int: ...
