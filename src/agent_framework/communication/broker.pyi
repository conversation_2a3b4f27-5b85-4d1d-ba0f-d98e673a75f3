"""
Event-driven message broker for component communication.
"""

import asyncio
from typing import Any, Dict, List, Optional, Callable, Awaitable
from collections import defaultdict

from ..core.config import FrameworkConfig
from ..core.types import AgentEvent

EventHandler = Callable[[AgentEvent], Awaitable[None]]

class MessageBroker:
    """
    Event-driven message broker for component communication.

    Provides publish-subscribe messaging for loose coupling
    between framework components.
    """
    
    def __init__(self, config: FrameworkConfig) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the message broker."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the message broker."""
        ...
    
    async def publish_event(self, event: AgentEvent) -> None:
        """Publish an event to all subscribers."""
        ...
    
    def subscribe(self, event_type: str, handler: EventHandler) -> None:
        """Subscribe to events of a specific type."""
        ...
    
    def unsubscribe(self, event_type: str, handler: <PERSON>Handler) -> None:
        """Unsubscribe from events."""
        ...
    
    def subscribe_all(self, handler: <PERSON><PERSON><PERSON><PERSON>) -> None:
        """Subscribe to all events."""
        ...
    
    def unsubscribe_all(self, handler: Event<PERSON>andler) -> None:
        """Unsubscribe from all events."""
        ...
    
    async def request_response(self, 
                              event: AgentEvent,
                              timeout: float = 30.0) -> Optional[AgentEvent]:
        """Send a request and wait for response."""
        ...
    
    def get_subscribers(self, event_type: str) -> List[EventHandler]:
        """Get subscribers for an event type."""
        ...
    
    def get_all_subscribers(self) -> Dict[str, List[EventHandler]]:
        """Get all subscribers."""
        ...
    
    async def get_broker_stats(self) -> Dict[str, Any]:
        """Get broker statistics."""
        ...
    
    # Properties
    @property
    def config(self) -> FrameworkConfig: ...
    
    @property
    def is_running(self) -> bool: ...
    
    @property
    def event_count(self) -> int: ...
    
    @property
    def subscriber_count(self) -> int: ...
