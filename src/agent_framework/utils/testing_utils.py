"""
Testing utilities for the agent framework.

This module provides utilities for testing, including temporary file/directory
creation, async function mocking, and testing helpers.
"""

import os
import tempfile
import shutil
import asyncio
import time
from typing import Any, Callable, Optional, Union, Awaitable, List, Tuple
from unittest.mock import AsyncMock, MagicMock
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def create_temp_file(content: str = "", suffix: str = ".txt", prefix: str = "test_") -> str:
    """
    Create a temporary file with optional content.
    
    Args:
        content: Content to write to the file
        suffix: File suffix/extension
        prefix: File prefix
        
    Returns:
        Path to the created temporary file
    """
    fd, filepath = tempfile.mkstemp(suffix=suffix, prefix=prefix)
    try:
        with os.fdopen(fd, 'w', encoding='utf-8') as f:
            f.write(content)
    except Exception:
        os.close(fd)
        raise
    return filepath


def create_temp_dir(prefix: str = "test_") -> str:
    """
    Create a temporary directory.
    
    Args:
        prefix: Directory prefix
        
    Returns:
        Path to the created temporary directory
    """
    return tempfile.mkdtemp(prefix=prefix)


def cleanup_temp_path(path: str) -> None:
    """
    Clean up a temporary file or directory.
    
    Args:
        path: Path to clean up
    """
    try:
        if os.path.isfile(path):
            os.unlink(path)
        elif os.path.isdir(path):
            shutil.rmtree(path)
    except Exception as e:
        logger.warning(f"Failed to cleanup temp path {path}: {e}")


class TempFileContext:
    """Context manager for temporary files."""
    
    def __init__(self, content: str = "", suffix: str = ".txt", prefix: str = "test_"):
        self.content = content
        self.suffix = suffix
        self.prefix = prefix
        self.filepath: Optional[str] = None
    
    def __enter__(self) -> str:
        self.filepath = create_temp_file(self.content, self.suffix, self.prefix)
        return self.filepath
    
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        if self.filepath:
            cleanup_temp_path(self.filepath)


class TempDirContext:
    """Context manager for temporary directories."""
    
    def __init__(self, prefix: str = "test_"):
        self.prefix = prefix
        self.dirpath: Optional[str] = None
    
    def __enter__(self) -> str:
        self.dirpath = create_temp_dir(self.prefix)
        return self.dirpath
    
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        if self.dirpath:
            cleanup_temp_path(self.dirpath)


def mock_async_function(return_value: Any = None, side_effect: Any = None) -> AsyncMock:
    """
    Create a mock async function.
    
    Args:
        return_value: Value to return when called
        side_effect: Side effect to apply when called
        
    Returns:
        AsyncMock object
    """
    mock = AsyncMock()
    if return_value is not None:
        mock.return_value = return_value
    if side_effect is not None:
        mock.side_effect = side_effect
    return mock


async def assert_eventually(
    condition: Callable[[], bool],
    timeout: float = 5.0,
    interval: float = 0.1,
    message: str = "Condition was not met within timeout"
) -> None:
    """
    Assert that a condition becomes true within a timeout period.
    
    Args:
        condition: Function that returns True when condition is met
        timeout: Maximum time to wait in seconds
        interval: Time between checks in seconds
        message: Error message if condition is not met
        
    Raises:
        AssertionError: If condition is not met within timeout
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition():
            return
        await asyncio.sleep(interval)
    
    raise AssertionError(message)


def time_function(func: Callable) -> Callable:
    """
    Decorator to time function execution.
    
    Args:
        func: Function to time
        
    Returns:
        Wrapped function that logs execution time
    """
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            logger.info(f"Function {func.__name__} took {end_time - start_time:.4f} seconds")
    
    return wrapper


async def time_async_function(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
    """
    Decorator to time async function execution.
    
    Args:
        func: Async function to time
        
    Returns:
        Wrapped async function that logs execution time
    """
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            end_time = time.time()
            logger.info(f"Async function {func.__name__} took {end_time - start_time:.4f} seconds")
    
    return wrapper


def create_mock_file_system(structure: dict, base_path: str) -> None:
    """
    Create a mock file system structure for testing.
    
    Args:
        structure: Dictionary representing file/directory structure
        base_path: Base path to create structure in
    """
    for name, content in structure.items():
        path = os.path.join(base_path, name)
        if isinstance(content, dict):
            # It's a directory
            os.makedirs(path, exist_ok=True)
            create_mock_file_system(content, path)
        else:
            # It's a file
            os.makedirs(os.path.dirname(path), exist_ok=True)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(str(content))


class MockLogger:
    """Mock logger for testing."""
    
    def __init__(self) -> None:
        self.messages: List[Tuple[str, str]] = []
    
    def debug(self, msg: str) -> None:
        self.messages.append(('DEBUG', msg))
    
    def info(self, msg: str) -> None:
        self.messages.append(('INFO', msg))
    
    def warning(self, msg: str) -> None:
        self.messages.append(('WARNING', msg))
    
    def error(self, msg: str) -> None:
        self.messages.append(('ERROR', msg))
    
    def critical(self, msg: str) -> None:
        self.messages.append(('CRITICAL', msg))
    
    def clear(self) -> None:
        self.messages.clear()
    
    def get_messages(self, level: Optional[str] = None) -> List[Any]:
        if level:
            return [msg for lvl, msg in self.messages if lvl == level]
        return self.messages
