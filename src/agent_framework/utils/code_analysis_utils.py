"""
Shared code analysis utilities.

This module provides common code analysis functions that are used
across multiple agents and plugins.
"""

import ast
import re
import os
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from pathlib import Path


@dataclass
class FunctionInfo:
    """Information about a function."""
    name: str
    line_number: int
    complexity: int
    parameters: List[str]
    return_type: Optional[str] = None
    docstring: Optional[str] = None
    decorators: List[str] = None
    
    def __post_init__(self):
        if self.decorators is None:
            self.decorators = []


@dataclass
class ClassInfo:
    """Information about a class."""
    name: str
    line_number: int
    methods: List[FunctionInfo]
    base_classes: List[str]
    docstring: Optional[str] = None
    decorators: List[str] = None
    
    def __post_init__(self):
        if self.decorators is None:
            self.decorators = []


@dataclass
class CodeQualityMetrics:
    """Code quality metrics."""
    complexity: float
    maintainability_index: float
    lines_of_code: int
    comment_ratio: float
    duplication_ratio: float
    test_coverage: Optional[float] = None


def calculate_complexity(code: str) -> Dict[str, Any]:
    """
    Calculate cyclomatic complexity of Python code.
    
    Args:
        code: Python code string
        
    Returns:
        Dictionary with complexity metrics
    """
    try:
        tree = ast.parse(code)
    except SyntaxError as e:
        return {"error": f"Syntax error: {e}", "complexity": 0}
    
    complexity_visitor = ComplexityVisitor()
    complexity_visitor.visit(tree)
    
    return {
        "total_complexity": complexity_visitor.complexity,
        "functions": complexity_visitor.functions,
        "classes": complexity_visitor.classes,
        "average_complexity": (
            complexity_visitor.complexity / max(len(complexity_visitor.functions), 1)
        )
    }


def analyze_code_quality(code: str, file_path: Optional[str] = None) -> CodeQualityMetrics:
    """
    Analyze code quality metrics.
    
    Args:
        code: Python code string
        file_path: Optional file path for additional context
        
    Returns:
        Code quality metrics
    """
    lines = code.split('\n')
    total_lines = len(lines)
    
    # Count non-empty lines
    non_empty_lines = sum(1 for line in lines if line.strip())
    
    # Count comment lines
    comment_lines = sum(1 for line in lines if line.strip().startswith('#'))
    
    # Calculate comment ratio
    comment_ratio = comment_lines / max(non_empty_lines, 1)
    
    # Calculate complexity
    complexity_result = calculate_complexity(code)
    total_complexity = complexity_result.get("total_complexity", 0)
    
    # Calculate maintainability index (simplified version)
    # MI = 171 - 5.2 * ln(Halstead Volume) - 0.23 * (Cyclomatic Complexity) - 16.2 * ln(Lines of Code)
    # Simplified version without Halstead metrics
    maintainability_index = max(0, 100 - total_complexity * 2 - max(0, total_lines - 100) * 0.1)
    
    # Estimate duplication (very basic - count repeated lines)
    line_counts = {}
    for line in lines:
        stripped = line.strip()
        if stripped and not stripped.startswith('#'):
            line_counts[stripped] = line_counts.get(stripped, 0) + 1
    
    duplicated_lines = sum(count - 1 for count in line_counts.values() if count > 1)
    duplication_ratio = duplicated_lines / max(non_empty_lines, 1)
    
    return CodeQualityMetrics(
        complexity=total_complexity,
        maintainability_index=maintainability_index,
        lines_of_code=total_lines,
        comment_ratio=comment_ratio,
        duplication_ratio=duplication_ratio
    )


def detect_patterns(code: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Detect common code patterns and anti-patterns.
    
    Args:
        code: Python code string
        
    Returns:
        Dictionary of detected patterns
    """
    patterns = {
        "design_patterns": [],
        "anti_patterns": [],
        "code_smells": []
    }
    
    try:
        tree = ast.parse(code)
    except SyntaxError:
        return patterns
    
    pattern_visitor = PatternVisitor()
    pattern_visitor.visit(tree)
    
    patterns.update(pattern_visitor.patterns)
    
    return patterns


def extract_functions(code: str) -> List[FunctionInfo]:
    """
    Extract function information from Python code.
    
    Args:
        code: Python code string
        
    Returns:
        List of function information
    """
    try:
        tree = ast.parse(code)
    except SyntaxError:
        return []
    
    function_visitor = FunctionVisitor()
    function_visitor.visit(tree)
    
    return function_visitor.functions


def extract_classes(code: str) -> List[ClassInfo]:
    """
    Extract class information from Python code.
    
    Args:
        code: Python code string
        
    Returns:
        List of class information
    """
    try:
        tree = ast.parse(code)
    except SyntaxError:
        return []
    
    class_visitor = ClassVisitor()
    class_visitor.visit(tree)
    
    return class_visitor.classes


def get_dependencies(code: str) -> Dict[str, List[str]]:
    """
    Extract import dependencies from Python code.
    
    Args:
        code: Python code string
        
    Returns:
        Dictionary with import information
    """
    try:
        tree = ast.parse(code)
    except SyntaxError:
        return {"imports": [], "from_imports": []}
    
    import_visitor = ImportVisitor()
    import_visitor.visit(tree)
    
    return {
        "imports": import_visitor.imports,
        "from_imports": import_visitor.from_imports
    }


class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor for calculating cyclomatic complexity."""
    
    def __init__(self):
        self.complexity = 1  # Base complexity
        self.functions = []
        self.classes = []
        self.current_function = None
        self.current_class = None
    
    def visit_FunctionDef(self, node):
        # Save current context
        old_function = self.current_function
        old_complexity = self.complexity
        
        # Start new function
        self.current_function = node.name
        self.complexity = 1  # Reset for function
        
        # Visit function body
        self.generic_visit(node)
        
        # Record function complexity
        self.functions.append({
            "name": node.name,
            "complexity": self.complexity,
            "line_number": node.lineno
        })
        
        # Restore context
        self.current_function = old_function
        if old_function is None:
            # Add function complexity to total
            self.complexity += old_complexity
        else:
            self.complexity = old_complexity + self.complexity
    
    def visit_AsyncFunctionDef(self, node):
        self.visit_FunctionDef(node)
    
    def visit_ClassDef(self, node):
        old_class = self.current_class
        self.current_class = node.name
        
        self.generic_visit(node)
        
        self.current_class = old_class
    
    def visit_If(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_While(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_For(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_AsyncFor(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_ExceptHandler(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_With(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_AsyncWith(self, node):
        self.complexity += 1
        self.generic_visit(node)
    
    def visit_BoolOp(self, node):
        # Add complexity for each additional boolean operation
        self.complexity += len(node.values) - 1
        self.generic_visit(node)


class PatternVisitor(ast.NodeVisitor):
    """AST visitor for detecting code patterns."""

    def __init__(self):
        self.patterns = {
            "design_patterns": [],
            "anti_patterns": [],
            "code_smells": []
        }

    def visit_ClassDef(self, node):
        # Check for singleton pattern
        if self._is_singleton_pattern(node):
            self.patterns["design_patterns"].append({
                "pattern": "Singleton",
                "line": node.lineno,
                "class": node.name
            })

        # Check for god class anti-pattern
        if len(node.body) > 20:  # Arbitrary threshold
            self.patterns["anti_patterns"].append({
                "pattern": "God Class",
                "line": node.lineno,
                "class": node.name,
                "methods_count": len([n for n in node.body if isinstance(n, ast.FunctionDef)])
            })

        self.generic_visit(node)

    def visit_FunctionDef(self, node):
        # Check for long method code smell
        if len(node.body) > 15:  # Arbitrary threshold
            self.patterns["code_smells"].append({
                "smell": "Long Method",
                "line": node.lineno,
                "function": node.name,
                "statements": len(node.body)
            })

        # Check for too many parameters
        if len(node.args.args) > 5:  # Arbitrary threshold
            self.patterns["code_smells"].append({
                "smell": "Long Parameter List",
                "line": node.lineno,
                "function": node.name,
                "parameters": len(node.args.args)
            })

        self.generic_visit(node)

    def _is_singleton_pattern(self, node):
        """Check if class implements singleton pattern."""
        # Look for __new__ method that returns same instance
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and item.name == "__new__":
                return True
        return False


class FunctionVisitor(ast.NodeVisitor):
    """AST visitor for extracting function information."""

    def __init__(self):
        self.functions = []

    def visit_FunctionDef(self, node):
        parameters = [arg.arg for arg in node.args.args]

        # Extract decorators
        decorators = []
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                decorators.append(decorator.id)
            elif isinstance(decorator, ast.Attribute):
                decorators.append(f"{decorator.attr}")

        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Str)):
            docstring = node.body[0].value.s

        # Calculate complexity for this function
        complexity_visitor = ComplexityVisitor()
        complexity_visitor.visit(node)

        function_info = FunctionInfo(
            name=node.name,
            line_number=node.lineno,
            complexity=complexity_visitor.complexity,
            parameters=parameters,
            docstring=docstring,
            decorators=decorators
        )

        self.functions.append(function_info)

        self.generic_visit(node)

    def visit_AsyncFunctionDef(self, node):
        self.visit_FunctionDef(node)


class ClassVisitor(ast.NodeVisitor):
    """AST visitor for extracting class information."""

    def __init__(self):
        self.classes = []

    def visit_ClassDef(self, node):
        # Extract base classes
        base_classes = []
        for base in node.bases:
            if isinstance(base, ast.Name):
                base_classes.append(base.id)
            elif isinstance(base, ast.Attribute):
                base_classes.append(f"{base.attr}")

        # Extract decorators
        decorators = []
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                decorators.append(decorator.id)

        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Str)):
            docstring = node.body[0].value.s

        # Extract methods
        methods = []
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                function_visitor = FunctionVisitor()
                function_visitor.visit(item)
                if function_visitor.functions:
                    methods.append(function_visitor.functions[0])

        class_info = ClassInfo(
            name=node.name,
            line_number=node.lineno,
            methods=methods,
            base_classes=base_classes,
            docstring=docstring,
            decorators=decorators
        )

        self.classes.append(class_info)

        self.generic_visit(node)


class ImportVisitor(ast.NodeVisitor):
    """AST visitor for extracting import information."""

    def __init__(self):
        self.imports = []
        self.from_imports = []

    def visit_Import(self, node):
        for alias in node.names:
            self.imports.append({
                "module": alias.name,
                "alias": alias.asname,
                "line": node.lineno
            })

    def visit_ImportFrom(self, node):
        module = node.module or ""
        for alias in node.names:
            self.from_imports.append({
                "module": module,
                "name": alias.name,
                "alias": alias.asname,
                "line": node.lineno
            })
