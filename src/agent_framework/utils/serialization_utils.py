"""
Serialization utilities for the agent framework.

This module provides utilities for serializing and deserializing objects,
handling JSON operations safely, and encoding/decoding data.
"""

import json
import base64
import pickle
from typing import Any, Dict, Optional, Union
import logging

logger = logging.getLogger(__name__)


def serialize_object(obj: Any, method: str = "json") -> str:
    """
    Serialize an object to a string representation.
    
    Args:
        obj: The object to serialize
        method: Serialization method ("json" or "pickle")
        
    Returns:
        Serialized string representation
        
    Raises:
        ValueError: If serialization fails
    """
    try:
        if method == "json":
            return safe_json_dumps(obj)
        elif method == "pickle":
            return base64.b64encode(pickle.dumps(obj)).decode('utf-8')
        else:
            raise ValueError(f"Unsupported serialization method: {method}")
    except Exception as e:
        logger.error(f"Failed to serialize object: {e}")
        raise ValueError(f"Serialization failed: {e}")


def deserialize_object(data: str, method: str = "json") -> Any:
    """
    Deserialize a string representation back to an object.
    
    Args:
        data: The serialized string data
        method: Deserialization method ("json" or "pickle")
        
    Returns:
        Deserialized object
        
    Raises:
        ValueError: If deserialization fails
    """
    try:
        if method == "json":
            return safe_json_loads(data)
        elif method == "pickle":
            return pickle.loads(base64.b64decode(data.encode('utf-8')))
        else:
            raise ValueError(f"Unsupported deserialization method: {method}")
    except Exception as e:
        logger.error(f"Failed to deserialize object: {e}")
        raise ValueError(f"Deserialization failed: {e}")


def safe_json_loads(data: str, default: Any = None) -> Any:
    """
    Safely load JSON data with error handling.
    
    Args:
        data: JSON string to parse
        default: Default value to return on error
        
    Returns:
        Parsed JSON data or default value
    """
    try:
        return json.loads(data)
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"Failed to parse JSON: {e}")
        return default


def safe_json_dumps(obj: Any, indent: Optional[int] = None, **kwargs: Any) -> str:
    """
    Safely serialize object to JSON string with error handling.
    
    Args:
        obj: Object to serialize
        indent: JSON indentation level
        **kwargs: Additional arguments for json.dumps
        
    Returns:
        JSON string representation
        
    Raises:
        ValueError: If serialization fails
    """
    try:
        return json.dumps(obj, indent=indent, default=str, **kwargs)
    except (TypeError, ValueError) as e:
        logger.error(f"Failed to serialize to JSON: {e}")
        raise ValueError(f"JSON serialization failed: {e}")


def encode_base64(data: Union[str, bytes]) -> str:
    """
    Encode data to base64 string.
    
    Args:
        data: Data to encode (string or bytes)
        
    Returns:
        Base64 encoded string
    """
    if isinstance(data, str):
        data = data.encode('utf-8')
    return base64.b64encode(data).decode('utf-8')


def decode_base64(data: str) -> bytes:
    """
    Decode base64 string to bytes.
    
    Args:
        data: Base64 encoded string
        
    Returns:
        Decoded bytes
        
    Raises:
        ValueError: If decoding fails
    """
    try:
        return base64.b64decode(data)
    except Exception as e:
        logger.error(f"Failed to decode base64: {e}")
        raise ValueError(f"Base64 decoding failed: {e}")


def serialize_dict_to_json_file(data: Dict[str, Any], filepath: str) -> None:
    """
    Serialize a dictionary to a JSON file.
    
    Args:
        data: Dictionary to serialize
        filepath: Path to output file
        
    Raises:
        ValueError: If serialization or file writing fails
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
    except Exception as e:
        logger.error(f"Failed to write JSON file {filepath}: {e}")
        raise ValueError(f"Failed to write JSON file: {e}")


def deserialize_dict_from_json_file(filepath: str) -> Dict[str, Any]:
    """
    Deserialize a dictionary from a JSON file.
    
    Args:
        filepath: Path to JSON file
        
    Returns:
        Deserialized dictionary
        
    Raises:
        ValueError: If deserialization or file reading fails
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            result = json.load(f)
            if not isinstance(result, dict):
                raise ValueError(f"JSON file does not contain a dictionary: {type(result)}")
            return result
    except Exception as e:
        logger.error(f"Failed to read JSON file {filepath}: {e}")
        raise ValueError(f"Failed to read JSON file: {e}")
