"""
Common utilities for the agent framework.

This module provides shared utility functions and classes
that are used across multiple components.
"""

from .code_analysis_utils import (
    calculate_complexity, analyze_code_quality, detect_patterns,
    extract_functions, extract_classes, get_dependencies
)
from .file_utils import (
    read_file_safe, write_file_safe, ensure_directory,
    get_file_info, find_files, backup_file
)
from .validation_utils import (
    validate_python_syntax, validate_json_schema,
    validate_file_path, sanitize_input
)
from .serialization_utils import (
    serialize_object, deserialize_object, safe_json_loads,
    safe_json_dumps, encode_base64, decode_base64
)
from .async_utils import (
    run_with_timeout, gather_with_limit, retry_async,
    create_task_group, safe_await
)
from .testing_utils import (
    create_temp_file, create_temp_dir, mock_async_function,
    assert_eventually, time_function
)

__all__ = [
    # Code analysis utilities
    "calculate_complexity",
    "analyze_code_quality", 
    "detect_patterns",
    "extract_functions",
    "extract_classes",
    "get_dependencies",
    
    # File utilities
    "read_file_safe",
    "write_file_safe",
    "ensure_directory",
    "get_file_info",
    "find_files",
    "backup_file",
    
    # Validation utilities
    "validate_python_syntax",
    "validate_json_schema",
    "validate_file_path",
    "sanitize_input",
    
    # Serialization utilities
    "serialize_object",
    "deserialize_object",
    "safe_json_loads",
    "safe_json_dumps",
    "encode_base64",
    "decode_base64",
    
    # Async utilities
    "run_with_timeout",
    "gather_with_limit",
    "retry_async",
    "create_task_group",
    "safe_await",
    
    # Testing utilities
    "create_temp_file",
    "create_temp_dir",
    "mock_async_function",
    "assert_eventually",
    "time_function"
]
