"""
Security monitoring and audit logging for the agent framework.

Provides real-time security event detection, audit logging,
and threat monitoring capabilities.
"""

import time
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class SecurityEventType(Enum):
    """Types of security events."""
    AUTHENTICATION_FAILURE = "auth_failure"
    AUTHORIZATION_FAILURE = "authz_failure"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INVALID_INPUT = "invalid_input"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_ACCESS = "data_access"
    CONFIGURATION_CHANGE = "config_change"
    SYSTEM_ANOMALY = "system_anomaly"


class SecuritySeverity(Enum):
    """Security event severity levels."""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityEvent:
    """Security event data structure."""
    event_type: SecurityEventType
    severity: SecuritySeverity
    timestamp: datetime
    source: str
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    details: Dict[str, Any] = None
    correlation_id: Optional[str] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


@dataclass
class ThreatIndicator:
    """Threat indicator for pattern detection."""
    name: str
    pattern: str
    severity: SecuritySeverity
    description: str
    enabled: bool = True


class SecurityMonitor:
    """
    Real-time security monitoring and audit logging system.
    
    Features:
    - Real-time threat detection
    - Audit logging with structured data
    - Rate limiting and anomaly detection
    - Configurable alerting thresholds
    - Integration with external SIEM systems
    """
    
    def __init__(self, 
                 log_file: Optional[str] = None,
                 max_events: int = 10000,
                 alert_threshold: int = 10):
        """Initialize the security monitor."""
        self.logger = logging.getLogger(__name__)
        self.log_file = log_file
        self.max_events = max_events
        self.alert_threshold = alert_threshold
        
        # Event storage
        self._events: deque = deque(maxlen=max_events)
        self._event_counts: Dict[str, int] = defaultdict(int)
        
        # Rate limiting tracking
        self._rate_limits: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Threat indicators
        self._threat_indicators: List[ThreatIndicator] = []
        self._initialize_default_indicators()
        
        # Alert callbacks
        self._alert_callbacks: List[Callable] = []
        
        # Monitoring task
        self._monitor_task: Optional[asyncio.Task] = None
        self._is_monitoring = False
        
        # Statistics
        self._stats = {
            'total_events': 0,
            'events_by_type': defaultdict(int),
            'events_by_severity': defaultdict(int),
            'alerts_triggered': 0,
            'last_alert': None
        }
    
    def _initialize_default_indicators(self) -> None:
        """Initialize default threat indicators."""
        default_indicators = [
            ThreatIndicator(
                name="SQL Injection Attempt",
                pattern=r"\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION)\b.*(\bOR\b|\bAND\b).*=",
                severity=SecuritySeverity.HIGH,
                description="Potential SQL injection attack detected"
            ),
            ThreatIndicator(
                name="XSS Attempt",
                pattern=r"<script[^>]*>.*?</script>|javascript:|vbscript:",
                severity=SecuritySeverity.HIGH,
                description="Potential XSS attack detected"
            ),
            ThreatIndicator(
                name="Command Injection",
                pattern=r"[;&|`$()]|\.\.\/|\b(rm|del|format|shutdown)\b",
                severity=SecuritySeverity.CRITICAL,
                description="Potential command injection detected"
            ),
            ThreatIndicator(
                name="Path Traversal",
                pattern=r"\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c",
                severity=SecuritySeverity.HIGH,
                description="Path traversal attempt detected"
            ),
            ThreatIndicator(
                name="Brute Force Pattern",
                pattern=r"(password|login|auth).*fail",
                severity=SecuritySeverity.MEDIUM,
                description="Potential brute force attack pattern"
            )
        ]
        
        self._threat_indicators.extend(default_indicators)
    
    async def start_monitoring(self) -> None:
        """Start the security monitoring task."""
        if self._is_monitoring:
            return
        
        self._is_monitoring = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Security monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop the security monitoring task."""
        if not self._is_monitoring:
            return
        
        self._is_monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Security monitoring stopped")
    
    def log_event(self, 
                  event_type: SecurityEventType,
                  severity: SecuritySeverity,
                  source: str,
                  details: Optional[Dict[str, Any]] = None,
                  user_id: Optional[str] = None,
                  ip_address: Optional[str] = None,
                  user_agent: Optional[str] = None,
                  correlation_id: Optional[str] = None) -> None:
        """Log a security event."""
        
        event = SecurityEvent(
            event_type=event_type,
            severity=severity,
            timestamp=datetime.now(),
            source=source,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
            correlation_id=correlation_id
        )
        
        # Store event
        self._events.append(event)
        
        # Update statistics
        self._stats['total_events'] += 1
        self._stats['events_by_type'][event_type.value] += 1
        self._stats['events_by_severity'][severity.value] += 1
        
        # Check for threats
        self._check_threat_indicators(event)
        
        # Check rate limits
        self._check_rate_limits(event)
        
        # Log to file if configured
        if self.log_file:
            self._write_to_log_file(event)
        
        # Log to standard logger
        log_level = self._get_log_level(severity)
        self.logger.log(log_level, f"Security event: {event_type.value} - {details}")
    
    def _check_threat_indicators(self, event: SecurityEvent) -> None:
        """Check event against threat indicators."""
        import re
        
        # Convert event to searchable text
        searchable_text = json.dumps(asdict(event), default=str).lower()
        
        for indicator in self._threat_indicators:
            if not indicator.enabled:
                continue
            
            if re.search(indicator.pattern, searchable_text, re.IGNORECASE):
                self._trigger_alert(
                    f"Threat indicator matched: {indicator.name}",
                    event,
                    indicator.severity
                )
    
    def _check_rate_limits(self, event: SecurityEvent) -> None:
        """Check for rate limit violations."""
        now = time.time()
        
        # Track by IP address
        if event.ip_address:
            key = f"ip:{event.ip_address}"
            self._rate_limits[key].append(now)
            
            # Check if rate limit exceeded
            recent_events = [t for t in self._rate_limits[key] if now - t < 60]  # Last minute
            if len(recent_events) > self.alert_threshold:
                self._trigger_alert(
                    f"Rate limit exceeded for IP {event.ip_address}",
                    event,
                    SecuritySeverity.HIGH
                )
        
        # Track by user
        if event.user_id:
            key = f"user:{event.user_id}"
            self._rate_limits[key].append(now)
            
            recent_events = [t for t in self._rate_limits[key] if now - t < 60]
            if len(recent_events) > self.alert_threshold:
                self._trigger_alert(
                    f"Rate limit exceeded for user {event.user_id}",
                    event,
                    SecuritySeverity.MEDIUM
                )
    
    def _trigger_alert(self, 
                      message: str, 
                      event: SecurityEvent, 
                      severity: SecuritySeverity) -> None:
        """Trigger a security alert."""
        alert_data = {
            'message': message,
            'event': asdict(event),
            'severity': severity.value,
            'timestamp': datetime.now().isoformat()
        }
        
        # Update statistics
        self._stats['alerts_triggered'] += 1
        self._stats['last_alert'] = alert_data['timestamp']
        
        # Call alert callbacks
        for callback in self._alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                self.logger.error(f"Alert callback failed: {e}")
        
        # Log alert
        self.logger.warning(f"SECURITY ALERT: {message}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Add a callback for security alerts."""
        self._alert_callbacks.append(callback)
    
    def get_events(self, 
                   limit: int = 100,
                   event_type: Optional[SecurityEventType] = None,
                   severity: Optional[SecuritySeverity] = None,
                   since: Optional[datetime] = None) -> List[SecurityEvent]:
        """Get security events with optional filtering."""
        events = list(self._events)
        
        # Apply filters
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if severity:
            events = [e for e in events if e.severity == severity]
        
        if since:
            events = [e for e in events if e.timestamp >= since]
        
        # Sort by timestamp (newest first) and limit
        events.sort(key=lambda e: e.timestamp, reverse=True)
        return events[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get security monitoring statistics."""
        return dict(self._stats)
    
    def add_threat_indicator(self, indicator: ThreatIndicator) -> None:
        """Add a custom threat indicator."""
        self._threat_indicators.append(indicator)
    
    def remove_threat_indicator(self, name: str) -> bool:
        """Remove a threat indicator by name."""
        for i, indicator in enumerate(self._threat_indicators):
            if indicator.name == name:
                del self._threat_indicators[i]
                return True
        return False
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        while self._is_monitoring:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Perform system health checks
                await self._check_system_anomalies()
                
                # Clean up old rate limit data
                self._cleanup_rate_limits()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
    
    async def _check_system_anomalies(self) -> None:
        """Check for system-level anomalies."""
        if not PSUTIL_AVAILABLE:
            return
        
        try:
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                self.log_event(
                    SecurityEventType.SYSTEM_ANOMALY,
                    SecuritySeverity.MEDIUM,
                    "system_monitor",
                    {"metric": "cpu_usage", "value": cpu_percent}
                )
            
            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                self.log_event(
                    SecurityEventType.SYSTEM_ANOMALY,
                    SecuritySeverity.MEDIUM,
                    "system_monitor",
                    {"metric": "memory_usage", "value": memory.percent}
                )
            
            # Check disk usage
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                self.log_event(
                    SecurityEventType.SYSTEM_ANOMALY,
                    SecuritySeverity.HIGH,
                    "system_monitor",
                    {"metric": "disk_usage", "value": disk.percent}
                )
                
        except Exception as e:
            self.logger.error(f"System anomaly check failed: {e}")
    
    def _cleanup_rate_limits(self) -> None:
        """Clean up old rate limit tracking data."""
        now = time.time()
        cutoff = now - 3600  # Keep last hour
        
        for key in list(self._rate_limits.keys()):
            self._rate_limits[key] = deque(
                [t for t in self._rate_limits[key] if t > cutoff],
                maxlen=100
            )
            
            # Remove empty entries
            if not self._rate_limits[key]:
                del self._rate_limits[key]
    
    def _write_to_log_file(self, event: SecurityEvent) -> None:
        """Write event to log file."""
        try:
            log_entry = {
                'timestamp': event.timestamp.isoformat(),
                'event_type': event.event_type.value,
                'severity': event.severity.value,
                'source': event.source,
                'user_id': event.user_id,
                'ip_address': event.ip_address,
                'user_agent': event.user_agent,
                'details': event.details,
                'correlation_id': event.correlation_id
            }
            
            with open(self.log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
                
        except Exception as e:
            self.logger.error(f"Failed to write to log file: {e}")
    
    def _get_log_level(self, severity: SecuritySeverity) -> int:
        """Convert security severity to logging level."""
        mapping = {
            SecuritySeverity.INFO: logging.INFO,
            SecuritySeverity.LOW: logging.INFO,
            SecuritySeverity.MEDIUM: logging.WARNING,
            SecuritySeverity.HIGH: logging.ERROR,
            SecuritySeverity.CRITICAL: logging.CRITICAL
        }
        return mapping.get(severity, logging.INFO)
