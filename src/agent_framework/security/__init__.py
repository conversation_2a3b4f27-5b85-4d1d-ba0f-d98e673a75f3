"""
Security module for the agent framework.

Provides comprehensive security features including input validation,
secrets management, and security monitoring.
"""

from .input_validator import InputValida<PERSON>, ValidationResult, SecurityConfig
from .secrets_manager import SecretsManager, SecretMetadata
from .security_monitor import SecurityMonitor, SecurityEvent, SecurityEventType, SecuritySeverity

__all__ = [
    'InputValidator',
    'ValidationResult', 
    'SecurityConfig',
    'SecretsManager',
    'SecretMetadata',
    'SecurityMonitor',
    'SecurityEvent',
    'SecurityEventType',
    'SecuritySeverity',
    'SecurityManager'
]


class SecurityManager:
    """
    Unified security manager that integrates all security components.
    
    Provides a single interface for all security operations including
    input validation, secrets management, and security monitoring.
    """
    
    def __init__(self, 
                 validator_config: SecurityConfig = None,
                 secrets_storage_path: str = None,
                 monitor_log_file: str = None):
        """Initialize the security manager."""
        
        # Initialize components
        self.validator = InputValidator(validator_config)
        self.secrets = SecretsManager(storage_path=secrets_storage_path)
        self.monitor = SecurityMonitor(log_file=monitor_log_file)
        
        # Set up integration between components
        self._setup_integrations()
    
    def _setup_integrations(self) -> None:
        """Set up integrations between security components."""
        
        # Add security monitor callback for validation failures
        def validation_alert_callback(alert_data):
            if 'validation' in alert_data.get('message', '').lower():
                self.monitor.log_event(
                    SecurityEventType.INVALID_INPUT,
                    SecuritySeverity.MEDIUM,
                    'input_validator',
                    alert_data
                )
        
        self.monitor.add_alert_callback(validation_alert_callback)
    
    async def start(self) -> None:
        """Start all security services."""
        await self.monitor.start_monitoring()
    
    async def stop(self) -> None:
        """Stop all security services."""
        await self.monitor.stop_monitoring()
    
    def validate_and_monitor(self, 
                           input_value: str, 
                           input_type: str = "string",
                           source: str = "unknown",
                           user_id: str = None,
                           ip_address: str = None) -> ValidationResult:
        """Validate input and log security events."""
        
        # Perform validation
        if input_type == "string":
            result = self.validator.validate_string(input_value)
        elif input_type == "file_path":
            result = self.validator.validate_file_path(input_value)
        elif input_type == "json":
            result = self.validator.validate_json(input_value)
        elif input_type == "python_code":
            result = self.validator.validate_python_code(input_value)
        elif input_type == "url":
            result = self.validator.validate_url(input_value)
        else:
            result = self.validator.validate_string(input_value)
        
        # Log security events based on validation result
        if not result.is_valid:
            self.monitor.log_event(
                SecurityEventType.INVALID_INPUT,
                result.severity,
                source,
                {
                    'input_type': input_type,
                    'errors': result.errors,
                    'warnings': result.warnings,
                    'input_preview': self.validator.sanitize_for_logging(input_value)
                },
                user_id=user_id,
                ip_address=ip_address
            )
        
        return result
    
    def get_secret_secure(self, 
                         secret_name: str,
                         source: str = "unknown",
                         user_id: str = None) -> str:
        """Get secret with security logging."""
        
        # Log access attempt
        self.monitor.log_event(
            SecurityEventType.DATA_ACCESS,
            SecuritySeverity.INFO,
            source,
            {'secret_name': secret_name, 'operation': 'get_secret'},
            user_id=user_id
        )
        
        return self.secrets.get_secret(secret_name)
    
    def store_secret_secure(self,
                           secret_name: str,
                           secret_value: str,
                           source: str = "unknown",
                           user_id: str = None,
                           **kwargs) -> bool:
        """Store secret with security logging."""
        
        # Log storage attempt
        self.monitor.log_event(
            SecurityEventType.DATA_ACCESS,
            SecuritySeverity.INFO,
            source,
            {'secret_name': secret_name, 'operation': 'store_secret'},
            user_id=user_id
        )
        
        return self.secrets.store_secret(secret_name, secret_value, **kwargs)
