"""
Comprehensive input validation and sanitization for the agent framework.

Provides security-focused validation for all user inputs, API requests,
and data processing operations.
"""

import re
import json
import html
import urllib.parse
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import ast

from pydantic import BaseModel, ValidationError, validator
from pydantic.fields import Field


class ValidationSeverity(Enum):
    """Validation error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of input validation."""
    is_valid: bool
    sanitized_value: Any = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    severity: ValidationSeverity = ValidationSeverity.LOW

    def __post_init__(self) -> None:
        pass


class SecurityConfig(BaseModel):
    """Security configuration for input validation."""
    max_string_length: int = Field(default=10000, ge=1)
    max_list_length: int = Field(default=1000, ge=1)
    max_dict_depth: int = Field(default=10, ge=1)
    allowed_file_extensions: List[str] = Field(default_factory=lambda: [
        '.txt', '.json', '.csv', '.md', '.py', '.js', '.html', '.css'
    ])
    blocked_patterns: List[str] = Field(default_factory=lambda: [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',  # JavaScript URLs
        r'vbscript:',   # VBScript URLs
        r'on\w+\s*=',   # Event handlers
        r'eval\s*\(',   # eval() calls
        r'exec\s*\(',   # exec() calls
    ])
    sql_injection_patterns: List[str] = Field(default_factory=lambda: [
        r'\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b',
        r'(--|#|/\*|\*/)',
        r'(\bOR\b.*=.*\bOR\b)',
        r'(\bAND\b.*=.*\bAND\b)',
        r'(\'.*\')',
        r'(;.*)',
    ])
    command_injection_patterns: List[str] = Field(default_factory=lambda: [
        r'[;&|`$()]',  # Shell metacharacters
        r'\b(rm|del|format|shutdown|reboot)\b',  # Dangerous commands
        r'\.\./',  # Directory traversal
    ])


class InputValidator:
    """
    Comprehensive input validator with security focus.
    
    Provides validation and sanitization for various input types
    with configurable security policies.
    """
    
    def __init__(self, config: Optional[SecurityConfig] = None):
        """Initialize the input validator."""
        self.config = config or SecurityConfig()
        self._compile_patterns()
    
    def _compile_patterns(self) -> None:
        """Compile regex patterns for performance."""
        self._blocked_patterns = [
            re.compile(pattern, re.IGNORECASE | re.DOTALL)
            for pattern in self.config.blocked_patterns
        ]
        
        self._sql_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in self.config.sql_injection_patterns
        ]
        
        self._command_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in self.config.command_injection_patterns
        ]
    
    def validate_string(self, value: str, max_length: Optional[int] = None) -> ValidationResult:
        """Validate and sanitize string input."""
        if not isinstance(value, str):
            return ValidationResult(  # type: ignore[unreachable]
                is_valid=False,
                errors=["Input must be a string"],
                severity=ValidationSeverity.MEDIUM
            )
        
        result = ValidationResult(is_valid=True, sanitized_value=value)
        
        # Check length
        max_len = max_length or self.config.max_string_length
        if len(value) > max_len:
            result.errors.append(f"String length exceeds maximum ({max_len})")
            result.severity = ValidationSeverity.HIGH
            result.is_valid = False
            return result
        
        # Check for blocked patterns
        for pattern in self._blocked_patterns:
            if pattern.search(value):
                result.errors.append(f"Blocked pattern detected: {pattern.pattern}")
                result.severity = ValidationSeverity.CRITICAL
                result.is_valid = False
                return result
        
        # Check for SQL injection
        for pattern in self._sql_patterns:
            if pattern.search(value):
                result.errors.append("Potential SQL injection detected")
                result.severity = ValidationSeverity.CRITICAL
                result.is_valid = False
                return result
        
        # Check for command injection
        for pattern in self._command_patterns:
            if pattern.search(value):
                result.errors.append("Potential command injection detected")
                result.severity = ValidationSeverity.CRITICAL
                result.is_valid = False
                return result
        
        # Sanitize HTML
        sanitized = html.escape(value)
        if sanitized != value:
            result.warnings.append("HTML characters were escaped")
            result.sanitized_value = sanitized
        
        return result
    
    def validate_file_path(self, path: Union[str, Path]) -> ValidationResult:
        """Validate file path for security."""
        path_str = str(path)
        result = ValidationResult(is_valid=True, sanitized_value=path_str)
        
        # Check for directory traversal
        if '..' in path_str or path_str.startswith('/'):
            result.errors.append("Directory traversal detected")
            result.severity = ValidationSeverity.CRITICAL
            result.is_valid = False
            return result
        
        # Check file extension
        path_obj = Path(path_str)
        if path_obj.suffix.lower() not in self.config.allowed_file_extensions:
            result.errors.append(f"File extension not allowed: {path_obj.suffix}")
            result.severity = ValidationSeverity.HIGH
            result.is_valid = False
            return result
        
        # Normalize path
        try:
            normalized = Path(path_str).resolve()
            result.sanitized_value = str(normalized)
        except Exception as e:
            result.errors.append(f"Invalid path: {e}")
            result.severity = ValidationSeverity.HIGH
            result.is_valid = False
        
        return result
    
    def validate_json(self, value: str) -> ValidationResult:
        """Validate JSON input."""
        result = ValidationResult(is_valid=True)
        
        try:
            parsed = json.loads(value)
            
            # Check depth
            if self._get_dict_depth(parsed) > self.config.max_dict_depth:
                result.errors.append(f"JSON depth exceeds maximum ({self.config.max_dict_depth})")
                result.severity = ValidationSeverity.HIGH
                result.is_valid = False
                return result
            
            result.sanitized_value = parsed
            
        except json.JSONDecodeError as e:
            result.errors.append(f"Invalid JSON: {e}")
            result.severity = ValidationSeverity.MEDIUM
            result.is_valid = False
        
        return result
    
    def validate_python_code(self, code: str) -> ValidationResult:
        """Validate Python code for security."""
        result = ValidationResult(is_valid=True, sanitized_value=code)
        
        # Check for dangerous functions
        dangerous_functions = [
            'eval', 'exec', 'compile', '__import__', 'open', 'file',
            'input', 'raw_input', 'execfile', 'reload', 'vars', 'globals', 'locals'
        ]
        
        for func in dangerous_functions:
            if re.search(rf'\b{func}\s*\(', code):
                result.errors.append(f"Dangerous function detected: {func}")
                result.severity = ValidationSeverity.CRITICAL
                result.is_valid = False
        
        # Check for dangerous imports
        dangerous_imports = [
            'os', 'sys', 'subprocess', 'shutil', 'socket', 'urllib',
            'requests', 'http', 'ftplib', 'smtplib'
        ]
        
        for module in dangerous_imports:
            if re.search(rf'\b(import\s+{module}|from\s+{module})', code):
                result.warnings.append(f"Potentially dangerous import: {module}")
        
        # Try to parse AST
        try:
            ast.parse(code)
        except SyntaxError as e:
            result.errors.append(f"Syntax error: {e}")
            result.severity = ValidationSeverity.MEDIUM
            result.is_valid = False
        
        return result
    
    def validate_url(self, url: str) -> ValidationResult:
        """Validate URL input."""
        result = ValidationResult(is_valid=True)
        
        # Basic URL validation
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            result.errors.append("Invalid URL format")
            result.severity = ValidationSeverity.MEDIUM
            result.is_valid = False
            return result
        
        # Check for dangerous schemes
        parsed = urllib.parse.urlparse(url)
        if parsed.scheme.lower() not in ['http', 'https']:
            result.errors.append(f"Dangerous URL scheme: {parsed.scheme}")
            result.severity = ValidationSeverity.HIGH
            result.is_valid = False
            return result
        
        # Check for localhost/private IPs in production
        if parsed.hostname in ['localhost', '127.0.0.1', '0.0.0.0']:
            result.warnings.append("URL points to localhost")
        
        result.sanitized_value = url
        return result
    
    def validate_list(self, value: List[Any]) -> ValidationResult:
        """Validate list input."""
        if not isinstance(value, list):
            return ValidationResult(  # type: ignore[unreachable]
                is_valid=False,
                errors=["Input must be a list"],
                severity=ValidationSeverity.MEDIUM
            )
        
        result = ValidationResult(is_valid=True, sanitized_value=value)
        
        # Check length
        if len(value) > self.config.max_list_length:
            result.errors.append(f"List length exceeds maximum ({self.config.max_list_length})")
            result.severity = ValidationSeverity.HIGH
            result.is_valid = False
        
        return result
    
    def _get_dict_depth(self, obj: Any, depth: int = 0) -> int:
        """Calculate the depth of nested dictionaries."""
        if not isinstance(obj, dict):
            return depth
        
        if not obj:
            return depth + 1
        
        return max(self._get_dict_depth(value, depth + 1) for value in obj.values())
    
    def sanitize_for_logging(self, value: str, max_length: int = 200) -> str:
        """Sanitize string for safe logging."""
        if not isinstance(value, str):
            value = str(value)  # type: ignore[unreachable]
        
        # Truncate
        if len(value) > max_length:
            value = value[:max_length] + "..."
        
        # Remove sensitive patterns
        sensitive_patterns = [
            (r'password["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', 'password=***'),
            (r'api[_-]?key["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', 'api_key=***'),
            (r'token["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', 'token=***'),
            (r'secret["\']?\s*[:=]\s*["\']?([^"\'\\s]+)', 'secret=***'),
        ]
        
        for pattern, replacement in sensitive_patterns:
            value = re.sub(pattern, replacement, value, flags=re.IGNORECASE)
        
        return value
