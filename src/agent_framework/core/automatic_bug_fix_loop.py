"""
Automatic bug fix loop system for iterative debugging and error resolution.
"""

import asyncio
import logging
import subprocess
import tempfile
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum

from .debugger import Debugger, DebugContext, RootCauseAnalysis, DebugSuggestion
from .code_editor import CodeEditor, CodeEdit
from .robust_code_generator import RobustCodeGenerator
from ..agents.testing_agent import TestingAgent
from ..agents.error_detection_agent import ErrorDetectionAgent
from ..core.types import Task, TaskResult, TaskStatus


class FixAttemptStatus(Enum):
    """Status of a fix attempt."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"


@dataclass
class FixAttempt:
    """Represents a single fix attempt."""
    attempt_number: int
    suggestion: DebugSuggestion
    applied_changes: List[CodeEdit]
    test_results: Dict[str, Any]
    status: FixAttemptStatus
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: float = field(default_factory=time.time)


@dataclass
class BugFixSession:
    """Represents a complete bug fix session."""
    session_id: str
    initial_error: DebugContext
    root_cause: RootCauseAnalysis
    fix_attempts: List[FixAttempt]
    final_status: FixAttemptStatus
    total_time: float
    success_rate: float
    lessons_learned: List[str]


class AutomaticBugFixLoop:
    """Automatic bug fix loop with iterative debugging and resolution."""

    def __init__(self, max_iterations: int = 5, timeout_per_attempt: int = 300, enable_advanced_analysis: bool = True):
        self.logger = logging.getLogger(__name__)
        self.debugger = Debugger()
        self.editor = CodeEditor()
        self.code_generator = RobustCodeGenerator()
        self.testing_agent = TestingAgent()

        # Create a minimal config for the error detection agent
        from ..core.config import FrameworkConfig
        minimal_config = FrameworkConfig()
        self.error_detection_agent = ErrorDetectionAgent(minimal_config)

        self.max_iterations = max_iterations
        self.timeout_per_attempt = timeout_per_attempt
        self.enable_advanced_analysis = enable_advanced_analysis
        self._fix_history: List[BugFixSession] = []

        # Enhanced logging configuration
        self._setup_enhanced_logging()

        # Performance tracking
        self._performance_metrics: Dict[str, Any] = {
            'total_fix_sessions': 0,
            'successful_sessions': 0,
            'average_iterations': 0.0,
            'average_time_per_session': 0.0,
            'common_error_types': {},
            'most_effective_fixes': {}
        }

    def _setup_enhanced_logging(self):
        """Setup enhanced logging for the bug fix loop."""
        # Create a dedicated logger for bug fix sessions
        self.session_logger = logging.getLogger(f"{__name__}.sessions")

        # Configure detailed logging format
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [Session: %(session_id)s] - %(message)s'
        )

        # Add session context to logger
        self.session_logger.setLevel(logging.DEBUG)

    async def start_fix_loop(self,
                           error: Exception,
                           code_content: str,
                           file_path: str,
                           test_files: Optional[List[str]] = None,
                           context_vars: Optional[Dict[str, Any]] = None) -> BugFixSession:
        """
        Start the automatic bug fix loop.
        
        Args:
            error: The exception that occurred
            code_content: The code where the error occurred
            file_path: Path to the file
            test_files: Optional list of test files to run
            context_vars: Variables in the error context
            
        Returns:
            Bug fix session results
        """
        session_id = f"fix_session_{int(time.time())}"
        start_time = time.time()

        # Enhanced logging with session context
        session_extra = {'session_id': session_id}
        self.session_logger.info(f"Starting bug fix loop session: {session_id}", extra=session_extra)
        self.session_logger.info(f"Error type: {type(error).__name__}, File: {file_path}", extra=session_extra)

        # Update performance metrics
        self._performance_metrics['total_fix_sessions'] += 1
        error_type = type(error).__name__
        self._performance_metrics['common_error_types'][error_type] = \
            self._performance_metrics['common_error_types'].get(error_type, 0) + 1

        try:
            # Enhanced error analysis with multiple approaches
            debug_context = await self.debugger.analyze_error(
                error, code_content, file_path, context_vars
            )

            # Perform root cause analysis
            root_cause = await self.debugger.perform_root_cause_analysis(debug_context)

            # Generate debug suggestions from multiple sources
            suggestions = await self.debugger.generate_debug_suggestions(
                debug_context, root_cause
            )

            # Enhanced analysis: Use error detection agent for additional insights
            if self.enable_advanced_analysis:
                additional_suggestions = await self._get_additional_error_insights(
                    code_content, file_path, error, session_extra
                )
                suggestions.extend(additional_suggestions)
            
            # Initialize session
            session = BugFixSession(
                session_id=session_id,
                initial_error=debug_context,
                root_cause=root_cause,
                fix_attempts=[],
                final_status=FixAttemptStatus.PENDING,
                total_time=0.0,
                success_rate=0.0,
                lessons_learned=[]
            )
            
            # Run fix iterations
            await self._run_fix_iterations(session, suggestions, code_content, file_path, test_files)
            
            # Finalize session
            session.total_time = time.time() - start_time
            session.success_rate = self._calculate_success_rate(session)
            session.lessons_learned = self._extract_lessons_learned(session)
            
            # Add to history
            self._fix_history.append(session)

            # Update performance metrics
            self._update_performance_metrics(session)

            # Enhanced completion logging
            self.session_logger.info(
                f"Bug fix session completed: {session_id}, Status: {session.final_status.value}, "
                f"Attempts: {len(session.fix_attempts)}, Time: {session.total_time:.2f}s",
                extra=session_extra
            )

            if session.final_status == FixAttemptStatus.SUCCESS:
                self.session_logger.info(f"Successful fix applied: {session.lessons_learned}", extra=session_extra)
            else:
                self.session_logger.warning(f"Fix session failed after {len(session.fix_attempts)} attempts", extra=session_extra)

            return session
            
        except Exception as e:
            self.logger.error(f"Bug fix loop failed: {e}")
            return BugFixSession(
                session_id=session_id,
                initial_error=DebugContext(
                    error_type="FixLoopError",
                    error_message=str(e),
                    traceback_info="",
                    file_path=file_path,
                    line_number=0,
                    code_context=[],
                    variables={},
                    call_stack=[]
                ),
                root_cause=RootCauseAnalysis(
                    primary_cause="fix_loop_failure",
                    contributing_factors=[],
                    confidence_score=0.0,
                    evidence=[],
                    similar_patterns=[]
                ),
                fix_attempts=[],
                final_status=FixAttemptStatus.FAILED,
                total_time=time.time() - start_time,
                success_rate=0.0,
                lessons_learned=[]
            )
    
    async def _run_fix_iterations(self,
                                session: BugFixSession,
                                suggestions: List[DebugSuggestion],
                                original_code: str,
                                file_path: str,
                                test_files: Optional[List[str]]) -> None:
        """Run iterative fix attempts with enhanced logging and error handling."""
        current_code = original_code
        session_extra = {'session_id': session.session_id}

        self.session_logger.info(f"Starting fix iterations with {len(suggestions)} suggestions", extra=session_extra)

        for iteration in range(self.max_iterations):
            if not suggestions:
                self.session_logger.warning("No more suggestions available", extra=session_extra)
                break

            # Get the highest priority suggestion
            suggestion = suggestions.pop(0)

            self.session_logger.info(
                f"Iteration {iteration + 1}/{self.max_iterations}: Trying {suggestion.suggestion_type} - {suggestion.description} "
                f"(confidence: {suggestion.confidence:.2f})",
                extra=session_extra
            )

            # Create fix attempt
            attempt = FixAttempt(
                attempt_number=iteration + 1,
                suggestion=suggestion,
                applied_changes=[],
                test_results={},
                status=FixAttemptStatus.IN_PROGRESS
            )

            attempt_start_time = time.time()
            
            try:
                # Apply the suggested fix
                modified_code, changes = await self._apply_suggestion(
                    suggestion, current_code, file_path
                )
                attempt.applied_changes = changes
                
                # Run tests to verify the fix
                test_results = await self._run_tests(modified_code, file_path, test_files)
                attempt.test_results = test_results
                
                # Check if fix was successful
                if test_results.get("success", False):
                    attempt.status = FixAttemptStatus.SUCCESS
                    session.final_status = FixAttemptStatus.SUCCESS
                    current_code = modified_code

                    self.session_logger.info(
                        f"✅ Fix successful on iteration {iteration + 1}! Applied: {suggestion.description}",
                        extra=session_extra
                    )
                    session.fix_attempts.append(attempt)
                    break
                else:
                    attempt.status = FixAttemptStatus.FAILED
                    attempt.error_message = test_results.get("error", "Tests failed")

                    self.session_logger.warning(
                        f"❌ Fix attempt {iteration + 1} failed: {attempt.error_message}",
                        extra=session_extra
                    )

                    # Analyze the new failure and generate more suggestions
                    if test_results.get("new_error"):
                        self.session_logger.debug("Analyzing new failure for additional suggestions", extra=session_extra)
                        new_suggestions = await self._analyze_new_failure(
                            test_results["new_error"], modified_code, file_path
                        )
                        if new_suggestions:
                            suggestions.extend(new_suggestions)
                            self.session_logger.info(f"Generated {len(new_suggestions)} additional suggestions", extra=session_extra)
                
            except asyncio.TimeoutError:
                attempt.status = FixAttemptStatus.TIMEOUT
                attempt.error_message = "Fix attempt timed out"
                
            except Exception as e:
                attempt.status = FixAttemptStatus.FAILED
                attempt.error_message = str(e)
                
            finally:
                attempt.execution_time = time.time() - attempt_start_time
                session.fix_attempts.append(attempt)
        
        # Set final status if not already successful
        if session.final_status == FixAttemptStatus.PENDING:
            session.final_status = FixAttemptStatus.FAILED
    
    async def _apply_suggestion(self, 
                              suggestion: DebugSuggestion,
                              code: str,
                              file_path: str) -> Tuple[str, List[CodeEdit]]:
        """Apply a debug suggestion to the code."""
        changes = []
        
        if suggestion.code_example:
            # Try to intelligently apply the code example
            modified_code = await self._integrate_code_example(
                code, suggestion.code_example, suggestion.description
            )
            
            # Create a code edit record
            edit = CodeEdit(
                file_path=file_path,
                original_content=code,
                modified_content=modified_code,
                edit_type="fix_application",
                line_range=(0, len(code.split('\n'))),
                description=suggestion.description
            )
            
            changes.append(edit)
            return modified_code, changes
        else:
            # Generate code based on suggestion description
            modified_code = await self._generate_fix_from_description(
                code, suggestion.description, file_path
            )
            
            edit = CodeEdit(
                file_path=file_path,
                original_content=code,
                modified_content=modified_code,
                edit_type="generated_fix",
                line_range=(0, len(code.split('\n'))),
                description=suggestion.description
            )
            
            changes.append(edit)
            return modified_code, changes
    
    async def _integrate_code_example(self, 
                                    original_code: str,
                                    code_example: str,
                                    description: str) -> str:
        """Intelligently integrate a code example into the original code."""
        # This is a simplified implementation
        # In practice, this would use more sophisticated code analysis
        
        if "try:" in code_example and "except:" in code_example:
            # Add error handling
            return self._add_error_handling_wrapper(original_code, code_example)
        elif "if" in code_example and "is not None" in code_example:
            # Add null checks
            return self._add_null_checks(original_code, code_example)
        elif "import" in code_example:
            # Add imports
            return self._add_imports(original_code, code_example)
        else:
            # Generic integration
            return f"{code_example}\n\n{original_code}"
    
    async def _generate_fix_from_description(self, 
                                           code: str,
                                           description: str,
                                           file_path: str) -> str:
        """Generate a fix based on the description."""
        # Use the code generator to create a fix
        requirements = {
            "type": "fix",
            "name": "bug_fix",
            "description": description,
            "original_code": code
        }
        
        # This would typically use more sophisticated generation
        # For now, return the original code with a comment
        return f"# Fix applied: {description}\n{code}"
    
    async def _run_tests(self, 
                        code: str,
                        file_path: str,
                        test_files: Optional[List[str]]) -> Dict[str, Any]:
        """Run tests to verify the fix."""
        try:
            # Write code to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            # Run syntax check first
            try:
                compile(code, temp_file, 'exec')
            except SyntaxError as e:
                return {
                    "success": False,
                    "error": f"Syntax error: {str(e)}",
                    "new_error": e
                }
            
            # If test files are provided, run them
            if test_files:
                return await self._run_test_files(test_files, temp_file)
            else:
                # Basic execution test
                try:
                    exec(compile(code, temp_file, 'exec'))
                    return {"success": True, "message": "Code executed successfully"}
                except Exception as e:
                    return {
                        "success": False,
                        "error": str(e),
                        "new_error": e
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"Test execution failed: {str(e)}",
                "new_error": e
            }
        finally:
            # Clean up temporary file
            try:
                Path(temp_file).unlink()
            except:
                pass
    
    async def _run_test_files(self, test_files: List[str], code_file: str) -> Dict[str, Any]:
        """Run specific test files."""
        try:
            # Run pytest on the test files
            cmd = ["python", "-m", "pytest", "-v"] + test_files
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.timeout_per_attempt
            )
            
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Test execution timed out"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Test execution failed: {str(e)}"
            }
    
    async def _analyze_new_failure(self, 
                                 error: Exception,
                                 code: str,
                                 file_path: str) -> List[DebugSuggestion]:
        """Analyze a new failure and generate additional suggestions."""
        try:
            # Analyze the new error
            debug_context = await self.debugger.analyze_error(error, code, file_path)
            root_cause = await self.debugger.perform_root_cause_analysis(debug_context)
            suggestions = await self.debugger.generate_debug_suggestions(debug_context, root_cause)
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Failed to analyze new failure: {e}")
            return []

    async def _get_additional_error_insights(self,
                                           code_content: str,
                                           file_path: str,
                                           error: Exception,
                                           session_extra: Dict[str, str]) -> List[DebugSuggestion]:
        """Get additional error insights using the error detection agent."""
        try:
            self.session_logger.debug("Running additional error analysis", extra=session_extra)

            # Create a temporary file for analysis
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code_content)
                temp_file = f.name

            try:
                # Use error detection agent to find potential issues
                task = Task(
                    name='detect_errors_for_fix',
                    task_type='detect_errors',
                    parameters={'file_path': temp_file}
                )

                result = await self.error_detection_agent.execute_task(task)

                if result.status == TaskStatus.COMPLETED and result.result:
                    detections = result.result.get('detections', [])

                    # Convert error detections to debug suggestions
                    suggestions = []
                    for detection in detections:
                        if detection.get('severity') in ['high', 'critical']:
                            suggestion = DebugSuggestion(
                                suggestion_type='error_detection',
                                description=f"Fix {detection.get('type', 'issue')}: {detection.get('description', '')}",
                                code_example=detection.get('suggested_fix', ''),
                                confidence=detection.get('confidence', 0.5),
                                priority=1 if detection.get('severity') == 'critical' else 2
                            )
                            suggestions.append(suggestion)

                    self.session_logger.info(f"Found {len(suggestions)} additional insights", extra=session_extra)
                    return suggestions

            finally:
                # Clean up temporary file
                try:
                    Path(temp_file).unlink()
                except:
                    pass

        except Exception as e:
            self.session_logger.warning(f"Failed to get additional insights: {e}", extra=session_extra)

        return []

    def _update_performance_metrics(self, session: BugFixSession) -> None:
        """Update performance metrics based on session results."""
        if session.final_status == FixAttemptStatus.SUCCESS:
            self._performance_metrics['successful_sessions'] += 1

            # Track effective fix types
            for attempt in session.fix_attempts:
                if attempt.status == FixAttemptStatus.SUCCESS:
                    fix_type = attempt.suggestion.suggestion_type
                    self._performance_metrics['most_effective_fixes'][fix_type] = \
                        self._performance_metrics['most_effective_fixes'].get(fix_type, 0) + 1
                    break

        # Update averages
        total_sessions = self._performance_metrics['total_fix_sessions']
        if total_sessions > 0:
            total_iterations = sum(len(s.fix_attempts) for s in self._fix_history)
            self._performance_metrics['average_iterations'] = total_iterations / total_sessions

            total_time = sum(s.total_time for s in self._fix_history)
            self._performance_metrics['average_time_per_session'] = total_time / total_sessions

    def _calculate_success_rate(self, session: BugFixSession) -> float:
        """Calculate the success rate of the session."""
        if not session.fix_attempts:
            return 0.0
        
        successful_attempts = sum(1 for attempt in session.fix_attempts 
                                if attempt.status == FixAttemptStatus.SUCCESS)
        
        return successful_attempts / len(session.fix_attempts)
    
    def _extract_lessons_learned(self, session: BugFixSession) -> List[str]:
        """Extract lessons learned from the session."""
        lessons = []
        
        # Analyze successful attempts
        successful_attempts = [a for a in session.fix_attempts 
                             if a.status == FixAttemptStatus.SUCCESS]
        
        if successful_attempts:
            successful_attempt = successful_attempts[0]
            lessons.append(f"Successful fix type: {successful_attempt.suggestion.suggestion_type}")
            lessons.append(f"Effective approach: {successful_attempt.suggestion.description}")
        
        # Analyze failed attempts
        failed_attempts = [a for a in session.fix_attempts 
                         if a.status == FixAttemptStatus.FAILED]
        
        if len(failed_attempts) > 1:
            lessons.append("Multiple attempts were needed, consider more thorough initial analysis")
        
        # Analyze timing
        avg_time = sum(a.execution_time for a in session.fix_attempts) / len(session.fix_attempts)
        if avg_time > 60:
            lessons.append("Fix attempts took longer than expected, consider optimization")
        
        return lessons
    
    # Helper methods for code modification
    def _add_error_handling_wrapper(self, original_code: str, error_handling_code: str) -> str:
        """Add error handling wrapper to code."""
        lines = original_code.split('\n')
        
        # Find the main logic (simplified)
        main_logic_start = 0
        for i, line in enumerate(lines):
            if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('import'):
                main_logic_start = i
                break
        
        # Wrap main logic in try-except
        indented_logic = '\n'.join('    ' + line for line in lines[main_logic_start:])
        
        return f"""try:
{indented_logic}
except Exception as e:
    # Handle error
    logging.error(f"Error occurred: {{e}}")
    raise"""
    
    def _add_null_checks(self, original_code: str, null_check_code: str) -> str:
        """Add null checks to code."""
        # Simplified null check addition
        return f"{null_check_code}\n{original_code}"
    
    def _add_imports(self, original_code: str, import_code: str) -> str:
        """Add imports to code."""
        lines = original_code.split('\n')
        
        # Find where to insert imports
        insert_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('import') or line.strip().startswith('from'):
                insert_index = i + 1
            elif line.strip() and not line.strip().startswith('#'):
                break
        
        lines.insert(insert_index, import_code)
        return '\n'.join(lines)
    
    def get_fix_history(self) -> List[BugFixSession]:
        """Get the history of bug fix sessions."""
        return self._fix_history.copy()
    
    def get_success_statistics(self) -> Dict[str, Any]:
        """Get statistics about fix success rates."""
        if not self._fix_history:
            return {"total_sessions": 0, "success_rate": 0.0}
        
        successful_sessions = sum(1 for session in self._fix_history 
                                if session.final_status == FixAttemptStatus.SUCCESS)
        
        total_attempts = sum(len(session.fix_attempts) for session in self._fix_history)
        successful_attempts = sum(
            sum(1 for attempt in session.fix_attempts 
                if attempt.status == FixAttemptStatus.SUCCESS)
            for session in self._fix_history
        )
        
        return {
            "total_sessions": len(self._fix_history),
            "successful_sessions": successful_sessions,
            "session_success_rate": successful_sessions / len(self._fix_history),
            "total_attempts": total_attempts,
            "successful_attempts": successful_attempts,
            "attempt_success_rate": successful_attempts / total_attempts if total_attempts > 0 else 0.0,
            "average_attempts_per_session": total_attempts / len(self._fix_history)
        }

    def get_comprehensive_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance and usage metrics."""
        base_stats = self.get_success_statistics()

        return {
            **base_stats,
            **self._performance_metrics,
            "recent_sessions": [
                {
                    "session_id": session.session_id,
                    "status": session.final_status.value,
                    "attempts": len(session.fix_attempts),
                    "time": session.total_time,
                    "error_type": session.initial_error.error_type
                }
                for session in self._fix_history[-10:]  # Last 10 sessions
            ]
        }

    def reset_metrics(self) -> None:
        """Reset performance metrics (useful for testing or fresh starts)."""
        self._performance_metrics = {
            'total_fix_sessions': 0,
            'successful_sessions': 0,
            'average_iterations': 0.0,
            'average_time_per_session': 0.0,
            'common_error_types': {},
            'most_effective_fixes': {}
        }
        self._fix_history.clear()
        self.logger.info("Bug fix loop metrics reset")
