"""
Multi-agent types and data structures for the agent framework.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union, Callable, Awaitable
from uuid import UUID

from pydantic import BaseModel
from .types import Task, TaskResult, TaskStatus
from .config import ModelConfig

class AgentStatus(Enum):
    """Status of an agent."""
    INACTIVE: str
    ACTIVE: str
    BUSY: str
    ERROR: str
    MAINTENANCE: str

class AgentCapability(Enum):
    """Capabilities that agents can provide."""
    CODE_ANALYSIS: str
    CODE_GENERATION: str
    TESTING: str
    DOCUMENTATION: str
    REFACTORING: str
    ERROR_DETECTION: str
    OPTIMIZATION: str
    DEBUGGING: str

class CoordinationStrategy(Enum):
    """Strategies for coordinating multiple agents."""
    ROUND_ROBIN: str
    PRIORITY_BASED: str
    LOAD_BALANCED: str
    CAPABILITY_BASED: str
    CONSENSUS: str

class ConflictResolutionStrategy(Enum):
    """Strategies for resolving conflicts between agents."""
    VOTING: str
    PRIORITY: str
    CONSENSUS: str
    FIRST_RESPONSE: str
    BEST_SCORE: str

@dataclass
class AgentInfo:
    """Information about an agent."""
    id: str
    name: str
    agent_type: str
    status: AgentStatus
    capabilities: List[AgentCapability]
    current_tasks: List[UUID]
    completed_tasks: int
    failed_tasks: int
    average_response_time: float
    last_activity: datetime
    metadata: Dict[str, Any]

@dataclass
class TaskDelegationRequest:
    """Request to delegate a task to an agent."""
    task: Task
    preferred_agent_id: Optional[str]
    required_capabilities: List[AgentCapability]
    priority: int
    deadline: Optional[datetime]
    context: Dict[str, Any]

@dataclass
class TaskDelegationResponse:
    """Response to a task delegation request."""
    request_id: UUID
    accepted: bool
    assigned_agent_id: Optional[str]
    estimated_completion_time: Optional[datetime]
    reason: Optional[str]
    alternative_agents: List[str]

@dataclass
class AgentCommunicationMessage:
    """Message for inter-agent communication."""
    sender_id: str
    recipient_id: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[UUID]

@dataclass
class ConflictResolutionRequest:
    """Request to resolve a conflict between agents."""
    conflict_id: UUID
    conflicting_agents: List[str]
    conflict_type: str
    task_id: Optional[UUID]
    conflicting_results: List[TaskResult]
    context: Dict[str, Any]

@dataclass
class ConflictResolutionResult:
    """Result of conflict resolution."""
    conflict_id: UUID
    resolution_strategy: ConflictResolutionStrategy
    winning_agent_id: str
    final_result: TaskResult
    confidence_score: float
    resolution_time: float

@dataclass
class CoordinationContext:
    """Context for agent coordination."""
    coordination_id: UUID
    participating_agents: List[str]
    coordination_strategy: CoordinationStrategy
    task_dependencies: Dict[UUID, List[UUID]]
    shared_state: Dict[str, Any]
    coordination_metadata: Dict[str, Any]

class MultiAgentEvent(BaseModel):
    """Event in the multi-agent system."""
    event_id: UUID
    event_type: str
    source_agent_id: str
    target_agent_ids: List[str]
    timestamp: datetime
    payload: Dict[str, Any]
    correlation_id: Optional[UUID]

class AgentInterface(ABC):
    """Abstract interface for agents in the multi-agent system."""
    
    @property
    @abstractmethod
    def agent_id(self) -> str: ...
    
    @property
    @abstractmethod
    def agent_type(self) -> str: ...
    
    @property
    @abstractmethod
    def capabilities(self) -> List[AgentCapability]: ...
    
    @abstractmethod
    async def process_task(self, task: Task) -> TaskResult: ...
    
    @abstractmethod
    async def handle_message(self, message: AgentCommunicationMessage) -> Optional[AgentCommunicationMessage]: ...
    
    @abstractmethod
    async def get_status(self) -> AgentStatus: ...
    
    @abstractmethod
    async def get_load_info(self) -> Dict[str, Any]: ...
    
    @abstractmethod
    async def can_handle_task(self, task: Task) -> bool: ...

AgentFactory = Callable[[Dict[str, Any]], AgentInterface]
MessageHandler = Callable[[AgentCommunicationMessage], Awaitable[Optional[AgentCommunicationMessage]]]
ConflictResolver = Callable[[ConflictResolutionRequest], Awaitable[ConflictResolutionResult]]
