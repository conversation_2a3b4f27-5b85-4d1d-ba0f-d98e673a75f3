"""
Automatic evaluation cycles for code quality, performance, and security assessment.
"""

import asyncio
import logging
import subprocess
import tempfile
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from enum import Enum
import ast
import re

from .code_analysis import CodeAnalyzer
from ..agents.testing_agent import TestingAgent
from ..agents.error_detection_agent import ErrorDetectionAgent
from ..agents.optimization_agent import OptimizationAgent
from ..core.types import Task, TaskResult, TaskStatus


class EvaluationStatus(Enum):
    """Status of an evaluation."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class QualityLevel(Enum):
    """Quality levels for evaluation results."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class EvaluationResult:
    """Result of a single evaluation."""
    evaluation_type: str
    status: EvaluationStatus
    quality_level: QualityLevel
    score: float  # 0.0 to 1.0
    metrics: Dict[str, Any]
    issues: List[Dict[str, Any]]
    recommendations: List[str]
    execution_time: float
    timestamp: float = field(default_factory=time.time)


@dataclass
class EvaluationCycle:
    """Complete evaluation cycle results."""
    cycle_id: str
    file_path: str
    evaluations: Dict[str, EvaluationResult]
    overall_quality: QualityLevel
    overall_score: float
    critical_issues: List[Dict[str, Any]]
    improvement_plan: List[str]
    rollback_recommended: bool
    total_time: float
    timestamp: float = field(default_factory=time.time)


class AutomaticEvaluationCycles:
    """Enhanced automatic evaluation system for comprehensive code quality assessment."""

    def __init__(self, enable_advanced_features: bool = True):
        self.logger = logging.getLogger(__name__)
        self.analyzer = CodeAnalyzer()
        self.testing_agent = TestingAgent()

        # Create a minimal config for the agents
        from ..core.config import FrameworkConfig
        minimal_config = FrameworkConfig()
        self.error_detection_agent = ErrorDetectionAgent(minimal_config)
        self.optimization_agent = OptimizationAgent(minimal_config)
        self.enable_advanced_features = enable_advanced_features
        self._evaluation_history: List[EvaluationCycle] = []

        # Enhanced quality thresholds with more granular levels
        self.quality_thresholds = {
            "excellent": 0.9,
            "good": 0.75,
            "fair": 0.6,
            "poor": 0.4,
            "critical": 0.0
        }

        # Performance tracking
        self._performance_metrics = {
            'total_evaluations': 0,
            'average_evaluation_time': 0.0,
            'quality_distribution': {level.value: 0 for level in QualityLevel},
            'most_common_issues': {},
            'rollback_rate': 0.0
        }

        # Enhanced logging setup
        self._setup_enhanced_logging()

    def _setup_enhanced_logging(self):
        """Setup enhanced logging for evaluation cycles."""
        # Create a dedicated logger for evaluation cycles
        self.cycle_logger = logging.getLogger(f"{__name__}.cycles")

        # Configure detailed logging format
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [Cycle: %(cycle_id)s] - %(message)s'
        )

        # Add cycle context to logger
        self.cycle_logger.setLevel(logging.DEBUG)

    async def run_evaluation_cycle(self,
                                 code_content: str,
                                 file_path: str,
                                 evaluation_types: Optional[List[str]] = None) -> EvaluationCycle:
        """
        Run a complete evaluation cycle on code.
        
        Args:
            code_content: The code to evaluate
            file_path: Path to the code file
            evaluation_types: Types of evaluations to run
            
        Returns:
            Complete evaluation cycle results
        """
        cycle_id = f"eval_cycle_{int(time.time())}"
        start_time = time.time()

        # Enhanced logging with cycle context
        cycle_extra = {'cycle_id': cycle_id}

        if evaluation_types is None:
            evaluation_types = [
                "static_analysis",
                "code_quality",
                "performance_analysis",
                "security_scan",
                "test_coverage",
                "complexity_analysis"
            ]

            # Add advanced evaluations if enabled
            if self.enable_advanced_features:
                evaluation_types.extend([
                    "error_detection",
                    "optimization_analysis",
                    "maintainability_analysis",
                    "documentation_quality"
                ])

        self.cycle_logger.info(f"Starting evaluation cycle: {cycle_id} for {file_path}", extra=cycle_extra)
        self.cycle_logger.info(f"Running {len(evaluation_types)} evaluation types: {evaluation_types}", extra=cycle_extra)

        # Update performance metrics
        self._performance_metrics['total_evaluations'] += 1

        evaluations = {}
        
        try:
            # Run each evaluation type with enhanced progress tracking
            for i, eval_type in enumerate(evaluation_types, 1):
                self.cycle_logger.info(f"Running {eval_type} evaluation ({i}/{len(evaluation_types)})", extra=cycle_extra)
                eval_start_time = time.time()

                try:
                    result = await self._run_evaluation(eval_type, code_content, file_path, cycle_extra)
                    evaluations[eval_type] = result

                    eval_time = time.time() - eval_start_time
                    self.cycle_logger.info(
                        f"✅ {eval_type} completed: {result.quality_level.value} "
                        f"(score: {result.score:.2f}, time: {eval_time:.2f}s)",
                        extra=cycle_extra
                    )

                except Exception as e:
                    eval_time = time.time() - eval_start_time
                    self.cycle_logger.error(f"❌ Evaluation {eval_type} failed: {e}", extra=cycle_extra)
                    evaluations[eval_type] = EvaluationResult(
                        evaluation_type=eval_type,
                        status=EvaluationStatus.FAILED,
                        quality_level=QualityLevel.CRITICAL,
                        score=0.0,
                        metrics={},
                        issues=[{"type": "evaluation_error", "message": str(e)}],
                        recommendations=[f"Fix evaluation error: {str(e)}"],
                        execution_time=eval_time
                    )
            
            # Calculate overall results
            overall_score = self._calculate_overall_score(evaluations)
            overall_quality = self._determine_quality_level(overall_score)
            critical_issues = self._extract_critical_issues(evaluations)
            improvement_plan = self._generate_improvement_plan(evaluations)
            rollback_recommended = self._should_recommend_rollback(evaluations)
            
            cycle = EvaluationCycle(
                cycle_id=cycle_id,
                file_path=file_path,
                evaluations=evaluations,
                overall_quality=overall_quality,
                overall_score=overall_score,
                critical_issues=critical_issues,
                improvement_plan=improvement_plan,
                rollback_recommended=rollback_recommended,
                total_time=time.time() - start_time
            )
            
            # Add to history
            self._evaluation_history.append(cycle)

            # Update performance metrics
            self._update_performance_metrics(cycle)

            # Enhanced completion logging
            self.cycle_logger.info(
                f"🎯 Evaluation cycle completed: {overall_quality.value} (score: {overall_score:.2f}, "
                f"time: {cycle.total_time:.2f}s, issues: {len(critical_issues)})",
                extra=cycle_extra
            )

            if rollback_recommended:
                self.cycle_logger.warning("⚠️  Rollback recommended due to critical issues", extra=cycle_extra)

            if improvement_plan:
                self.cycle_logger.info(f"📋 Improvement plan: {len(improvement_plan)} recommendations", extra=cycle_extra)

            return cycle
            
        except Exception as e:
            self.logger.error(f"Evaluation cycle failed: {e}")
            return EvaluationCycle(
                cycle_id=cycle_id,
                file_path=file_path,
                evaluations=evaluations,
                overall_quality=QualityLevel.CRITICAL,
                overall_score=0.0,
                critical_issues=[{"type": "cycle_error", "message": str(e)}],
                improvement_plan=["Manual review required due to evaluation failure"],
                rollback_recommended=True,
                total_time=time.time() - start_time
            )
    
    async def _run_evaluation(self,
                            eval_type: str,
                            code_content: str,
                            file_path: str,
                            cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run a specific type of evaluation."""
        start_time = time.time()
        
        try:
            if eval_type == "static_analysis":
                return await self._run_static_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "code_quality":
                return await self._run_code_quality_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "performance_analysis":
                return await self._run_performance_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "security_scan":
                return await self._run_security_scan(code_content, file_path, cycle_extra)
            elif eval_type == "test_coverage":
                return await self._run_test_coverage_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "complexity_analysis":
                return await self._run_complexity_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "error_detection":
                return await self._run_error_detection_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "optimization_analysis":
                return await self._run_optimization_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "maintainability_analysis":
                return await self._run_maintainability_analysis(code_content, file_path, cycle_extra)
            elif eval_type == "documentation_quality":
                return await self._run_documentation_quality_analysis(code_content, file_path, cycle_extra)
            else:
                raise ValueError(f"Unknown evaluation type: {eval_type}")
                
        except Exception as e:
            return EvaluationResult(
                evaluation_type=eval_type,
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "evaluation_error", "message": str(e)}],
                recommendations=[],
                execution_time=time.time() - start_time
            )
    
    def _update_performance_metrics(self, cycle: EvaluationCycle):
        """Update performance metrics based on evaluation cycle results."""
        # Update quality distribution
        self._performance_metrics['quality_distribution'][cycle.overall_quality.value] += 1

        # Update average evaluation time
        total_evals = self._performance_metrics['total_evaluations']
        current_avg = self._performance_metrics['average_evaluation_time']
        self._performance_metrics['average_evaluation_time'] = \
            ((current_avg * (total_evals - 1)) + cycle.total_time) / total_evals

        # Update rollback rate
        if cycle.rollback_recommended:
            rollback_count = sum(1 for c in self._evaluation_history if c.rollback_recommended)
            self._performance_metrics['rollback_rate'] = rollback_count / len(self._evaluation_history)

        # Track common issues
        for issue in cycle.critical_issues:
            issue_type = issue.get('type', 'unknown')
            self._performance_metrics['most_common_issues'][issue_type] = \
                self._performance_metrics['most_common_issues'].get(issue_type, 0) + 1

    async def _run_static_analysis(self, code_content: str, file_path: str, cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run static code analysis."""
        issues = []
        recommendations = []
        metrics = {}
        
        try:
            # Parse AST for static analysis
            tree = ast.parse(code_content)
            
            # Check for common issues
            issues.extend(self._check_unused_imports(tree, code_content))
            issues.extend(self._check_undefined_variables(tree, code_content))
            issues.extend(self._check_style_violations(code_content))
            
            # Calculate metrics
            metrics = {
                "total_lines": len(code_content.split('\n')),
                "code_lines": len([line for line in code_content.split('\n') if line.strip()]),
                "comment_lines": len([line for line in code_content.split('\n') if line.strip().startswith('#')]),
                "function_count": len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]),
                "class_count": len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)])
            }
            
            # Generate recommendations
            if metrics["comment_lines"] / max(metrics["code_lines"], 1) < 0.1:
                recommendations.append("Add more comments to improve code documentation")
            
            if len(issues) == 0:
                recommendations.append("Code passes static analysis checks")
            
            # Calculate score
            score = max(0.0, 1.0 - (len(issues) * 0.1))
            
            return EvaluationResult(
                evaluation_type="static_analysis",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(score),
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="static_analysis",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "analysis_error", "message": str(e)}],
                recommendations=["Fix syntax errors before analysis"],
                execution_time=0.0
            )
    
    async def _run_code_quality_analysis(self, code_content: str, file_path: str) -> EvaluationResult:
        """Run code quality analysis."""
        try:
            context = await self.analyzer.analyze_code_comprehensive(code_content, file_path)
            
            issues = context.potential_issues
            metrics = context.complexity_metrics
            
            recommendations = []
            
            # Check complexity
            if metrics.get("cyclomatic_complexity", 0) > 10:
                recommendations.append("Reduce cyclomatic complexity by breaking down complex functions")
            
            # Check function length
            long_functions = [f for f in context.functions if len(f.get("calls", [])) > 10]
            if long_functions:
                recommendations.append("Consider breaking down long functions")
            
            # Calculate score based on issues and complexity
            complexity_penalty = min(0.3, metrics.get("cyclomatic_complexity", 0) * 0.02)
            issue_penalty = len(issues) * 0.1
            score = max(0.0, 1.0 - complexity_penalty - issue_penalty)
            
            return EvaluationResult(
                evaluation_type="code_quality",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(score),
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="code_quality",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "quality_analysis_error", "message": str(e)}],
                recommendations=["Manual code review required"],
                execution_time=0.0
            )
    
    async def _run_performance_analysis(self, code_content: str, file_path: str) -> EvaluationResult:
        """Run performance analysis."""
        issues = []
        recommendations = []
        metrics = {}
        
        try:
            # Analyze for performance anti-patterns
            tree = ast.parse(code_content)
            
            # Check for nested loops
            nested_loops = self._find_nested_loops(tree)
            if nested_loops:
                issues.append({
                    "type": "performance",
                    "severity": "medium",
                    "message": f"Found {len(nested_loops)} nested loops that may impact performance"
                })
                recommendations.append("Consider optimizing nested loops or using more efficient algorithms")
            
            # Check for string concatenation in loops
            string_concat_in_loops = self._find_string_concat_in_loops(tree)
            if string_concat_in_loops:
                issues.append({
                    "type": "performance",
                    "severity": "medium",
                    "message": "String concatenation in loops detected"
                })
                recommendations.append("Use list.join() or f-strings instead of string concatenation in loops")
            
            # Calculate performance score
            performance_issues = len([i for i in issues if i.get("type") == "performance"])
            score = max(0.0, 1.0 - (performance_issues * 0.2))
            
            metrics = {
                "nested_loops": len(nested_loops),
                "string_concat_issues": len(string_concat_in_loops),
                "performance_score": score
            }
            
            return EvaluationResult(
                evaluation_type="performance_analysis",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(score),
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="performance_analysis",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "performance_analysis_error", "message": str(e)}],
                recommendations=["Manual performance review required"],
                execution_time=0.0
            )
    
    async def _run_security_scan(self, code_content: str, file_path: str, cycle_extra: Dict[str, str] = None) -> EvaluationResult:
        """Run security vulnerability scan."""
        issues = []
        recommendations = []
        metrics = {}
        
        try:
            # Check for common security issues
            security_issues = []

            # Check for hardcoded secrets
            if re.search(r'password\s*=\s*["\'][^"\']+["\']', code_content, re.IGNORECASE):
                security_issues.append({
                    "type": "security",
                    "severity": "high",
                    "message": "Potential hardcoded password detected"
                })
            
            # Check for SQL injection vulnerabilities
            if (re.search(r'execute\s*\(\s*["\'].*%.*["\']', code_content) or
                re.search(r'["\'].*%.*["\'].*%', code_content) or
                re.search(r'SELECT.*%.*FROM', code_content, re.IGNORECASE)):
                security_issues.append({
                    "type": "security",
                    "severity": "high",
                    "message": "Potential SQL injection vulnerability"
                })
            
            # Check for eval usage
            if 'eval(' in code_content:
                security_issues.append({
                    "type": "security",
                    "severity": "critical",
                    "message": "Use of eval() function detected - security risk"
                })
            
            issues.extend(security_issues)
            
            # Generate recommendations
            if security_issues:
                recommendations.append("Review and fix security vulnerabilities")
                recommendations.append("Use parameterized queries and avoid eval()")
            else:
                recommendations.append("No obvious security issues detected")
            
            # Calculate security score
            critical_issues = len([i for i in security_issues if i.get("severity") == "critical"])
            high_issues = len([i for i in security_issues if i.get("severity") == "high"])
            
            score = max(0.0, 1.0 - (critical_issues * 0.5) - (high_issues * 0.3))
            
            metrics = {
                "critical_security_issues": critical_issues,
                "high_security_issues": high_issues,
                "total_security_issues": len(security_issues)
            }
            
            return EvaluationResult(
                evaluation_type="security_scan",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(score),
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="security_scan",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "security_scan_error", "message": str(e)}],
                recommendations=["Manual security review required"],
                execution_time=0.0
            )
    
    async def _run_test_coverage_analysis(self, code_content: str, file_path: str) -> EvaluationResult:
        """Run test coverage analysis."""
        # This is a simplified implementation
        # In practice, this would integrate with coverage tools
        
        try:
            # Analyze testability
            tree = ast.parse(code_content)
            functions = [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
            classes = [n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]
            
            testable_units = len(functions) + len(classes)
            
            # Estimate coverage (simplified)
            # In practice, this would run actual coverage tools
            estimated_coverage = 0.7  # Placeholder
            
            issues = []
            recommendations = []
            
            if estimated_coverage < 0.8:
                issues.append({
                    "type": "coverage",
                    "severity": "medium",
                    "message": f"Test coverage is below recommended threshold ({estimated_coverage:.1%})"
                })
                recommendations.append("Increase test coverage to at least 80%")
            
            if testable_units > 0:
                recommendations.append(f"Consider writing tests for {testable_units} testable units")
            
            metrics = {
                "testable_units": testable_units,
                "estimated_coverage": estimated_coverage,
                "functions": len(functions),
                "classes": len(classes)
            }
            
            return EvaluationResult(
                evaluation_type="test_coverage",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(estimated_coverage),
                score=estimated_coverage,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="test_coverage",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "coverage_analysis_error", "message": str(e)}],
                recommendations=["Manual test coverage review required"],
                execution_time=0.0
            )
    
    async def _run_complexity_analysis(self, code_content: str, file_path: str) -> EvaluationResult:
        """Run complexity analysis."""
        try:
            context = await self.analyzer.analyze_code_comprehensive(code_content, file_path)
            
            complexity_metrics = context.complexity_metrics
            cyclomatic_complexity = complexity_metrics.get("cyclomatic_complexity", 0)
            
            issues = []
            recommendations = []
            
            # Evaluate complexity levels
            if cyclomatic_complexity > 15:
                issues.append({
                    "type": "complexity",
                    "severity": "high",
                    "message": f"High cyclomatic complexity ({cyclomatic_complexity})"
                })
                recommendations.append("Refactor to reduce cyclomatic complexity")
            elif cyclomatic_complexity > 10:
                issues.append({
                    "type": "complexity",
                    "severity": "medium",
                    "message": f"Moderate cyclomatic complexity ({cyclomatic_complexity})"
                })
                recommendations.append("Consider refactoring complex functions")
            
            # Calculate complexity score
            score = max(0.0, 1.0 - (cyclomatic_complexity * 0.05))
            
            return EvaluationResult(
                evaluation_type="complexity_analysis",
                status=EvaluationStatus.COMPLETED,
                quality_level=self._determine_quality_level(score),
                score=score,
                metrics=complexity_metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=0.0
            )
            
        except Exception as e:
            return EvaluationResult(
                evaluation_type="complexity_analysis",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "complexity_analysis_error", "message": str(e)}],
                recommendations=["Manual complexity review required"],
                execution_time=0.0
            )
    
    def _calculate_overall_score(self, evaluations: Dict[str, EvaluationResult]) -> float:
        """Calculate overall score from individual evaluations."""
        if not evaluations:
            return 0.0
        
        # Weight different evaluation types
        weights = {
            "static_analysis": 0.2,
            "code_quality": 0.25,
            "performance_analysis": 0.15,
            "security_scan": 0.25,
            "test_coverage": 0.1,
            "complexity_analysis": 0.05
        }
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for eval_type, result in evaluations.items():
            if result.status == EvaluationStatus.COMPLETED:
                weight = weights.get(eval_type, 0.1)
                weighted_sum += result.score * weight
                total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level from score."""
        if score >= self.quality_thresholds["excellent"]:
            return QualityLevel.EXCELLENT
        elif score >= self.quality_thresholds["good"]:
            return QualityLevel.GOOD
        elif score >= self.quality_thresholds["fair"]:
            return QualityLevel.FAIR
        elif score >= self.quality_thresholds["poor"]:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _extract_critical_issues(self, evaluations: Dict[str, EvaluationResult]) -> List[Dict[str, Any]]:
        """Extract critical issues from all evaluations."""
        critical_issues = []
        
        for eval_type, result in evaluations.items():
            for issue in result.issues:
                if issue.get("severity") in ["critical", "high"]:
                    issue["evaluation_type"] = eval_type
                    critical_issues.append(issue)
        
        return critical_issues
    
    def _generate_improvement_plan(self, evaluations: Dict[str, EvaluationResult]) -> List[str]:
        """Generate improvement plan from evaluation results."""
        plan = []
        
        # Collect all recommendations
        for eval_type, result in evaluations.items():
            for recommendation in result.recommendations:
                if recommendation not in plan:
                    plan.append(recommendation)
        
        # Prioritize based on critical issues
        critical_issues = self._extract_critical_issues(evaluations)
        if critical_issues:
            plan.insert(0, f"Address {len(critical_issues)} critical issues immediately")
        
        return plan

    async def _run_error_detection_analysis(self, code_content: str, file_path: str, cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run error detection analysis using the error detection agent."""
        start_time = time.time()

        try:
            # Create temporary file for analysis
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code_content)
                temp_file = f.name

            try:
                # Use error detection agent
                task = Task(
                    name='detect_errors_evaluation',
                    task_type='detect_errors',
                    parameters={'file_path': temp_file}
                )

                result = await self.error_detection_agent.execute_task(task)

                if result.status == TaskStatus.COMPLETED and result.result:
                    detections = result.result.get('detections', [])

                    # Convert detections to evaluation format
                    issues = []
                    recommendations = []

                    for detection in detections:
                        issues.append({
                            'type': detection.get('type', 'unknown'),
                            'severity': detection.get('severity', 'medium'),
                            'message': detection.get('description', ''),
                            'line': detection.get('line_number', 0),
                            'confidence': detection.get('confidence', 0.5)
                        })

                        if detection.get('suggested_fix'):
                            recommendations.append(f"Fix {detection.get('type', 'issue')}: {detection.get('suggested_fix')}")

                    # Calculate score based on severity and count
                    critical_count = sum(1 for issue in issues if issue['severity'] == 'critical')
                    high_count = sum(1 for issue in issues if issue['severity'] == 'high')
                    medium_count = sum(1 for issue in issues if issue['severity'] == 'medium')

                    # Score calculation (lower is better for errors)
                    penalty = (critical_count * 0.3) + (high_count * 0.2) + (medium_count * 0.1)
                    score = max(0.0, 1.0 - penalty)

                    quality_level = self._determine_quality_level(score)

                    return EvaluationResult(
                        evaluation_type="error_detection",
                        status=EvaluationStatus.COMPLETED,
                        quality_level=quality_level,
                        score=score,
                        metrics={
                            'total_issues': len(issues),
                            'critical_issues': critical_count,
                            'high_issues': high_count,
                            'medium_issues': medium_count
                        },
                        issues=issues,
                        recommendations=recommendations,
                        execution_time=time.time() - start_time
                    )

            finally:
                # Clean up temporary file
                try:
                    Path(temp_file).unlink()
                except:
                    pass

        except Exception as e:
            self.cycle_logger.warning(f"Error detection analysis failed: {e}", extra=cycle_extra)

        # Fallback result
        return EvaluationResult(
            evaluation_type="error_detection",
            status=EvaluationStatus.FAILED,
            quality_level=QualityLevel.CRITICAL,
            score=0.0,
            metrics={},
            issues=[{"type": "analysis_error", "message": "Error detection analysis failed"}],
            recommendations=["Manual code review recommended"],
            execution_time=time.time() - start_time
        )

    async def _run_optimization_analysis(self, code_content: str, file_path: str, cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run optimization analysis using the optimization agent."""
        start_time = time.time()

        try:
            # Create temporary file for analysis
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code_content)
                temp_file = f.name

            try:
                # Use optimization agent
                task = Task(
                    name='analyze_optimizations_evaluation',
                    task_type='analyze_optimizations',
                    parameters={'file_path': temp_file}
                )

                result = await self.optimization_agent.execute_task(task)

                if result.status == TaskStatus.COMPLETED and result.result:
                    suggestions = result.result.get('suggestions', [])

                    # Convert suggestions to evaluation format
                    issues = []
                    recommendations = []

                    for suggestion in suggestions:
                        if suggestion.get('impact') in ['high', 'medium']:
                            issues.append({
                                'type': 'optimization_opportunity',
                                'severity': 'medium' if suggestion.get('impact') == 'high' else 'low',
                                'message': suggestion.get('description', ''),
                                'line': suggestion.get('line_number', 0),
                                'impact': suggestion.get('impact', 'unknown')
                            })

                            recommendations.append(f"Optimize: {suggestion.get('description', '')}")

                    # Score based on optimization opportunities (more opportunities = lower score)
                    high_impact = sum(1 for s in suggestions if s.get('impact') == 'high')
                    medium_impact = sum(1 for s in suggestions if s.get('impact') == 'medium')

                    # Score calculation
                    penalty = (high_impact * 0.15) + (medium_impact * 0.1)
                    score = max(0.0, 1.0 - penalty)

                    quality_level = self._determine_quality_level(score)

                    return EvaluationResult(
                        evaluation_type="optimization_analysis",
                        status=EvaluationStatus.COMPLETED,
                        quality_level=quality_level,
                        score=score,
                        metrics={
                            'total_suggestions': len(suggestions),
                            'high_impact': high_impact,
                            'medium_impact': medium_impact,
                            'potential_improvements': len(recommendations)
                        },
                        issues=issues,
                        recommendations=recommendations,
                        execution_time=time.time() - start_time
                    )

            finally:
                # Clean up temporary file
                try:
                    Path(temp_file).unlink()
                except:
                    pass

        except Exception as e:
            self.cycle_logger.warning(f"Optimization analysis failed: {e}", extra=cycle_extra)

        # Fallback result
        return EvaluationResult(
            evaluation_type="optimization_analysis",
            status=EvaluationStatus.FAILED,
            quality_level=QualityLevel.CRITICAL,
            score=0.0,
            metrics={},
            issues=[{"type": "analysis_error", "message": "Optimization analysis failed"}],
            recommendations=["Manual optimization review recommended"],
            execution_time=time.time() - start_time
        )

    def _should_recommend_rollback(self, evaluations: Dict[str, EvaluationResult]) -> bool:
        """Determine if rollback should be recommended."""
        # Check for critical security issues
        security_result = evaluations.get("security_scan")
        if security_result:
            critical_security = len([i for i in security_result.issues 
                                   if i.get("severity") == "critical"])
            if critical_security > 0:
                return True
        
        # Check overall quality
        overall_score = self._calculate_overall_score(evaluations)
        if overall_score < 0.3:
            return True
        
        return False
    
    # Helper methods for specific checks
    def _check_unused_imports(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Check for unused imports."""
        # Simplified implementation
        return []
    
    def _check_undefined_variables(self, tree: ast.AST, code_content: str) -> List[Dict[str, Any]]:
        """Check for undefined variables."""
        # Simplified implementation
        return []
    
    def _check_style_violations(self, code_content: str) -> List[Dict[str, Any]]:
        """Check for style violations."""
        issues = []
        lines = code_content.split('\n')
        
        for i, line in enumerate(lines, 1):
            if len(line) > 88:
                issues.append({
                    "type": "style",
                    "severity": "low",
                    "message": f"Line {i} exceeds 88 characters",
                    "line": i
                })
        
        return issues
    
    def _find_nested_loops(self, tree: ast.AST) -> List[ast.AST]:
        """Find nested loops in the AST."""
        nested_loops = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.For, ast.While)):
                for child in ast.walk(node):
                    if child != node and isinstance(child, (ast.For, ast.While)):
                        nested_loops.append(child)
        
        return nested_loops
    
    def _find_string_concat_in_loops(self, tree: ast.AST) -> List[ast.AST]:
        """Find string concatenation in loops."""
        # Simplified implementation
        return []
    
    def get_evaluation_history(self) -> List[EvaluationCycle]:
        """Get evaluation history."""
        return self._evaluation_history.copy()
    
    def get_quality_trends(self) -> Dict[str, Any]:
        """Get quality trends over time."""
        if not self._evaluation_history:
            return {"trend": "no_data"}
        
        recent_cycles = self._evaluation_history[-10:]  # Last 10 cycles
        scores = [cycle.overall_score for cycle in recent_cycles]
        
        if len(scores) < 2:
            return {"trend": "insufficient_data", "current_score": scores[0] if scores else 0.0}
        
        # Calculate trend
        trend = "stable"
        if scores[-1] > scores[0] + 0.1:
            trend = "improving"
        elif scores[-1] < scores[0] - 0.1:
            trend = "declining"
        
        return {
            "trend": trend,
            "current_score": scores[-1],
            "average_score": sum(scores) / len(scores),
            "score_range": (min(scores), max(scores)),
            "cycles_analyzed": len(scores)
        }

    async def _run_maintainability_analysis(self, code_content: str, file_path: str, cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run maintainability analysis."""
        start_time = time.time()

        try:
            # Parse the code to analyze maintainability factors
            tree = ast.parse(code_content)

            issues = []
            recommendations = []

            # Analyze various maintainability factors
            class MaintainabilityAnalyzer(ast.NodeVisitor):
                def __init__(self):
                    self.function_lengths = []
                    self.class_sizes = []
                    self.nesting_levels = []
                    self.current_nesting = 0
                    self.docstring_coverage = {'total': 0, 'documented': 0}

                def visit_FunctionDef(self, node):
                    self.docstring_coverage['total'] += 1
                    if ast.get_docstring(node):
                        self.docstring_coverage['documented'] += 1

                    # Calculate function length
                    func_length = len([n for n in ast.walk(node) if isinstance(n, ast.stmt)])
                    self.function_lengths.append(func_length)

                    if func_length > 50:
                        issues.append({
                            'type': 'long_function',
                            'severity': 'medium',
                            'message': f"Function '{node.name}' is too long ({func_length} statements)",
                            'line': node.lineno
                        })
                        recommendations.append(f"Consider breaking down function '{node.name}' into smaller functions")

                    self.generic_visit(node)

                def visit_ClassDef(self, node):
                    self.docstring_coverage['total'] += 1
                    if ast.get_docstring(node):
                        self.docstring_coverage['documented'] += 1

                    # Calculate class size
                    class_size = len([n for n in node.body if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef))])
                    self.class_sizes.append(class_size)

                    if class_size > 20:
                        issues.append({
                            'type': 'large_class',
                            'severity': 'medium',
                            'message': f"Class '{node.name}' has too many methods ({class_size})",
                            'line': node.lineno
                        })
                        recommendations.append(f"Consider splitting class '{node.name}' into smaller classes")

                    self.generic_visit(node)

                def visit_If(self, node):
                    self.current_nesting += 1
                    self.nesting_levels.append(self.current_nesting)

                    if self.current_nesting > 4:
                        issues.append({
                            'type': 'deep_nesting',
                            'severity': 'medium',
                            'message': f"Deep nesting level ({self.current_nesting}) detected",
                            'line': node.lineno
                        })
                        recommendations.append("Consider extracting nested logic into separate functions")

                    self.generic_visit(node)
                    self.current_nesting -= 1

            analyzer = MaintainabilityAnalyzer()
            analyzer.visit(tree)

            # Calculate metrics
            avg_function_length = sum(analyzer.function_lengths) / len(analyzer.function_lengths) if analyzer.function_lengths else 0
            avg_class_size = sum(analyzer.class_sizes) / len(analyzer.class_sizes) if analyzer.class_sizes else 0
            max_nesting = max(analyzer.nesting_levels) if analyzer.nesting_levels else 0
            docstring_coverage = analyzer.docstring_coverage['documented'] / analyzer.docstring_coverage['total'] if analyzer.docstring_coverage['total'] > 0 else 1.0

            metrics = {
                'average_function_length': avg_function_length,
                'average_class_size': avg_class_size,
                'max_nesting_level': max_nesting,
                'docstring_coverage': docstring_coverage,
                'total_functions': len(analyzer.function_lengths),
                'total_classes': len(analyzer.class_sizes)
            }

            # Calculate overall maintainability score
            score = 1.0
            score -= min(0.3, (avg_function_length - 20) * 0.01) if avg_function_length > 20 else 0
            score -= min(0.2, (avg_class_size - 10) * 0.02) if avg_class_size > 10 else 0
            score -= min(0.2, (max_nesting - 3) * 0.05) if max_nesting > 3 else 0
            score += (docstring_coverage - 0.5) * 0.2 if docstring_coverage > 0.5 else -(0.5 - docstring_coverage) * 0.3
            score = max(0.0, min(1.0, score))

            quality_level = self._determine_quality_level(score)

            return EvaluationResult(
                evaluation_type="maintainability_analysis",
                status=EvaluationStatus.COMPLETED,
                quality_level=quality_level,
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=time.time() - start_time
            )

        except Exception as e:
            self.cycle_logger.warning(f"Maintainability analysis failed: {e}", extra=cycle_extra)
            return EvaluationResult(
                evaluation_type="maintainability_analysis",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "analysis_error", "message": str(e)}],
                recommendations=["Manual maintainability review recommended"],
                execution_time=time.time() - start_time
            )

    async def _run_documentation_quality_analysis(self, code_content: str, file_path: str, cycle_extra: Dict[str, str]) -> EvaluationResult:
        """Run documentation quality analysis."""
        start_time = time.time()

        try:
            tree = ast.parse(code_content)

            issues = []
            recommendations = []

            # Analyze documentation coverage and quality
            class DocAnalyzer(ast.NodeVisitor):
                def __init__(self):
                    self.functions = []
                    self.classes = []
                    self.modules_with_docstring = 0
                    self.total_modules = 1  # Current module

                def visit_Module(self, node):
                    if ast.get_docstring(node):
                        self.modules_with_docstring += 1
                    else:
                        issues.append({
                            'type': 'missing_module_docstring',
                            'severity': 'low',
                            'message': 'Module is missing a docstring',
                            'line': 1
                        })
                        recommendations.append("Add a module-level docstring describing the purpose of this module")

                    self.generic_visit(node)

                def visit_FunctionDef(self, node):
                    docstring = ast.get_docstring(node)
                    self.functions.append({
                        'name': node.name,
                        'has_docstring': docstring is not None,
                        'docstring_length': len(docstring) if docstring else 0,
                        'line': node.lineno,
                        'is_public': not node.name.startswith('_')
                    })

                    if not docstring and not node.name.startswith('_'):
                        issues.append({
                            'type': 'missing_function_docstring',
                            'severity': 'medium',
                            'message': f"Public function '{node.name}' is missing a docstring",
                            'line': node.lineno
                        })
                        recommendations.append(f"Add a docstring to function '{node.name}' describing its purpose, parameters, and return value")

                    self.generic_visit(node)

                def visit_ClassDef(self, node):
                    docstring = ast.get_docstring(node)
                    self.classes.append({
                        'name': node.name,
                        'has_docstring': docstring is not None,
                        'docstring_length': len(docstring) if docstring else 0,
                        'line': node.lineno
                    })

                    if not docstring:
                        issues.append({
                            'type': 'missing_class_docstring',
                            'severity': 'medium',
                            'message': f"Class '{node.name}' is missing a docstring",
                            'line': node.lineno
                        })
                        recommendations.append(f"Add a docstring to class '{node.name}' describing its purpose and usage")

                    self.generic_visit(node)

            analyzer = DocAnalyzer()
            analyzer.visit(tree)

            # Calculate documentation metrics
            total_functions = len(analyzer.functions)
            documented_functions = sum(1 for f in analyzer.functions if f['has_docstring'])
            public_functions = sum(1 for f in analyzer.functions if f['is_public'])
            documented_public_functions = sum(1 for f in analyzer.functions if f['has_docstring'] and f['is_public'])

            total_classes = len(analyzer.classes)
            documented_classes = sum(1 for c in analyzer.classes if c['has_docstring'])

            # Calculate coverage scores
            function_coverage = documented_functions / total_functions if total_functions > 0 else 1.0
            public_function_coverage = documented_public_functions / public_functions if public_functions > 0 else 1.0
            class_coverage = documented_classes / total_classes if total_classes > 0 else 1.0
            module_coverage = analyzer.modules_with_docstring / analyzer.total_modules

            metrics = {
                'function_documentation_coverage': function_coverage,
                'public_function_coverage': public_function_coverage,
                'class_documentation_coverage': class_coverage,
                'module_documentation_coverage': module_coverage,
                'total_functions': total_functions,
                'total_classes': total_classes,
                'documented_functions': documented_functions,
                'documented_classes': documented_classes
            }

            # Calculate overall documentation score
            score = (function_coverage * 0.3 + public_function_coverage * 0.4 +
                    class_coverage * 0.2 + module_coverage * 0.1)

            quality_level = self._determine_quality_level(score)

            return EvaluationResult(
                evaluation_type="documentation_quality",
                status=EvaluationStatus.COMPLETED,
                quality_level=quality_level,
                score=score,
                metrics=metrics,
                issues=issues,
                recommendations=recommendations,
                execution_time=time.time() - start_time
            )

        except Exception as e:
            self.cycle_logger.warning(f"Documentation quality analysis failed: {e}", extra=cycle_extra)
            return EvaluationResult(
                evaluation_type="documentation_quality",
                status=EvaluationStatus.FAILED,
                quality_level=QualityLevel.CRITICAL,
                score=0.0,
                metrics={},
                issues=[{"type": "analysis_error", "message": str(e)}],
                recommendations=["Manual documentation review recommended"],
                execution_time=time.time() - start_time
            )

    def get_comprehensive_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance and usage metrics."""
        base_stats = self.get_evaluation_statistics()

        return {
            **base_stats,
            **self._performance_metrics,
            "recent_cycles": [
                {
                    "cycle_id": cycle.cycle_id,
                    "quality": cycle.overall_quality.value,
                    "score": cycle.overall_score,
                    "time": cycle.total_time,
                    "rollback_recommended": cycle.rollback_recommended,
                    "critical_issues": len(cycle.critical_issues)
                }
                for cycle in self._evaluation_history[-10:]  # Last 10 cycles
            ]
        }

    def get_evaluation_statistics(self) -> Dict[str, Any]:
        """Get statistics about evaluation performance."""
        if not self._evaluation_history:
            return {"message": "No evaluations completed yet"}

        total_cycles = len(self._evaluation_history)
        quality_counts = {}
        for level in QualityLevel:
            quality_counts[level.value] = sum(
                1 for cycle in self._evaluation_history
                if cycle.overall_quality == level
            )

        avg_score = sum(cycle.overall_score for cycle in self._evaluation_history) / total_cycles
        avg_time = sum(cycle.total_time for cycle in self._evaluation_history) / total_cycles
        rollback_rate = sum(1 for cycle in self._evaluation_history if cycle.rollback_recommended) / total_cycles

        return {
            "total_cycles": total_cycles,
            "quality_distribution": quality_counts,
            "average_score": avg_score,
            "average_time": avg_time,
            "rollback_rate": rollback_rate
        }
