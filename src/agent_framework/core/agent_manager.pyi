"""
Agent manager for coordinating multiple agents in the framework.
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Callable, Awaitable
from uuid import UUID

from .multi_agent_types import (
    AgentInfo, AgentStatus, AgentCapability, AgentInterface,
    TaskDelegationRequest, TaskDelegationResponse, AgentCommunicationMessage,
    ConflictResolutionRequest, ConflictResolutionResult, CoordinationStrategy,
    ConflictResolutionStrategy, CoordinationContext, MultiAgentEvent
)
from .agent_registry import AgentRegistry
from .types import Task, TaskResult, TaskStatus
from ..communication.broker import MessageBroker

class AgentManager:
    """
    Manager for coordinating multiple agents in the framework.
    
    Handles agent lifecycle, task delegation, load balancing,
    and conflict resolution between agents.
    """
    
    def __init__(self, 
                 agent_registry: AgentRegistry,
                 message_broker: MessageBroker) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the agent manager."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the agent manager and all agents."""
        ...
    
    async def register_agent(self, agent: AgentInterface) -> None:
        """Register a new agent."""
        ...
    
    async def unregister_agent(self, agent_id: str) -> None:
        """Unregister an agent."""
        ...
    
    async def get_agent(self, agent_id: str) -> Optional[AgentInterface]:
        """Get an agent by ID."""
        ...
    
    async def list_agents(self) -> List[AgentInfo]:
        """List all registered agents."""
        ...
    
    async def get_agents_by_capability(self, capability: AgentCapability) -> List[AgentInterface]:
        """Get agents that have a specific capability."""
        ...
    
    async def delegate_task(self, request: TaskDelegationRequest) -> TaskDelegationResponse:
        """Delegate a task to an appropriate agent."""
        ...
    
    async def execute_task_with_agent(self, task: Task, agent_id: str) -> TaskResult:
        """Execute a task with a specific agent."""
        ...
    
    async def broadcast_message(self, message: AgentCommunicationMessage) -> List[AgentCommunicationMessage]:
        """Broadcast a message to all agents."""
        ...
    
    async def send_message(self, message: AgentCommunicationMessage) -> Optional[AgentCommunicationMessage]:
        """Send a message to a specific agent."""
        ...
    
    async def resolve_conflict(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """Resolve a conflict between agents."""
        ...
    
    async def balance_load(self) -> None:
        """Balance the load across agents."""
        ...
    
    async def monitor_agent_health(self) -> Dict[str, Any]:
        """Monitor the health of all agents."""
        ...
    
    async def get_agent_metrics(self, agent_id: str) -> Dict[str, Any]:
        """Get metrics for a specific agent."""
        ...
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get system-wide agent metrics."""
        ...
    
    def add_agent_event_handler(self, event_type: str, handler: Callable[[MultiAgentEvent], Awaitable[None]]) -> None:
        """Add an event handler for agent events."""
        ...
    
    def remove_agent_event_handler(self, event_type: str, handler: Callable[[MultiAgentEvent], Awaitable[None]]) -> None:
        """Remove an agent event handler."""
        ...
    
    # Properties
    @property
    def agent_count(self) -> int: ...
    
    @property
    def active_agent_count(self) -> int: ...
    
    @property
    def total_tasks_processed(self) -> int: ...
    
    @property
    def average_response_time(self) -> float: ...
