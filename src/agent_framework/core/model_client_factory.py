"""
Model client factory for creating different types of model clients.

This module provides a factory pattern for creating model clients based on
configuration, supporting multiple providers including OpenAI, Anthropic,
Azure, and local models following AutoGen's design patterns.
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional, Type, Union

from autogen_core.models import ChatCompletionClient

try:
    from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
except ImportError:
    OpenAIChatCompletionClient = None
    AzureOpenAIChatCompletionClient = None

try:
    from autogen_ext.models.anthropic import AnthropicChatCompletionClient
except ImportError:
    AnthropicChatCompletionClient = None

try:
    from autogen_ext.models.ollama import OllamaChatCompletionClient
except ImportError:
    OllamaChatCompletionClient = None

try:
    from autogen_ext.models.azure import AzureAIChatCompletionClient
except ImportError:
    AzureAIChatCompletionClient = None

try:
    from autogen_ext.models.semantic_kernel import SK<PERSON>hatCompletionAdapter
except ImportError:
    SKChatCompletionAdapter = None

from .config import ModelConfig, ModelProvider
from .error_handling import error_handler, ModelClientError, ConfigurationError, ErrorContext


class ModelClientFactory:
    """
    Factory for creating model clients based on configuration.
    
    This factory supports multiple model providers and follows AutoGen's
    model client patterns for consistent interface and behavior.
    """
    
    def __init__(self) -> None:
        """Initialize the model client factory."""
        self.logger = logging.getLogger(__name__)
        self._client_registry: Dict[ModelProvider, Type[ChatCompletionClient]] = {}
        self._register_default_clients()
    
    def _register_default_clients(self) -> None:
        """Register default model client implementations."""
        if OpenAIChatCompletionClient:
            self._client_registry[ModelProvider.OPENAI] = OpenAIChatCompletionClient
            self._client_registry[ModelProvider.OPENROUTER] = OpenAIChatCompletionClient
        
        if AzureOpenAIChatCompletionClient:
            self._client_registry[ModelProvider.AZURE_OPENAI] = AzureOpenAIChatCompletionClient
        
        if AnthropicChatCompletionClient:
            self._client_registry[ModelProvider.ANTHROPIC] = AnthropicChatCompletionClient
        
        if OllamaChatCompletionClient:
            self._client_registry[ModelProvider.OLLAMA] = OllamaChatCompletionClient
            self._client_registry[ModelProvider.LOCAL] = OllamaChatCompletionClient
        
        if AzureAIChatCompletionClient:
            self._client_registry[ModelProvider.AZURE_AI] = AzureAIChatCompletionClient
        
        if SKChatCompletionAdapter:
            self._client_registry[ModelProvider.SEMANTIC_KERNEL] = SKChatCompletionAdapter
    
    def register_client(self, provider: ModelProvider, client_class: Type[ChatCompletionClient]) -> None:
        """
        Register a custom model client for a provider.

        Args:
            provider: The model provider
            client_class: The client class to register
        """
        self._client_registry[provider] = client_class
        self.logger.info(f"Registered custom client for provider: {provider}")

    def _validate_config(self, config: ModelConfig) -> None:
        """
        Validate model configuration.

        Args:
            config: Model configuration to validate

        Raises:
            ConfigurationError: If configuration is invalid
        """
        if not config.model:
            raise ConfigurationError("Model name is required", config_field="model")

        # Provider-specific validation
        if config.provider == ModelProvider.AZURE_OPENAI:
            if not config.azure_endpoint and not config.base_url:
                raise ConfigurationError(
                    "Azure endpoint is required for Azure OpenAI",
                    config_field="azure_endpoint"
                )

        if config.provider in [ModelProvider.OPENAI, ModelProvider.ANTHROPIC, ModelProvider.OPENROUTER]:
            if not config.api_key:
                raise ConfigurationError(
                    f"API key is required for {config.provider.value}",
                    config_field="api_key"
                )
    
    def create_client(self, config: ModelConfig) -> ChatCompletionClient:
        """
        Create a model client based on configuration.

        Args:
            config: Model configuration

        Returns:
            Configured model client

        Raises:
            ModelClientError: If client creation fails
            ConfigurationError: If configuration is invalid
        """
        # Validate configuration
        self._validate_config(config)

        provider = config.provider

        if provider not in self._client_registry:
            available_providers = list(self._client_registry.keys())
            error_context = ErrorContext(
                timestamp=datetime.now(),
                model_config=config,
                operation="create_client"
            )
            raise error_handler.handle_error(
                ConfigurationError(
                    f"Unsupported model provider: {provider}. "
                    f"Available providers: {available_providers}",
                    config_field="provider"
                ),
                error_context
            )

        client_class = self._client_registry[provider]

        try:
            if provider == ModelProvider.OPENAI:
                return self._create_openai_client(config, client_class)
            elif provider == ModelProvider.AZURE_OPENAI:
                return self._create_azure_openai_client(config, client_class)
            elif provider == ModelProvider.ANTHROPIC:
                return self._create_anthropic_client(config, client_class)
            elif provider == ModelProvider.OLLAMA or provider == ModelProvider.LOCAL:
                return self._create_ollama_client(config, client_class)
            elif provider == ModelProvider.OPENROUTER:
                return self._create_openrouter_client(config, client_class)
            elif provider == ModelProvider.AZURE_AI:
                return self._create_azure_ai_client(config, client_class)
            elif provider == ModelProvider.SEMANTIC_KERNEL:
                return self._create_semantic_kernel_client(config, client_class)
            else:
                # Generic client creation
                return self._create_generic_client(config, client_class)

        except Exception as e:
            error_context = ErrorContext(
                timestamp=datetime.now(),
                model_config=config,
                operation="create_client"
            )

            # Handle the error through the error handler
            framework_error = error_handler.handle_error(e, error_context)

            # Try fallback configurations if available
            if config.fallback_configs:
                for fallback_config in config.fallback_configs:
                    try:
                        self.logger.info(f"Trying fallback configuration: {fallback_config.provider}")
                        return self.create_client(fallback_config)
                    except Exception as fallback_error:
                        self.logger.warning(f"Fallback failed: {fallback_error}")
                        continue

            # If all fallbacks failed, raise the original error
            raise framework_error
    
    def _create_openai_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create OpenAI client."""
        kwargs = {
            "model": config.model,
            "api_key": config.api_key,
            "base_url": config.base_url,
            "timeout": config.timeout_seconds,
        }
        
        # Add optional parameters
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.top_p is not None:
            kwargs["top_p"] = config.top_p
        if config.frequency_penalty is not None:
            kwargs["frequency_penalty"] = config.frequency_penalty
        if config.presence_penalty is not None:
            kwargs["presence_penalty"] = config.presence_penalty
        if config.response_format:
            kwargs["response_format"] = config.response_format
        if config.extra_headers:
            kwargs["extra_headers"] = config.extra_headers
        if config.extra_body:
            kwargs["extra_body"] = config.extra_body
        
        # Add model info
        if config.model_info:
            kwargs["model_info"] = config.model_info
        
        return client_class(**kwargs)
    
    def _create_azure_openai_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create Azure OpenAI client."""
        kwargs = {
            "model": config.azure_deployment or config.model,
            "api_key": config.api_key,
            "azure_endpoint": config.azure_endpoint or config.base_url,
            "api_version": config.azure_api_version or "2024-02-01",
            "timeout": config.timeout_seconds,
        }
        
        # Add optional parameters
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.model_info:
            kwargs["model_info"] = config.model_info
        
        return client_class(**kwargs)
    
    def _create_anthropic_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create Anthropic client."""
        kwargs = {
            "model": config.model,
            "api_key": config.api_key,
            "timeout": config.timeout_seconds,
        }
        
        if config.base_url:
            kwargs["base_url"] = config.base_url
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.anthropic_version:
            kwargs["anthropic_version"] = config.anthropic_version
        
        return client_class(**kwargs)
    
    def _create_ollama_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create Ollama client."""
        kwargs = {
            "model": config.model,
            "base_url": config.base_url or "http://localhost:11434",
            "timeout": config.timeout_seconds,
        }
        
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        
        return client_class(**kwargs)
    
    def _create_openrouter_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create OpenRouter client (uses OpenAI-compatible interface)."""
        return self._create_openai_client(config, client_class)
    
    def _create_azure_ai_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create Azure AI client."""
        kwargs = {
            "model": config.azure_ai_model_name or config.model,
            "api_key": config.api_key,
            "endpoint": config.azure_ai_endpoint or config.base_url,
            "timeout": config.timeout_seconds,
        }
        
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        
        return client_class(**kwargs)
    
    def _create_semantic_kernel_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create Semantic Kernel adapter client."""
        # This would require Semantic Kernel service configuration
        # Implementation depends on specific SK setup
        kwargs = {
            "service_id": config.sk_service_id or "default",
        }
        
        return client_class(**kwargs)
    
    def _create_generic_client(self, config: ModelConfig, client_class: Type) -> ChatCompletionClient:
        """Create a generic client with basic parameters."""
        kwargs = {
            "model": config.model,
            "api_key": config.api_key,
            "base_url": config.base_url,
            "timeout": config.timeout_seconds,
        }
        
        if config.temperature is not None:
            kwargs["temperature"] = config.temperature
        if config.max_tokens:
            kwargs["max_tokens"] = config.max_tokens
        
        return client_class(**kwargs)
    
    def get_supported_providers(self) -> list[ModelProvider]:
        """Get list of supported model providers."""
        return list(self._client_registry.keys())
    
    def is_provider_supported(self, provider: ModelProvider) -> bool:
        """Check if a provider is supported."""
        return provider in self._client_registry


# Global factory instance
model_client_factory = ModelClientFactory()
