"""
Agent registry for managing agent metadata and discovery.
"""

from typing import Any, Dict, List, Optional, Set, Callable
from uuid import UUID
from datetime import datetime

from .multi_agent_types import AgentInfo, AgentStatus, AgentCapability, AgentInterface
from .types import Task

class AgentRegistry:
    """
    Registry for managing agent metadata and discovery.
    
    Provides centralized storage and querying of agent information,
    capabilities, and status for efficient agent selection and coordination.
    """
    
    def __init__(self) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the agent registry."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the agent registry."""
        ...
    
    async def register_agent(self, agent: AgentInterface) -> AgentInfo:
        """Register an agent and return its info."""
        ...
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent."""
        ...
    
    async def update_agent_status(self, agent_id: str, status: AgentStatus) -> bool:
        """Update an agent's status."""
        ...
    
    async def update_agent_metrics(self, agent_id: str, metrics: Dict[str, Any]) -> bool:
        """Update an agent's metrics."""
        ...
    
    async def get_agent_info(self, agent_id: str) -> Optional[AgentInfo]:
        """Get information about an agent."""
        ...
    
    async def list_all_agents(self) -> List[AgentInfo]:
        """List all registered agents."""
        ...
    
    async def list_active_agents(self) -> List[AgentInfo]:
        """List all active agents."""
        ...
    
    async def find_agents_by_capability(self, capability: AgentCapability) -> List[AgentInfo]:
        """Find agents that have a specific capability."""
        ...
    
    async def find_agents_by_type(self, agent_type: str) -> List[AgentInfo]:
        """Find agents of a specific type."""
        ...
    
    async def find_available_agents(self, required_capabilities: List[AgentCapability]) -> List[AgentInfo]:
        """Find agents that are available and have required capabilities."""
        ...
    
    async def find_best_agent_for_task(self, task: Task) -> Optional[AgentInfo]:
        """Find the best agent to handle a specific task."""
        ...
    
    async def get_agent_load_info(self, agent_id: str) -> Dict[str, Any]:
        """Get load information for an agent."""
        ...
    
    async def get_least_loaded_agent(self, capability: AgentCapability) -> Optional[AgentInfo]:
        """Get the least loaded agent with a specific capability."""
        ...
    
    async def get_agent_statistics(self) -> Dict[str, Any]:
        """Get statistics about all agents."""
        ...
    
    async def search_agents(self, 
                           status: Optional[AgentStatus] = None,
                           capabilities: Optional[List[AgentCapability]] = None,
                           agent_type: Optional[str] = None,
                           max_load: Optional[float] = None) -> List[AgentInfo]:
        """Search for agents based on criteria."""
        ...
    
    def add_agent_change_listener(self, listener: Callable[[str, AgentInfo], None]) -> None:
        """Add a listener for agent changes."""
        ...
    
    def remove_agent_change_listener(self, listener: Callable[[str, AgentInfo], None]) -> None:
        """Remove an agent change listener."""
        ...
    
    # Properties
    @property
    def agent_count(self) -> int: ...
    
    @property
    def active_agent_count(self) -> int: ...
    
    @property
    def available_capabilities(self) -> Set[AgentCapability]: ...
    
    @property
    def agent_types(self) -> Set[str]: ...
