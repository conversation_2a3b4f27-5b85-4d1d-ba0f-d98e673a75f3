"""
Central orchestration system for the agent framework.
"""

from typing import Any, Dict, List, Optional, Union, Callable, Awaitable
from uuid import UUID
import asyncio

from .config import FrameworkConfig
from .types import Task, TaskResult, TaskStatus, AgentInterface, PluginInterface
from .agent_manager import Agent<PERSON>anager
from .agent_registry import Agent<PERSON>egistry
from ..plugins.manager import Plugin<PERSON>anager
from ..context.manager import ContextManager
from ..execution.executor import TaskExecutor
from ..communication.broker import MessageBroker
from ..mcp.connection_manager import MCPConnectionManager
from ..coordination.coordinator import AgentCoordinator
from ..monitoring.multi_agent_logger import MultiAgentLogger

class AgentOrchestrator:
    """
    Central orchestration system for the agent framework.

    Manages agent lifecycle, task execution, plugin coordination,
    and communication between all framework components.
    """
    
    def __init__(self, config: FrameworkConfig) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the orchestrator and all components."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the orchestrator and cleanup resources."""
        ...
    
    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task using the appropriate agent or plugin."""
        ...
    
    async def execute_task_async(self, task: Task) -> UUID:
        """Execute a task asynchronously and return task ID."""
        ...
    
    async def get_task_result(self, task_id: UUID) -> Optional[TaskResult]:
        """Get the result of an asynchronously executed task."""
        ...
    
    async def cancel_task(self, task_id: UUID) -> bool:
        """Cancel a running task."""
        ...
    
    async def get_task_status(self, task_id: UUID) -> Optional[TaskStatus]:
        """Get the status of a task."""
        ...
    
    async def list_active_tasks(self) -> List[Task]:
        """List all currently active tasks."""
        ...
    
    async def register_agent(self, agent: AgentInterface) -> None:
        """Register a new agent with the orchestrator."""
        ...
    
    async def unregister_agent(self, agent_id: str) -> None:
        """Unregister an agent from the orchestrator."""
        ...
    
    async def list_agents(self) -> List[Dict[str, Any]]:
        """List all registered agents."""
        ...
    
    async def load_plugin(self, plugin_name: str) -> bool:
        """Load a plugin by name."""
        ...
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """Unload a plugin by name."""
        ...
    
    async def list_plugins(self) -> List[Dict[str, Any]]:
        """List all loaded plugins."""
        ...
    
    async def get_capabilities(self) -> List[str]:
        """Get all available capabilities from agents and plugins."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check of all components."""
        ...
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get performance and usage metrics."""
        ...
    
    def add_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]) -> None:
        """Add an event handler for specific event types."""
        ...
    
    def remove_event_handler(self, event_type: str, handler: Callable[[Dict[str, Any]], Awaitable[None]]) -> None:
        """Remove an event handler."""
        ...
    
    # Properties
    @property
    def config(self) -> FrameworkConfig: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def agent_manager(self) -> Optional[AgentManager]: ...
    
    @property
    def plugin_manager(self) -> Optional[PluginManager]: ...
    
    @property
    def task_executor(self) -> Optional[TaskExecutor]: ...
    
    @property
    def message_broker(self) -> Optional[MessageBroker]: ...
    
    @property
    def context_manager(self) -> Optional[ContextManager]: ...
    
    @property
    def mcp_manager(self) -> Optional[MCPConnectionManager]: ...
