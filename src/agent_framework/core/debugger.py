"""
Enhanced debugging system with root cause analysis and intelligent error resolution.
"""

import ast
import logging
import traceback
import re
import sys
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import subprocess
import tempfile

from .code_analysis import CodeAnalyzer
from ..core.types import Task, TaskResult, TaskStatus


@dataclass
class DebugContext:
    """Context information for debugging."""
    error_type: str
    error_message: str
    traceback_info: str
    file_path: str
    line_number: int
    code_context: List[str]
    variables: Dict[str, Any]
    call_stack: List[Dict[str, Any]]


@dataclass
class RootCauseAnalysis:
    """Root cause analysis results."""
    primary_cause: str
    contributing_factors: List[str]
    confidence_score: float
    evidence: List[str]
    similar_patterns: List[Dict[str, Any]]


@dataclass
class DebugSuggestion:
    """Debugging suggestion."""
    suggestion_type: str  # 'fix', 'investigation', 'prevention'
    description: str
    code_example: Optional[str]
    confidence: float
    priority: int  # 1 = highest priority


class Debugger:
    """Advanced debugger with intelligent error analysis and resolution."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.analyzer = CodeAnalyzer()
        self._error_patterns = self._load_error_patterns()
        self._debug_history: List[DebugContext] = []
        
    async def analyze_error(self, 
                           error: Exception,
                           code_content: str,
                           file_path: str = "",
                           context_vars: Optional[Dict[str, Any]] = None) -> DebugContext:
        """
        Analyze an error and create comprehensive debug context.
        
        Args:
            error: The exception that occurred
            code_content: The code where the error occurred
            file_path: Path to the file
            context_vars: Variables in the error context
            
        Returns:
            Debug context with comprehensive information
        """
        try:
            # Extract error information
            error_type = type(error).__name__
            error_message = str(error)
            traceback_info = traceback.format_exc()
            
            # Extract line number and context
            line_number = self._extract_line_number(traceback_info)
            code_context = self._extract_code_context(code_content, line_number)
            
            # Build call stack
            call_stack = self._build_call_stack(traceback_info)
            
            debug_context = DebugContext(
                error_type=error_type,
                error_message=error_message,
                traceback_info=traceback_info,
                file_path=file_path,
                line_number=line_number,
                code_context=code_context,
                variables=context_vars or {},
                call_stack=call_stack
            )
            
            # Add to history
            self._debug_history.append(debug_context)
            
            return debug_context
            
        except Exception as e:
            self.logger.error(f"Error analysis failed: {e}")
            return DebugContext(
                error_type="AnalysisError",
                error_message=f"Failed to analyze error: {str(e)}",
                traceback_info="",
                file_path=file_path,
                line_number=0,
                code_context=[],
                variables={},
                call_stack=[]
            )
    
    async def perform_root_cause_analysis(self, debug_context: DebugContext) -> RootCauseAnalysis:
        """
        Perform root cause analysis on a debug context.
        
        Args:
            debug_context: The debug context to analyze
            
        Returns:
            Root cause analysis results
        """
        try:
            # Identify primary cause
            primary_cause = self._identify_primary_cause(debug_context)
            
            # Find contributing factors
            contributing_factors = self._find_contributing_factors(debug_context)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(debug_context, primary_cause)
            
            # Gather evidence
            evidence = self._gather_evidence(debug_context, primary_cause)
            
            # Find similar patterns
            similar_patterns = self._find_similar_patterns(debug_context)
            
            return RootCauseAnalysis(
                primary_cause=primary_cause,
                contributing_factors=contributing_factors,
                confidence_score=confidence_score,
                evidence=evidence,
                similar_patterns=similar_patterns
            )
            
        except Exception as e:
            self.logger.error(f"Root cause analysis failed: {e}")
            return RootCauseAnalysis(
                primary_cause="Unknown",
                contributing_factors=[],
                confidence_score=0.0,
                evidence=[],
                similar_patterns=[]
            )
    
    async def generate_debug_suggestions(self, 
                                       debug_context: DebugContext,
                                       root_cause: RootCauseAnalysis) -> List[DebugSuggestion]:
        """
        Generate intelligent debugging suggestions.
        
        Args:
            debug_context: The debug context
            root_cause: Root cause analysis results
            
        Returns:
            List of debugging suggestions
        """
        suggestions = []
        
        try:
            # Generate fix suggestions
            fix_suggestions = self._generate_fix_suggestions(debug_context, root_cause)
            suggestions.extend(fix_suggestions)
            
            # Generate investigation suggestions
            investigation_suggestions = self._generate_investigation_suggestions(debug_context)
            suggestions.extend(investigation_suggestions)
            
            # Generate prevention suggestions
            prevention_suggestions = self._generate_prevention_suggestions(debug_context, root_cause)
            suggestions.extend(prevention_suggestions)
            
            # Sort by priority and confidence
            suggestions.sort(key=lambda x: (x.priority, -x.confidence))
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Suggestion generation failed: {e}")
            return [DebugSuggestion(
                suggestion_type="investigation",
                description=f"Manual investigation required due to analysis error: {str(e)}",
                code_example=None,
                confidence=0.1,
                priority=10
            )]
    
    def _load_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load common error patterns and their solutions."""
        return {
            "NameError": {
                "common_causes": ["undefined_variable", "typo", "scope_issue"],
                "solutions": [
                    "Check variable spelling",
                    "Ensure variable is defined before use",
                    "Check variable scope"
                ]
            },
            "AttributeError": {
                "common_causes": ["nonexistent_attribute", "none_object", "wrong_type"],
                "solutions": [
                    "Check if attribute exists",
                    "Verify object is not None",
                    "Check object type"
                ]
            },
            "TypeError": {
                "common_causes": ["wrong_argument_type", "unsupported_operation", "missing_arguments"],
                "solutions": [
                    "Check argument types",
                    "Verify operation is supported",
                    "Check function signature"
                ]
            },
            "IndexError": {
                "common_causes": ["list_index_out_of_range", "empty_list"],
                "solutions": [
                    "Check list length before accessing",
                    "Use try-except for safe access",
                    "Verify list is not empty"
                ]
            },
            "KeyError": {
                "common_causes": ["missing_dictionary_key", "typo_in_key"],
                "solutions": [
                    "Check if key exists in dictionary",
                    "Use dict.get() for safe access",
                    "Verify key spelling"
                ]
            },
            "ImportError": {
                "common_causes": ["missing_module", "circular_import", "path_issue"],
                "solutions": [
                    "Install missing module",
                    "Check for circular imports",
                    "Verify Python path"
                ]
            },
            "SyntaxError": {
                "common_causes": ["missing_parentheses", "indentation_error", "invalid_syntax"],
                "solutions": [
                    "Check parentheses matching",
                    "Fix indentation",
                    "Review syntax rules"
                ]
            }
        }
    
    def _extract_line_number(self, traceback_info: str) -> int:
        """Extract line number from traceback."""
        try:
            # Look for line number in traceback
            match = re.search(r'line (\d+)', traceback_info)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def _extract_code_context(self, code_content: str, line_number: int, context_lines: int = 5) -> List[str]:
        """Extract code context around the error line."""
        lines = code_content.split('\n')
        start = max(0, line_number - context_lines - 1)
        end = min(len(lines), line_number + context_lines)
        
        context = []
        for i in range(start, end):
            marker = ">>>" if i == line_number - 1 else "   "
            context.append(f"{marker} {i+1:3d}: {lines[i]}")
        
        return context
    
    def _build_call_stack(self, traceback_info: str) -> List[Dict[str, Any]]:
        """Build call stack from traceback."""
        call_stack = []
        
        try:
            # Parse traceback for call stack information
            lines = traceback_info.split('\n')
            current_frame = {}
            
            for line in lines:
                if 'File "' in line and ', line ' in line:
                    if current_frame:
                        call_stack.append(current_frame)
                    
                    # Extract file and line info
                    match = re.search(r'File "([^"]+)", line (\d+), in (.+)', line)
                    if match:
                        current_frame = {
                            "file": match.group(1),
                            "line": int(match.group(2)),
                            "function": match.group(3),
                            "code": ""
                        }
                elif current_frame and line.strip() and not line.startswith('  '):
                    current_frame["code"] = line.strip()
            
            if current_frame:
                call_stack.append(current_frame)
                
        except Exception as e:
            self.logger.warning(f"Failed to build call stack: {e}")
        
        return call_stack
    
    def _identify_primary_cause(self, debug_context: DebugContext) -> str:
        """Identify the primary cause of the error."""
        error_type = debug_context.error_type
        error_message = debug_context.error_message.lower()
        
        if error_type in self._error_patterns:
            patterns = self._error_patterns[error_type]
            
            # Check for specific patterns in error message
            if "not defined" in error_message:
                return "undefined_variable"
            elif "has no attribute" in error_message:
                return "nonexistent_attribute"
            elif "object is not" in error_message:
                return "wrong_type"
            elif "index out of range" in error_message:
                return "list_index_out_of_range"
            elif "key" in error_message and error_type == "KeyError":
                return "missing_dictionary_key"
            elif "no module named" in error_message:
                return "missing_module"
            
            # Default to first common cause
            return patterns["common_causes"][0] if patterns["common_causes"] else "unknown"
        
        return "unknown"
    
    def _find_contributing_factors(self, debug_context: DebugContext) -> List[str]:
        """Find contributing factors to the error."""
        factors = []
        
        # Check code context for patterns
        code_lines = [line.split(': ', 1)[1] if ': ' in line else line 
                     for line in debug_context.code_context]
        code_text = '\n'.join(code_lines)
        
        # Look for common contributing factors
        if len(code_lines) > 20:
            factors.append("long_function")
        
        if any("TODO" in line or "FIXME" in line for line in code_lines):
            factors.append("incomplete_implementation")
        
        if debug_context.variables:
            none_vars = [k for k, v in debug_context.variables.items() if v is None]
            if none_vars:
                factors.append("none_values")
        
        # Check for complex expressions
        if any(len(line) > 100 for line in code_lines):
            factors.append("complex_expressions")
        
        return factors
    
    def _calculate_confidence_score(self, debug_context: DebugContext, primary_cause: str) -> float:
        """Calculate confidence score for the root cause analysis."""
        score = 0.5  # Base confidence
        
        # Increase confidence for known error patterns
        if debug_context.error_type in self._error_patterns:
            score += 0.3
        
        # Increase confidence if we have good context
        if debug_context.code_context:
            score += 0.1
        
        if debug_context.variables:
            score += 0.1
        
        # Decrease confidence for unknown causes
        if primary_cause == "unknown":
            score -= 0.3
        
        return min(1.0, max(0.0, score))
    
    def _gather_evidence(self, debug_context: DebugContext, primary_cause: str) -> List[str]:
        """Gather evidence supporting the root cause."""
        evidence = []
        
        # Add error message as evidence
        evidence.append(f"Error message: {debug_context.error_message}")
        
        # Add relevant code context
        if debug_context.code_context:
            error_line = next((line for line in debug_context.code_context if ">>>" in line), None)
            if error_line:
                evidence.append(f"Error occurred at: {error_line.strip()}")
        
        # Add variable evidence
        if debug_context.variables:
            for var, value in debug_context.variables.items():
                if value is None:
                    evidence.append(f"Variable '{var}' is None")
                elif isinstance(value, (list, dict)) and len(value) == 0:
                    evidence.append(f"Variable '{var}' is empty")
        
        return evidence
    
    def _find_similar_patterns(self, debug_context: DebugContext) -> List[Dict[str, Any]]:
        """Find similar error patterns in history."""
        similar = []
        
        for historical_context in self._debug_history[-10:]:  # Check last 10 errors
            if historical_context.error_type == debug_context.error_type:
                similarity_score = self._calculate_similarity(debug_context, historical_context)
                if similarity_score > 0.5:
                    similar.append({
                        "context": historical_context,
                        "similarity": similarity_score
                    })
        
        return sorted(similar, key=lambda x: x["similarity"], reverse=True)
    
    def _calculate_similarity(self, context1: DebugContext, context2: DebugContext) -> float:
        """Calculate similarity between two debug contexts."""
        score = 0.0
        
        # Same error type
        if context1.error_type == context2.error_type:
            score += 0.4
        
        # Similar error messages
        if context1.error_message in context2.error_message or context2.error_message in context1.error_message:
            score += 0.3
        
        # Same file
        if context1.file_path == context2.file_path:
            score += 0.2
        
        # Similar line numbers
        if abs(context1.line_number - context2.line_number) < 10:
            score += 0.1
        
        return score
    
    def _generate_fix_suggestions(self, 
                                debug_context: DebugContext, 
                                root_cause: RootCauseAnalysis) -> List[DebugSuggestion]:
        """Generate fix suggestions."""
        suggestions = []
        error_type = debug_context.error_type
        
        if error_type in self._error_patterns:
            solutions = self._error_patterns[error_type]["solutions"]
            
            for i, solution in enumerate(solutions):
                suggestions.append(DebugSuggestion(
                    suggestion_type="fix",
                    description=solution,
                    code_example=self._generate_code_example(error_type, solution),
                    confidence=0.8 - (i * 0.1),
                    priority=i + 1
                ))
        
        return suggestions
    
    def _generate_investigation_suggestions(self, debug_context: DebugContext) -> List[DebugSuggestion]:
        """Generate investigation suggestions."""
        suggestions = []
        
        # Suggest checking variables
        if debug_context.variables:
            suggestions.append(DebugSuggestion(
                suggestion_type="investigation",
                description="Check the values of variables at the error point",
                code_example="print(f'Variable values: {locals()}')",
                confidence=0.7,
                priority=2
            ))
        
        # Suggest adding logging
        suggestions.append(DebugSuggestion(
            suggestion_type="investigation",
            description="Add logging to trace execution flow",
            code_example="import logging\nlogging.debug('Reached this point')",
            confidence=0.6,
            priority=3
        ))
        
        return suggestions
    
    def _generate_prevention_suggestions(self, 
                                       debug_context: DebugContext,
                                       root_cause: RootCauseAnalysis) -> List[DebugSuggestion]:
        """Generate prevention suggestions."""
        suggestions = []
        
        # Suggest adding error handling
        suggestions.append(DebugSuggestion(
            suggestion_type="prevention",
            description="Add try-except blocks for error handling",
            code_example="try:\n    # risky code\n    pass\nexcept Exception as e:\n    # handle error\n    pass",
            confidence=0.8,
            priority=1
        ))
        
        # Suggest input validation
        if debug_context.error_type in ["TypeError", "ValueError"]:
            suggestions.append(DebugSuggestion(
                suggestion_type="prevention",
                description="Add input validation",
                code_example="if not isinstance(value, expected_type):\n    raise TypeError('Invalid type')",
                confidence=0.7,
                priority=2
            ))
        
        return suggestions
    
    def _generate_code_example(self, error_type: str, solution: str) -> Optional[str]:
        """Generate code example for a solution."""
        examples = {
            "NameError": {
                "Check variable spelling": "# Make sure variable is spelled correctly\nvariable_name = 'value'  # Define before use",
                "Ensure variable is defined before use": "# Define variable before using it\nif 'variable' in locals():\n    use_variable()",
                "Check variable scope": "# Make sure variable is in correct scope\ndef function():\n    global variable_name\n    variable_name = 'value'"
            },
            "AttributeError": {
                "Check if attribute exists": "if hasattr(obj, 'attribute'):\n    obj.attribute",
                "Verify object is not None": "if obj is not None:\n    obj.method()",
                "Check object type": "if isinstance(obj, ExpectedType):\n    obj.method()"
            }
        }
        
        return examples.get(error_type, {}).get(solution)
    
    def get_debug_history(self) -> List[DebugContext]:
        """Get debugging history."""
        return self._debug_history.copy()
