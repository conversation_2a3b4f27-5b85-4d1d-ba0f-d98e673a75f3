"""
Core types and data structures for the agent framework.
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable, Awaitable
from uuid import UUID

from pydantic import BaseModel

class TaskStatus(Enum):
    """Status of a task in the execution pipeline."""
    PENDING: str
    RUNNING: str
    COMPLETED: str
    FAILED: str
    CANCELLED: str

class TaskPriority(Enum):
    """Priority levels for task execution."""
    LOW: int
    NORMAL: int
    HIGH: int
    CRITICAL: int

@dataclass
class Task:
    """Represents a task to be executed by the agent framework."""
    id: UUID
    name: str
    description: str
    task_type: str
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    parameters: Dict[str, Any]
    context: Dict[str, Any]
    dependencies: List[UUID]
    timeout_seconds: Optional[int]
    retry_count: int
    max_retries: int
    
    def __lt__(self, other: Task) -> bool: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...

@dataclass
class TaskResult:
    """Result of task execution."""
    task_id: UUID
    status: TaskStatus
    result: Optional[Any]
    error: Optional[str]
    execution_time: Optional[float]
    metadata: Dict[str, Any]
    created_at: datetime

class AgentEvent(BaseModel):
    """Base class for agent events."""
    event_type: str
    agent_id: str
    timestamp: datetime
    data: Dict[str, Any]

class TaskEvent(AgentEvent):
    """Event related to task execution."""
    task_id: UUID
    task_name: str

class AgentInterface(ABC):
    """Abstract interface for agents."""
    
    @abstractmethod
    async def process_task(self, task: Task) -> TaskResult: ...
    
    @abstractmethod
    async def get_capabilities(self) -> List[str]: ...
    
    @abstractmethod
    async def initialize(self) -> None: ...
    
    @abstractmethod
    async def shutdown(self) -> None: ...

class PluginInterface(ABC):
    """Abstract interface for plugins."""
    
    @property
    @abstractmethod
    def name(self) -> str: ...
    
    @property
    @abstractmethod
    def version(self) -> str: ...
    
    @abstractmethod
    async def initialize(self) -> None: ...
    
    @abstractmethod
    async def shutdown(self) -> None: ...
    
    @abstractmethod
    async def get_capabilities(self) -> List[str]: ...

TaskHandler = Callable[[Task], Awaitable[TaskResult]]
EventHandler = Callable[[AgentEvent], Awaitable[None]]
