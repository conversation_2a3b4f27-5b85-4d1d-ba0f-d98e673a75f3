"""
Dynamic configuration management with hot-reload capabilities.

Provides configuration loading, validation, hot-reload, and change notifications
for the agent framework.
"""

import os
import json
import yaml
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import hashlib

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False

try:
    from pydantic import BaseModel, ValidationError, validator
    from pydantic_settings import BaseSettings
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False


@dataclass
class ConfigChangeEvent:
    """Configuration change event."""
    config_path: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.now)
    change_type: str = "update"  # update, add, delete


class ConfigFile<PERSON>andler(FileSystemEventHandler):
    """File system event handler for configuration files."""
    
    def __init__(self, config_manager: 'ConfigManager'):
        """Initialize the file handler."""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
    
    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        # Check if it's a config file we're watching
        if file_path in self.config_manager._watched_files:
            self.logger.info(f"Configuration file modified: {file_path}")
            asyncio.create_task(self.config_manager._reload_config_file(file_path))


class ConfigManager:
    """
    Dynamic configuration manager with hot-reload capabilities.
    
    Features:
    - Multiple configuration file formats (JSON, YAML)
    - Configuration validation with Pydantic
    - Hot-reload with file system watching
    - Change notifications and callbacks
    - Configuration merging and inheritance
    - Environment variable substitution
    - Configuration history and rollback
    """
    
    def __init__(self, 
                 config_dirs: List[Union[str, Path]] = None,
                 auto_reload: bool = True,
                 validation_schema: Optional[BaseModel] = None):
        """Initialize the configuration manager."""
        self.logger = logging.getLogger(__name__)
        self.config_dirs = [Path(d) for d in (config_dirs or ["config"])]
        self.auto_reload = auto_reload
        self.validation_schema = validation_schema
        
        # Configuration state
        self._config: Dict[str, Any] = {}
        self._config_history: List[Dict[str, Any]] = []
        self._file_checksums: Dict[Path, str] = {}
        self._watched_files: Set[Path] = set()
        
        # Change tracking
        self._change_callbacks: List[Callable[[ConfigChangeEvent], None]] = []
        
        # File watching
        self._observer: Optional[Observer] = None
        self._file_handler: Optional[ConfigFileHandler] = None
        
        # Reload state
        self._reload_lock = asyncio.Lock()
        self._last_reload = datetime.now()
        
        # Initialize
        self._initialize()
    
    def _initialize(self) -> None:
        """Initialize the configuration manager."""
        # Load initial configuration
        self._load_all_configs()
        
        # Start file watching if enabled
        if self.auto_reload and WATCHDOG_AVAILABLE:
            self._start_file_watching()
        elif self.auto_reload:
            self.logger.warning("Watchdog not available - auto-reload disabled")
    
    def _load_all_configs(self) -> None:
        """Load all configuration files."""
        config_files = []
        
        # Find all config files
        for config_dir in self.config_dirs:
            if config_dir.exists():
                for pattern in ["*.json", "*.yaml", "*.yml"]:
                    config_files.extend(config_dir.glob(pattern))
        
        # Sort files for consistent loading order
        config_files.sort()
        
        # Load each file
        for config_file in config_files:
            try:
                self._load_config_file(config_file)
                self._watched_files.add(config_file)
            except Exception as e:
                self.logger.error(f"Failed to load config file {config_file}: {e}")
        
        # Validate configuration
        self._validate_config()
        
        # Store in history
        self._store_config_snapshot()
        
        self.logger.info(f"Loaded configuration from {len(config_files)} files")
    
    def _load_config_file(self, config_file: Path) -> None:
        """Load a single configuration file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Calculate checksum
            checksum = hashlib.md5(content.encode()).hexdigest()
            self._file_checksums[config_file] = checksum
            
            # Parse based on file extension
            if config_file.suffix.lower() == '.json':
                file_config = json.loads(content)
            elif config_file.suffix.lower() in ['.yaml', '.yml']:
                file_config = yaml.safe_load(content)
            else:
                self.logger.warning(f"Unknown config file format: {config_file}")
                return
            
            # Perform environment variable substitution
            file_config = self._substitute_env_vars(file_config)
            
            # Merge with existing configuration
            self._merge_config(file_config, str(config_file))
            
        except Exception as e:
            self.logger.error(f"Failed to load config file {config_file}: {e}")
            raise
    
    def _substitute_env_vars(self, config: Any) -> Any:
        """Substitute environment variables in configuration."""
        if isinstance(config, dict):
            return {key: self._substitute_env_vars(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._substitute_env_vars(item) for item in config]
        elif isinstance(config, str):
            # Simple environment variable substitution
            if config.startswith("${") and config.endswith("}"):
                env_var = config[2:-1]
                default_value = None
                
                # Handle default values: ${VAR:default}
                if ":" in env_var:
                    env_var, default_value = env_var.split(":", 1)
                
                return os.getenv(env_var, default_value)
            return config
        else:
            return config
    
    def _merge_config(self, new_config: Dict[str, Any], source: str) -> None:
        """Merge new configuration with existing configuration."""
        def deep_merge(base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
            """Deep merge two dictionaries."""
            result = base.copy()
            for key, value in update.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        self._config = deep_merge(self._config, new_config)
    
    def _validate_config(self) -> None:
        """Validate configuration against schema."""
        if not self.validation_schema or not PYDANTIC_AVAILABLE:
            return
        
        try:
            # Validate using Pydantic schema
            self.validation_schema(**self._config)
            self.logger.debug("Configuration validation passed")
        except ValidationError as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise
    
    def _start_file_watching(self) -> None:
        """Start file system watching for configuration changes."""
        if not WATCHDOG_AVAILABLE:
            return
        
        self._file_handler = ConfigFileHandler(self)
        self._observer = Observer()
        
        # Watch all config directories
        for config_dir in self.config_dirs:
            if config_dir.exists():
                self._observer.schedule(
                    self._file_handler,
                    str(config_dir),
                    recursive=False
                )
        
        self._observer.start()
        self.logger.info("Configuration file watching started")
    
    def _stop_file_watching(self) -> None:
        """Stop file system watching."""
        if self._observer:
            self._observer.stop()
            self._observer.join()
            self._observer = None
            self.logger.info("Configuration file watching stopped")
    
    async def _reload_config_file(self, config_file: Path) -> None:
        """Reload a specific configuration file."""
        async with self._reload_lock:
            try:
                # Check if file actually changed
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                new_checksum = hashlib.md5(content.encode()).hexdigest()
                old_checksum = self._file_checksums.get(config_file)
                
                if new_checksum == old_checksum:
                    return  # No actual change
                
                # Store old configuration for comparison
                old_config = self._config.copy()
                
                # Reload all configurations to maintain proper merging order
                self._config = {}
                self._load_all_configs()
                
                # Detect changes
                changes = self._detect_changes(old_config, self._config)
                
                # Notify change callbacks
                for change in changes:
                    for callback in self._change_callbacks:
                        try:
                            callback(change)
                        except Exception as e:
                            self.logger.error(f"Configuration change callback failed: {e}")
                
                self._last_reload = datetime.now()
                self.logger.info(f"Configuration reloaded from {config_file}")
                
            except Exception as e:
                self.logger.error(f"Failed to reload configuration from {config_file}: {e}")
    
    def _detect_changes(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> List[ConfigChangeEvent]:
        """Detect changes between old and new configuration."""
        changes = []
        
        def compare_dicts(old_dict: Dict[str, Any], new_dict: Dict[str, Any], path: str = ""):
            # Check for updates and additions
            for key, new_value in new_dict.items():
                current_path = f"{path}.{key}" if path else key
                
                if key not in old_dict:
                    # New key added
                    changes.append(ConfigChangeEvent(
                        config_path=current_path,
                        old_value=None,
                        new_value=new_value,
                        change_type="add"
                    ))
                elif old_dict[key] != new_value:
                    if isinstance(old_dict[key], dict) and isinstance(new_value, dict):
                        # Recursively compare nested dictionaries
                        compare_dicts(old_dict[key], new_value, current_path)
                    else:
                        # Value changed
                        changes.append(ConfigChangeEvent(
                            config_path=current_path,
                            old_value=old_dict[key],
                            new_value=new_value,
                            change_type="update"
                        ))
            
            # Check for deletions
            for key, old_value in old_dict.items():
                if key not in new_dict:
                    current_path = f"{path}.{key}" if path else key
                    changes.append(ConfigChangeEvent(
                        config_path=current_path,
                        old_value=old_value,
                        new_value=None,
                        change_type="delete"
                    ))
        
        compare_dicts(old_config, new_config)
        return changes
    
    def _store_config_snapshot(self) -> None:
        """Store current configuration in history."""
        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'config': self._config.copy()
        }
        
        self._config_history.append(snapshot)
        
        # Keep only last 10 snapshots
        if len(self._config_history) > 10:
            self._config_history.pop(0)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key path."""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value by key path."""
        keys = key.split('.')
        config = self._config
        
        # Navigate to parent
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set value
        old_value = config.get(keys[-1])
        config[keys[-1]] = value
        
        # Validate after change
        self._validate_config()
        
        # Notify change
        change = ConfigChangeEvent(
            config_path=key,
            old_value=old_value,
            new_value=value,
            change_type="update" if old_value is not None else "add"
        )
        
        for callback in self._change_callbacks:
            try:
                callback(change)
            except Exception as e:
                self.logger.error(f"Configuration change callback failed: {e}")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration."""
        return self._config.copy()
    
    def add_change_callback(self, callback: Callable[[ConfigChangeEvent], None]) -> None:
        """Add a configuration change callback."""
        self._change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[ConfigChangeEvent], None]) -> None:
        """Remove a configuration change callback."""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
    
    async def reload(self) -> None:
        """Manually reload all configuration files."""
        async with self._reload_lock:
            old_config = self._config.copy()
            self._config = {}
            self._load_all_configs()
            
            changes = self._detect_changes(old_config, self._config)
            for change in changes:
                for callback in self._change_callbacks:
                    try:
                        callback(change)
                    except Exception as e:
                        self.logger.error(f"Configuration change callback failed: {e}")
            
            self._last_reload = datetime.now()
            self.logger.info("Configuration manually reloaded")
    
    def get_history(self) -> List[Dict[str, Any]]:
        """Get configuration change history."""
        return self._config_history.copy()
    
    def rollback(self, steps: int = 1) -> bool:
        """Rollback configuration to previous state."""
        if len(self._config_history) <= steps:
            return False
        
        try:
            target_snapshot = self._config_history[-(steps + 1)]
            old_config = self._config.copy()
            self._config = target_snapshot['config'].copy()
            
            # Validate rolled back configuration
            self._validate_config()
            
            # Detect changes
            changes = self._detect_changes(old_config, self._config)
            for change in changes:
                for callback in self._change_callbacks:
                    try:
                        callback(change)
                    except Exception as e:
                        self.logger.error(f"Configuration change callback failed: {e}")
            
            self.logger.info(f"Configuration rolled back {steps} steps")
            return True
            
        except Exception as e:
            self.logger.error(f"Configuration rollback failed: {e}")
            return False
    
    def shutdown(self) -> None:
        """Shutdown the configuration manager."""
        self._stop_file_watching()
        self.logger.info("Configuration manager shutdown")
