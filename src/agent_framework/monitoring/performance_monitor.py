"""
Performance monitoring system for the agent framework.
"""

import asyncio
import logging
import psutil
from typing import Dict, List, Optional, Any, Callable, Awaitable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque

from ..core.config import FrameworkConfig


@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    memory_available: float
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_task_time: float
    queue_size: int
    agent_count: int
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """
    Monitors system and framework performance metrics.
    """

    def __init__(self, config: FrameworkConfig):
        """Initialize the performance monitor."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Metrics storage
        self._metrics_history: deque[PerformanceMetrics] = deque(
            maxlen=1000)  # Keep last 1000 metrics
        self._current_metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_usage=0.0,
            memory_usage=0.0,
            memory_available=0.0,
            active_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            average_task_time=0.0,
            queue_size=0,
            agent_count=0
        )

        # Monitoring state
        self._is_monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._collection_interval = 5.0  # seconds

        # Performance thresholds
        self._thresholds: Dict[str, float | int] = {
            'cpu_usage_warning': 80.0,
            'cpu_usage_critical': 95.0,
            'memory_usage_warning': 80.0,
            'memory_usage_critical': 95.0,
            'task_time_warning': 300.0,  # 5 minutes
            'queue_size_warning': 100,
            'queue_size_critical': 500
        }

        # Alert callbacks
        self._alert_callbacks: List[Callable[[
            Dict[str, Any]], Any | Awaitable[Any]]] = []

        # Custom metric collectors
        self._custom_collectors: Dict[str,
                                      Callable[[], Any | Awaitable[Any]]] = {}

    async def start_monitoring(self):
        """Start performance monitoring."""
        if self._is_monitoring:
            self.logger.warning("Performance monitoring is already running")
            return

        self._is_monitoring = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop())

        self.logger.info("Performance monitoring started")

    async def stop_monitoring(self):
        """Stop performance monitoring."""
        if not self._is_monitoring:
            return

        self._is_monitoring = False

        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        self.logger.info("Performance monitoring stopped")

    async def _monitoring_loop(self):
        """Main monitoring loop."""
        try:
            while self._is_monitoring:
                # Collect metrics
                await self._collect_metrics()

                # Check thresholds and trigger alerts
                await self._check_thresholds()

                # Wait for next collection
                await asyncio.sleep(self._collection_interval)

        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Error in monitoring loop: {e}")

    async def _collect_metrics(self):
        """Collect current performance metrics."""
        try:
            # System metrics
            cpu_usage = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            memory_available = memory.available / (1024 * 1024 * 1024)  # GB

            # Framework metrics (these would be injected by the framework)
            active_tasks = getattr(self, '_active_tasks_count', 0)
            completed_tasks = getattr(self, '_completed_tasks_count', 0)
            failed_tasks = getattr(self, '_failed_tasks_count', 0)
            average_task_time = getattr(self, '_average_task_time', 0.0)
            queue_size = getattr(self, '_queue_size', 0)
            agent_count = getattr(self, '_agent_count', 0)

            # Collect custom metrics
            custom_metrics: Dict[str, Any] = {}
            for name, collector in self._custom_collectors.items():
                try:
                    if asyncio.iscoroutinefunction(collector):
                        # type: ignore[misc]
                        custom_metrics[name] = await collector()
                    else:
                        custom_metrics[name] = collector()
                except Exception as e:
                    self.logger.warning(
                        f"Error collecting custom metric {name}: {e}")

            # Create metrics snapshot
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                memory_available=memory_available,
                active_tasks=active_tasks,
                completed_tasks=completed_tasks,
                failed_tasks=failed_tasks,
                average_task_time=average_task_time,
                queue_size=queue_size,
                agent_count=agent_count,
                custom_metrics=custom_metrics
            )

            # Store metrics
            self._current_metrics = metrics
            self._metrics_history.append(metrics)

        except Exception as e:
            self.logger.error(f"Error collecting metrics: {e}")

    async def _check_thresholds(self):
        """Check performance thresholds and trigger alerts."""
        metrics = self._current_metrics
        alerts: List[Dict[str, Any]] = []

        # CPU usage alerts
        if metrics.cpu_usage >= float(self._thresholds['cpu_usage_critical']):
            alerts.append({
                'level': 'critical',
                'metric': 'cpu_usage',
                'value': metrics.cpu_usage,
                'threshold': self._thresholds['cpu_usage_critical'],
                'message': f'Critical CPU usage: {metrics.cpu_usage:.1f}%'
            })
        elif metrics.cpu_usage >= float(self._thresholds['cpu_usage_warning']):
            alerts.append({
                'level': 'warning',
                'metric': 'cpu_usage',
                'value': metrics.cpu_usage,
                'threshold': self._thresholds['cpu_usage_warning'],
                'message': f'High CPU usage: {metrics.cpu_usage:.1f}%'
            })

        # Memory usage alerts
        if metrics.memory_usage >= float(self._thresholds['memory_usage_critical']):
            alerts.append({
                'level': 'critical',
                'metric': 'memory_usage',
                'value': metrics.memory_usage,
                'threshold': self._thresholds['memory_usage_critical'],
                'message': f'Critical memory usage: {metrics.memory_usage:.1f}%'
            })
        elif metrics.memory_usage >= float(self._thresholds['memory_usage_warning']):
            alerts.append({
                'level': 'warning',
                'metric': 'memory_usage',
                'value': metrics.memory_usage,
                'threshold': self._thresholds['memory_usage_warning'],
                'message': f'High memory usage: {metrics.memory_usage:.1f}%'
            })

        # Task time alerts
        if metrics.average_task_time >= float(self._thresholds['task_time_warning']):
            alerts.append({
                'level': 'warning',
                'metric': 'average_task_time',
                'value': metrics.average_task_time,
                'threshold': self._thresholds['task_time_warning'],
                'message': f'High average task time: {metrics.average_task_time:.1f}s'
            })

        # Queue size alerts
        if metrics.queue_size >= int(self._thresholds['queue_size_critical']):
            alerts.append({
                'level': 'critical',
                'metric': 'queue_size',
                'value': metrics.queue_size,
                'threshold': self._thresholds['queue_size_critical'],
                'message': f'Critical queue size: {metrics.queue_size}'
            })
        elif metrics.queue_size >= int(self._thresholds['queue_size_warning']):
            alerts.append({
                'level': 'warning',
                'metric': 'queue_size',
                'value': metrics.queue_size,
                'threshold': self._thresholds['queue_size_warning'],
                'message': f'High queue size: {metrics.queue_size}'
            })

        # Trigger alert callbacks
        for alert in alerts:
            self.logger.warning(alert['message'])
            for callback in self._alert_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(alert)  # type: ignore[misc]
                    else:
                        callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")

    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        return self._current_metrics

    def get_metrics_history(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """Get metrics history for the specified time period."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self._metrics_history if m.timestamp >= cutoff_time]

    def get_metrics_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """Get summary statistics for the specified time period."""
        history = self.get_metrics_history(minutes)

        if not history:
            return {}

        cpu_values = [m.cpu_usage for m in history]
        memory_values = [m.memory_usage for m in history]
        task_times = [
            m.average_task_time for m in history if m.average_task_time > 0]

        return {
            'period_minutes': minutes,
            'sample_count': len(history),
            'cpu_usage': {
                'min': min(cpu_values),
                'max': max(cpu_values),
                'avg': sum(cpu_values) / len(cpu_values)
            },
            'memory_usage': {
                'min': min(memory_values),
                'max': max(memory_values),
                'avg': sum(memory_values) / len(memory_values)
            },
            'task_performance': {
                'avg_task_time': sum(task_times) / len(task_times) if task_times else 0,
                'total_completed': history[-1].completed_tasks - history[0].completed_tasks if len(history) > 1 else 0,
                'total_failed': history[-1].failed_tasks - history[0].failed_tasks if len(history) > 1 else 0
            }
        }

    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], Any | Awaitable[Any]]):
        """Add an alert callback function."""
        self._alert_callbacks.append(callback)

    def add_custom_metric_collector(self, name: str, collector: Callable[[], Any | Awaitable[Any]]):
        """Add a custom metric collector."""
        self._custom_collectors[name] = collector

    def update_framework_metrics(self, **kwargs):
        """Update framework-specific metrics."""
        for key, value in kwargs.items():
            setattr(self, f'_{key}', value)

    def configure_thresholds(self, **thresholds):
        """Configure performance thresholds."""
        for key, value in thresholds.items():
            if key in self._thresholds:
                self._thresholds[key] = value
                self.logger.info(f"Updated threshold {key} to {value}")

    def set_collection_interval(self, seconds: float):
        """Set the metrics collection interval."""
        self._collection_interval = max(1.0, seconds)  # Minimum 1 second
        self.logger.info(
            f"Set collection interval to {self._collection_interval}s")
