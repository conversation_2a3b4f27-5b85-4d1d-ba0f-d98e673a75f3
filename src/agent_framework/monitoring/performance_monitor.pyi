"""
Performance monitoring for the agent framework.
"""

import asyncio
from typing import Any, Dict, List, Optional, Callable, Awaitable
from datetime import datetime
from dataclasses import dataclass

@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    memory_available: float
    active_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_task_time: float
    queue_size: int
    agent_count: int
    custom_metrics: Dict[str, Any]

@dataclass
class AlertThreshold:
    """Alert threshold configuration."""
    metric_name: str
    threshold_value: float
    comparison: str  # 'gt', 'lt', 'eq'
    enabled: bool
    alert_message: str

class PerformanceMonitor:
    """
    Performance monitor for tracking system metrics and health.
    
    Provides:
    - Real-time performance monitoring
    - Configurable alert thresholds
    - Custom metric collection
    - Historical data tracking
    - Health status reporting
    """
    
    def __init__(self, 
                 collection_interval: float = 5.0,
                 history_size: int = 1000) -> None: ...
    
    async def start_monitoring(self) -> None:
        """Start performance monitoring."""
        ...
    
    async def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        ...
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        ...
    
    def get_latest_metrics(self) -> Optional[PerformanceMetrics]:
        """Get the latest metrics snapshot."""
        ...
    
    def get_metrics_history(self, count: Optional[int] = None) -> List[PerformanceMetrics]:
        """Get historical metrics."""
        ...
    
    def get_average_metrics(self, duration_minutes: int = 5) -> Optional[PerformanceMetrics]:
        """Get average metrics over a time period."""
        ...
    
    def add_alert_threshold(self, threshold: AlertThreshold) -> None:
        """Add an alert threshold."""
        ...
    
    def remove_alert_threshold(self, metric_name: str) -> None:
        """Remove an alert threshold."""
        ...
    
    def add_custom_metric_collector(self, name: str, collector: Callable[[], Any]) -> None:
        """Add a custom metric collector."""
        ...
    
    def remove_custom_metric_collector(self, name: str) -> None:
        """Remove a custom metric collector."""
        ...
    
    def add_alert_handler(self, handler: Callable[[str, PerformanceMetrics], Awaitable[None]]) -> None:
        """Add an alert handler."""
        ...
    
    def remove_alert_handler(self, handler: Callable[[str, PerformanceMetrics], Awaitable[None]]) -> None:
        """Remove an alert handler."""
        ...
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status."""
        ...
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        ...
    
    def export_metrics(self, format: str = "json") -> str:
        """Export metrics in specified format."""
        ...
    
    # Properties
    @property
    def is_monitoring(self) -> bool: ...
    
    @property
    def collection_interval(self) -> float: ...
    
    @property
    def metrics_count(self) -> int: ...
    
    @property
    def alert_thresholds(self) -> List[AlertThreshold]: ...
