"""
Programming Assistant Agent Framework

A comprehensive, high-performance programming assistant agent framework
with plugin-based functionality, built on top of AutoGen.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID
from datetime import datetime
from enum import Enum

from .core.orchestrator import AgentOrchestrator as AgentOrchestrator
from .core.config import FrameworkConfig as FrameworkConfig
from .core.types import (
    Task as Task,
    TaskResult as TaskResult, 
    TaskStatus as TaskStatus,
    TaskPriority as TaskPriority
)

__version__: str
__all__: List[str]
