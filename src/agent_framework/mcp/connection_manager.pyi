"""
MCP connection manager for handling multiple server connections.
"""

import async<PERSON>
from typing import Dict, List, Optional, Any

from .client import MCPClient
from .types import (
    MCPServerInfo, MCPConnectionStatus,
    MCPToolCallRequest, MCPToolCallResponse,
    MCPResourceRequest, MCPResourceResponse,
    MCPPromptRequest, MCPPromptResponse
)
from ..core.config import MCPConfig

class MCPConnectionManager:
    """
    Manager for handling multiple MCP server connections.
    
    Provides centralized management of MCP clients, connection pooling,
    load balancing, and failover capabilities.
    """
    
    def __init__(self, config: MCPConfig) -> None: ...
    
    async def initialize(self) -> None:
        """Initialize the connection manager."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the connection manager and all connections."""
        ...
    
    async def add_server(self, server_info: MCPServerInfo) -> bool:
        """Add a new MCP server."""
        ...
    
    async def remove_server(self, server_name: str) -> bool:
        """Remove an MCP server."""
        ...
    
    async def connect_server(self, server_name: str) -> bool:
        """Connect to a specific server."""
        ...
    
    async def disconnect_server(self, server_name: str) -> bool:
        """Disconnect from a specific server."""
        ...
    
    async def reconnect_server(self, server_name: str) -> bool:
        """Reconnect to a specific server."""
        ...
    
    async def get_client(self, server_name: str) -> Optional[MCPClient]:
        """Get a client for a specific server."""
        ...
    
    async def get_server_status(self, server_name: str) -> Optional[MCPConnectionStatus]:
        """Get the status of a server connection."""
        ...
    
    async def list_servers(self) -> List[MCPServerInfo]:
        """List all configured servers."""
        ...
    
    async def list_connected_servers(self) -> List[str]:
        """List all connected servers."""
        ...
    
    async def call_tool(self, 
                       server_name: str,
                       request: MCPToolCallRequest) -> MCPToolCallResponse:
        """Call a tool on a specific server."""
        ...
    
    async def call_tool_any_server(self, 
                                  tool_name: str,
                                  arguments: Dict[str, Any]) -> Optional[MCPToolCallResponse]:
        """Call a tool on any available server that supports it."""
        ...
    
    async def get_resource(self, 
                          server_name: str,
                          request: MCPResourceRequest) -> MCPResourceResponse:
        """Get a resource from a specific server."""
        ...
    
    async def get_prompt(self, 
                        server_name: str,
                        request: MCPPromptRequest) -> MCPPromptResponse:
        """Get a prompt from a specific server."""
        ...
    
    async def get_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all tools from all connected servers."""
        ...
    
    async def get_all_resources(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all resources from all connected servers."""
        ...
    
    async def get_all_prompts(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all prompts from all connected servers."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all connections."""
        ...
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        ...
    
    # Properties
    @property
    def config(self) -> MCPConfig: ...
    
    @property
    def server_count(self) -> int: ...
    
    @property
    def connected_server_count(self) -> int: ...
