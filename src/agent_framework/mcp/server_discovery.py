"""
MCP server discovery and auto-configuration.
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Dict, Optional  # removed unused: List, Set

from .types import MCPServerInfo, MCPTransportType
from ..core.config import MCPServerConfig  # kept if referenced externally

class MCPServerDiscovery:
    """
    Discovery service for MCP servers.
    
    Automatically discovers available MCP servers from various sources:
    - Package managers (npm, pip, etc.)
    - Local installations
    - Configuration files
    - Environment variables
    """
    
    def __init__(self, discovery_timeout: int = 10):
        """
        Initialize the server discovery service.
        
        Args:
            discovery_timeout: Timeout for discovery operations in seconds
        """
        self.logger = logging.getLogger(__name__)
        self.discovery_timeout = discovery_timeout
        self._discovered_servers: Dict[str, MCPServerInfo] = {}
    
    async def discover_servers(self) -> Dict[str, MCPServerInfo]:
        """
        Discover available MCP servers.
        
        Returns:
            Dictionary mapping server names to server information
        """
        self.logger.info("Starting MCP server discovery...")
        
        # Clear previous discoveries
        self._discovered_servers.clear()
        
        # Run discovery methods in parallel
        discovery_tasks = [
            self._discover_npm_servers(),
            self._discover_pip_servers(),
            self._discover_local_servers(),
            self._discover_from_config(),
            self._discover_from_environment()
        ]
        
        try:
            await asyncio.wait_for(
                asyncio.gather(*discovery_tasks, return_exceptions=True),
                timeout=self.discovery_timeout
            )
        except asyncio.TimeoutError:
            self.logger.warning("Server discovery timed out")
        
        self.logger.info(f"Discovered {len(self._discovered_servers)} MCP servers")
        return self._discovered_servers.copy()
    
    async def _discover_npm_servers(self) -> None:
        """Discover MCP servers from npm packages."""
        try:
            # Check if npm is available
            result = await asyncio.create_subprocess_exec(
                "npm", "list", "-g", "--depth=0", "--json",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, _stderr = await result.communicate()
            
            if result.returncode == 0:
                npm_data = json.loads(stdout.decode())
                dependencies = npm_data.get("dependencies", {})
                
                # Look for MCP server packages
                for package_name, package_info in dependencies.items():
                    if self._is_mcp_server_package(package_name):
                        server_info = await self._create_npm_server_info(package_name, package_info)
                        if server_info:
                            self._discovered_servers[package_name] = server_info
                            self.logger.debug(f"Discovered npm MCP server: {package_name}")
            
        except Exception as e:
            self.logger.debug(f"NPM discovery failed: {e}")
    
    async def _discover_pip_servers(self) -> None:
        """Discover MCP servers from pip packages."""
        try:
            # Check if pip is available
            result = await asyncio.create_subprocess_exec(
                "pip", "list", "--format=json",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, _stderr = await result.communicate()
            
            if result.returncode == 0:
                pip_packages = json.loads(stdout.decode())
                
                # Look for MCP server packages
                for package in pip_packages:
                    package_name = package.get("name", "")
                    if self._is_mcp_server_package(package_name):
                        server_info = await self._create_pip_server_info(package)
                        if server_info:
                            self._discovered_servers[package_name] = server_info
                            self.logger.debug(f"Discovered pip MCP server: {package_name}")
            
        except Exception as e:
            self.logger.debug(f"Pip discovery failed: {e}")
    
    async def _discover_local_servers(self) -> None:
        """Discover MCP servers from local installations."""
        try:
            # Common locations for MCP servers
            search_paths = [
                Path.home() / ".local" / "bin",
                Path("/usr/local/bin"),
                Path("/opt/mcp-servers"),
                Path.cwd() / "mcp-servers"
            ]
            
            for search_path in search_paths:
                if search_path.exists() and search_path.is_dir():
                    await self._scan_directory_for_servers(search_path)
            
        except Exception as e:
            self.logger.debug(f"Local discovery failed: {e}")
    
    async def _discover_from_config(self) -> None:
        """Discover MCP servers from configuration files."""
        try:
            # Look for MCP configuration files
            config_paths = [
                Path.home() / ".mcp" / "servers.json",
                Path.cwd() / "mcp-servers.json",
                Path("/etc/mcp/servers.json"),
            ]
            
            for config_path in config_paths:
                if config_path.exists():
                    await self._load_servers_from_config(config_path)
            
        except Exception as e:
            self.logger.debug(f"Config discovery failed: {e}")
    
    async def _discover_from_environment(self) -> None:
        """Discover MCP servers from environment variables."""
        try:
            # Look for MCP_SERVERS environment variable
            mcp_servers_env = os.getenv("MCP_SERVERS")
            if mcp_servers_env:
                servers_config = json.loads(mcp_servers_env)
                for server_name, server_config in servers_config.items():
                    server_info = self._create_server_info_from_config(server_name, server_config)
                    if server_info:
                        self._discovered_servers[server_name] = server_info
                        self.logger.debug(f"Discovered environment MCP server: {server_name}")
            
        except Exception as e:
            self.logger.debug(f"Environment discovery failed: {e}")
    
    def _is_mcp_server_package(self, package_name: str) -> bool:
        """Check if a package name indicates an MCP server."""
        mcp_indicators = [
            "mcp-server",
            "mcp_server",
            "@modelcontextprotocol/server",
            "model-context-protocol"
        ]
        
        package_lower = package_name.lower()
        return any(indicator in package_lower for indicator in mcp_indicators)
    
    async def _create_npm_server_info(self, package_name: str, package_info: Dict) -> Optional[MCPServerInfo]:
        """Create server info for an npm package."""
        try:
            # Try to determine the command for the npm package
            if package_name.startswith("@modelcontextprotocol/server-"):
                server_type = package_name.replace("@modelcontextprotocol/server-", "")
                command = "npx"
                args = ["-y", package_name]
                
                return MCPServerInfo(
                    name=f"npm-{server_type}",
                    command=command,
                    args=args,
                    description=f"NPM MCP server for {server_type}",
                    transport=MCPTransportType.STDIO
                )
            
        except Exception as e:
            self.logger.debug(f"Failed to create npm server info for {package_name}: {e}")
        
        return None
    
    async def _create_pip_server_info(self, package: Dict) -> Optional[MCPServerInfo]:
        """Create server info for a pip package."""
        try:
            package_name = package.get("name", "")
            
            # Try to determine the command for the pip package
            if "mcp-server" in package_name.lower():
                # Assume the package provides a command with the same name
                command = package_name.replace("_", "-")
                
                return MCPServerInfo(
                    name=f"pip-{package_name}",
                    command=command,
                    args=[],
                    description=f"Pip MCP server: {package_name}",
                    transport=MCPTransportType.STDIO
                )
            
        except Exception as e:
            self.logger.debug(f"Failed to create pip server info for {package}: {e}")
        
        return None
    
    async def _scan_directory_for_servers(self, directory: Path) -> None:
        """Scan a directory for MCP server executables."""
        try:
            for file_path in directory.iterdir():
                if file_path.is_file() and file_path.stat().st_mode & 0o111:  # Executable
                    file_name = file_path.name
                    if self._is_mcp_server_package(file_name):
                        server_info = MCPServerInfo(
                            name=f"local-{file_name}",
                            command=str(file_path),
                            args=[],
                            description=f"Local MCP server: {file_name}",
                            transport=MCPTransportType.STDIO
                        )
                        self._discovered_servers[server_info.name] = server_info
                        self.logger.debug(f"Discovered local MCP server: {file_name}")
            
        except Exception as e:
            self.logger.debug(f"Failed to scan directory {directory}: {e}")
    
    async def _load_servers_from_config(self, config_path: Path) -> None:
        """Load servers from a configuration file."""
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            servers = config_data.get("servers", {})
            for server_name, server_config in servers.items():
                server_info = self._create_server_info_from_config(server_name, server_config)
                if server_info:
                    self._discovered_servers[server_name] = server_info
                    self.logger.debug(f"Discovered config MCP server: {server_name}")
            
        except Exception as e:
            self.logger.debug(f"Failed to load config from {config_path}: {e}")
    
    def _create_server_info_from_config(self, server_name: str, config: Dict) -> Optional[MCPServerInfo]:
        """Create server info from configuration data."""
        try:
            return MCPServerInfo(
                name=server_name,
                command=config.get("command", ""),
                args=config.get("args", []),
                env=config.get("env", {}),
                timeout_seconds=config.get("timeout", 30),
                description=config.get("description"),
                enabled=config.get("enabled", True),
                transport=MCPTransportType(config.get("transport", "stdio"))
            )
        except Exception as e:
            self.logger.debug(f"Failed to create server info from config for {server_name}: {e}")
            return None
    
    async def validate_server(self, server_info: MCPServerInfo) -> bool:
        """
        Validate that a discovered server is actually functional.
        
        Args:
            server_info: Server information to validate
            
        Returns:
            True if the server is functional
        """
        try:
            # Try to start the server process briefly
            process = await asyncio.create_subprocess_exec(
                server_info.command,
                *server_info.args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=server_info.env
            )
            
            # Give it a moment to start
            await asyncio.sleep(0.5)
            
            # Terminate the process
            process.terminate()
            await process.wait()
            
            return True
            
        except Exception as e:
            self.logger.debug(f"Server validation failed for {server_info.name}: {e}")
            return False
