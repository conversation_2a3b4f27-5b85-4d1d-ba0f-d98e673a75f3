"""
MCP connection manager for handling multiple server connections.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any

from .client import MC<PERSON><PERSON>
from .types import (
    MCPServerInfo, MCPConnectionStatus,
    MCPToolCallRequest, MCPToolCallResponse,
    MCPResourceRequest, MCPResourceResponse,
    MCPPromptRequest, MCPPromptResponse
)
from ..core.config import MCPConfig


class MCPConnectionManager:
    """
    Manager for handling multiple MCP server connections.
    
    Provides centralized management of MCP clients, connection pooling,
    load balancing, and failover capabilities.
    """
    
    def __init__(self, config: MCPConfig):
        """
        Initialize the connection manager.
        
        Args:
            config: MCP configuration
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Connection management
        self._clients: Dict[str, MCPClient] = {}
        self._server_infos: Dict[str, MCPServerInfo] = {}
        self._connection_pool: Dict[str, List[MCPClient]] = {}
        
        # Health monitoring
        self._health_check_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Load balancing
        self._round_robin_counters: Dict[str, int] = {}

        # Tool integration
        from .tool_integration import MCPToolIntegration
        self._tool_integration: Optional[MCPToolIntegration] = MCPToolIntegration(self)
    
    async def start(self) -> None:
        """Start the connection manager."""
        if self._running:
            return
        
        self.logger.info("Starting MCP connection manager...")
        self._running = True
        
        # Initialize server configurations
        await self._initialize_servers()
        
        # Start health monitoring
        self._health_check_task = asyncio.create_task(self._health_check_loop())

        # Initialize tool integration
        if self._tool_integration:
            await self._tool_integration.initialize()

        self.logger.info("MCP connection manager started")
    
    async def stop(self) -> None:
        """Stop the connection manager."""
        if not self._running:
            return
        
        self.logger.info("Stopping MCP connection manager...")
        self._running = False
        
        # Cancel health check task
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Disconnect all clients
        await self._disconnect_all_clients()
        
        self.logger.info("MCP connection manager stopped")
    
    async def get_client(self, server_name: str) -> Optional[MCPClient]:
        """
        Get a connected client for the specified server.
        
        Args:
            server_name: Name of the MCP server
            
        Returns:
            Connected MCP client or None if not available
        """
        if server_name not in self._server_infos:
            self.logger.error(f"Unknown MCP server: {server_name}")
            return None
        
        # Check if we have an existing connected client
        if server_name in self._clients:
            client = self._clients[server_name]
            if client._is_connected():
                return client
            else:
                # Remove disconnected client
                del self._clients[server_name]
        
        # Create new client and connect
        server_info = self._server_infos[server_name]
        if not server_info.enabled:
            self.logger.warning(f"MCP server {server_name} is disabled")
            return None
        
        client = MCPClient(server_info)
        success = await client.connect()
        
        if success:
            self._clients[server_name] = client
            return client
        else:
            self.logger.error(f"Failed to connect to MCP server: {server_name}")
            return None
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> MCPToolCallResponse:
        """
        Call a tool on the specified server.
        
        Args:
            server_name: Name of the MCP server
            tool_name: Name of the tool to call
            arguments: Tool arguments
            
        Returns:
            Tool call response
        """
        client = await self.get_client(server_name)
        if not client:
            return MCPToolCallResponse(
                success=False,
                error=f"Could not connect to server {server_name}",
                server_name=server_name,
                tool_name=tool_name
            )
        
        request = MCPToolCallRequest(
            server_name=server_name,
            tool_name=tool_name,
            arguments=arguments or {}
        )
        
        return await client.call_tool(request)
    
    async def get_resource(self, server_name: str, resource_uri: str, parameters: Optional[Dict[str, Any]] = None) -> MCPResourceResponse:
        """
        Get a resource from the specified server.
        
        Args:
            server_name: Name of the MCP server
            resource_uri: URI of the resource
            parameters: Resource parameters
            
        Returns:
            Resource response
        """
        client = await self.get_client(server_name)
        if not client:
            return MCPResourceResponse(
                success=False,
                error=f"Could not connect to server {server_name}",
                server_name=server_name,
                resource_uri=resource_uri
            )
        
        request = MCPResourceRequest(
            server_name=server_name,
            resource_uri=resource_uri,
            parameters=parameters or {}
        )
        
        return await client.get_resource(request)
    
    async def get_prompt(self, server_name: str, prompt_name: str, arguments: Optional[Dict[str, Any]] = None) -> MCPPromptResponse:
        """
        Get a prompt from the specified server.
        
        Args:
            server_name: Name of the MCP server
            prompt_name: Name of the prompt
            arguments: Prompt arguments
            
        Returns:
            Prompt response
        """
        client = await self.get_client(server_name)
        if not client:
            return MCPPromptResponse(
                success=False,
                error=f"Could not connect to server {server_name}",
                server_name=server_name,
                prompt_name=prompt_name
            )
        
        request = MCPPromptRequest(
            server_name=server_name,
            prompt_name=prompt_name,
            arguments=arguments or {}
        )
        
        return await client.get_prompt(request)
    
    async def list_servers(self) -> List[str]:
        """List all configured server names."""
        return list(self._server_infos.keys())
    
    async def get_server_status(self, server_name: str) -> Optional[MCPConnectionStatus]:
        """Get the connection status of a server."""
        if server_name not in self._server_infos:
            return None
        
        if server_name in self._clients:
            return self._clients[server_name].connection_state.server_info.status
        
        return self._server_infos[server_name].status
    
    async def get_all_tools(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all tools from all connected servers."""
        all_tools: Dict[str, List[Dict[str, Any]]] = {}
        
        for server_name in self._server_infos:
            client = await self.get_client(server_name)
            if client:
                try:
                    tools = await client.list_tools()
                    all_tools[server_name] = tools
                except Exception as e:
                    self.logger.error(f"Failed to get tools from {server_name}: {e}")
                    all_tools[server_name] = []
            else:
                all_tools[server_name] = []
        
        return all_tools
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        stats: Dict[str, Any] = {
            "total_servers": len(self._server_infos),
            "connected_servers": 0,
            "disconnected_servers": 0,
            "error_servers": 0,
            "server_details": {}
        }
        
        for server_name, server_info in self._server_infos.items():
            status = await self.get_server_status(server_name)
            
            if status == MCPConnectionStatus.CONNECTED:
                stats["connected_servers"] += 1
            elif status == MCPConnectionStatus.DISCONNECTED:
                stats["disconnected_servers"] += 1
            elif status == MCPConnectionStatus.ERROR:
                stats["error_servers"] += 1
            
            client = self._clients.get(server_name)
            metrics = client.get_connection_metrics() if client else None
            
            stats["server_details"][server_name] = {
                "status": status.value if status else "unknown",
                "enabled": server_info.enabled,
                "connection_attempts": server_info.connection_attempts,
                "last_error": server_info.last_error,
                # fixed: pydantic v2 uses model_dump()
                "metrics": metrics.model_dump() if metrics else None
            }
        
        return stats

    def get_tool_integration(self) -> Optional['MCPToolIntegration']:
        """Get the tool integration instance."""
        return self._tool_integration

    async def call_tool_enhanced(self,
                                tool_name: str,
                                arguments: Optional[Dict[str, Any]] = None,
                                use_cache: bool = True,
                                max_retries: int = 1) -> Dict[str, Any]:
        """
        Enhanced tool calling with caching and retry logic.

        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            use_cache: Whether to use cached results
            max_retries: Maximum number of retries

        Returns:
            Tool execution result
        """
        if not self._tool_integration:
            # Fallback to basic tool calling
            # Find which server has this tool
            for server_name in self._server_infos.keys():
                try:
                    response = await self.call_tool(server_name, tool_name, arguments)
                    return {
                        "success": response.success,
                        "data": response.data,
                        "error": response.error,
                        "server_name": server_name,
                        "tool_name": tool_name
                    }
                except Exception:
                    continue

            return {
                "success": False,
                "error": f"Tool {tool_name} not found on any server",
                "tool_name": tool_name
            }

        # Use enhanced tool integration
        if max_retries > 1:
            result = await self._tool_integration.call_tool_with_retry(
                tool_name, arguments, max_retries
            )
        else:
            result = await self._tool_integration.call_tool(
                tool_name, arguments, use_cache
            )

        return {
            "success": result.success,
            "data": result.data,
            "error": result.error,
            "execution_time": result.execution_time,
            "server_name": result.server_name,
            "tool_name": result.tool_name,
            "cached": getattr(result, 'cached', False)
        }
    
    async def _initialize_servers(self) -> None:
        """Initialize server configurations."""
        for server_name, server_config in self.config.servers.items():
            server_info = MCPServerInfo(
                name=server_name,
                command=server_config.command,
                args=server_config.args,
                env=server_config.env,
                timeout_seconds=server_config.timeout_seconds,
                retry_attempts=server_config.retry_attempts,
                retry_delay_seconds=server_config.retry_delay_seconds,
                description=server_config.description,
                enabled=server_config.enabled
            )
            
            self._server_infos[server_name] = server_info
            self._round_robin_counters[server_name] = 0
        
        self.logger.info(f"Initialized {len(self._server_infos)} MCP servers")
    
    async def _health_check_loop(self) -> None:
        """Health check loop for monitoring server connections."""
        while self._running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(5)
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on all servers."""
        for server_name, client in self._clients.items():
            try:
                if not client._is_connected():
                    self.logger.warning(f"Server {server_name} is not connected, attempting reconnection")
                    await client.connect()
            except Exception as e:
                self.logger.error(f"Health check failed for {server_name}: {e}")
    
    async def _disconnect_all_clients(self) -> None:
        """Disconnect all clients."""
        for client in self._clients.values():
            try:
                await client.disconnect()
            except Exception as e:
                self.logger.error(f"Error disconnecting client: {e}")
        
        self._clients.clear()
