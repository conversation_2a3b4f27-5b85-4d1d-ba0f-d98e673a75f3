"""
Enhanced MCP tool integration for seamless tool access and execution.

Provides high-level interfaces for working with MCP tools, including:
- Tool discovery and registration
- Automatic tool wrapping
- Error handling and retry logic
- Tool result caching
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta

from .types import MCPToolCallRequest, MCPToolCallResponse, MCPServerInfo
from .connection_manager import MCPConnectionManager


@dataclass
class ToolMetadata:
    """Metadata for an MCP tool."""
    name: str
    server_name: str
    description: str
    input_schema: Dict[str, Any]
    output_schema: Optional[Dict[str, Any]] = None
    examples: List[Dict[str, Any]] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    last_used: Optional[datetime] = None
    usage_count: int = 0
    success_rate: float = 1.0


@dataclass
class ToolResult:
    """Result from tool execution."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    server_name: str = ""
    tool_name: str = ""
    cached: bool = False


class MCPToolIntegration:
    """
    High-level integration for MCP tools.
    
    Provides simplified access to MCP tools with automatic discovery,
    caching, error handling, and retry logic.
    """
    
    def __init__(self, connection_manager: MCPConnectionManager):
        """
        Initialize the tool integration.
        
        Args:
            connection_manager: MCP connection manager instance
        """
        self.connection_manager = connection_manager
        self.logger = logging.getLogger(__name__)
        
        # Tool registry
        self._tools: Dict[str, ToolMetadata] = {}
        self._tool_aliases: Dict[str, str] = {}  # alias -> tool_name
        
        # Caching
        self._result_cache: Dict[str, ToolResult] = {}
        self._cache_ttl = timedelta(minutes=5)
        
        # Performance tracking
        self._performance_stats: Dict[str, Dict[str, Any]] = {}
        
        # Auto-discovery settings
        self._auto_discovery_enabled = True
        self._discovery_interval = timedelta(minutes=10)
        self._last_discovery = datetime.min
    
    async def initialize(self) -> None:
        """Initialize the tool integration."""
        self.logger.info("Initializing MCP tool integration...")
        
        # Discover available tools
        await self.discover_tools()
        
        self.logger.info(f"Tool integration initialized with {len(self._tools)} tools")
    
    async def discover_tools(self) -> Dict[str, List[str]]:
        """
        Discover all available tools from connected MCP servers.
        
        Returns:
            Dictionary mapping server names to lists of tool names
        """
        self.logger.info("Discovering MCP tools...")
        
        discovered_tools = {}
        
        for server_name in self.connection_manager._server_infos.keys():
            try:
                client = await self.connection_manager.get_client(server_name)
                if not client:
                    continue
                
                # Get tools from server
                tools = await client.list_tools()
                server_tools = []
                
                for tool_info in tools:
                    tool_name = tool_info.get("name", "")
                    if tool_name:
                        # Register tool metadata
                        metadata = ToolMetadata(
                            name=tool_name,
                            server_name=server_name,
                            description=tool_info.get("description", ""),
                            input_schema=tool_info.get("inputSchema", {}),
                            output_schema=tool_info.get("outputSchema"),
                        )
                        
                        self._tools[tool_name] = metadata
                        server_tools.append(tool_name)
                        
                        # Create aliases for common patterns
                        self._create_tool_aliases(tool_name)
                
                discovered_tools[server_name] = server_tools
                self.logger.info(f"Discovered {len(server_tools)} tools from {server_name}")
                
            except Exception as e:
                self.logger.error(f"Error discovering tools from {server_name}: {e}")
                discovered_tools[server_name] = []
        
        self._last_discovery = datetime.now()
        return discovered_tools
    
    async def call_tool(self, 
                       tool_name: str, 
                       arguments: Optional[Dict[str, Any]] = None,
                       use_cache: bool = True,
                       timeout: Optional[float] = None) -> ToolResult:
        """
        Call an MCP tool with enhanced error handling and caching.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            use_cache: Whether to use cached results
            timeout: Timeout for the call
            
        Returns:
            Tool execution result
        """
        # Resolve aliases
        actual_tool_name = self._tool_aliases.get(tool_name, tool_name)
        
        if actual_tool_name not in self._tools:
            # Try auto-discovery if enabled
            if self._auto_discovery_enabled:
                await self._maybe_rediscover_tools()
                actual_tool_name = self._tool_aliases.get(tool_name, tool_name)
            
            if actual_tool_name not in self._tools:
                return ToolResult(
                    success=False,
                    error=f"Tool not found: {tool_name}",
                    tool_name=tool_name
                )
        
        tool_metadata = self._tools[actual_tool_name]
        
        # Check cache
        if use_cache:
            cached_result = self._get_cached_result(actual_tool_name, arguments)
            if cached_result:
                cached_result.cached = True
                return cached_result
        
        # Execute tool
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Validate arguments against schema
            validation_error = self._validate_arguments(tool_metadata, arguments)
            if validation_error:
                return ToolResult(
                    success=False,
                    error=f"Argument validation failed: {validation_error}",
                    tool_name=actual_tool_name,
                    server_name=tool_metadata.server_name
                )
            
            # Call the tool
            response = await self.connection_manager.call_tool(
                tool_metadata.server_name,
                actual_tool_name,
                arguments
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # Create result
            result = ToolResult(
                success=response.success,
                data=response.data if response.success else None,
                error=response.error if not response.success else None,
                execution_time=execution_time,
                server_name=tool_metadata.server_name,
                tool_name=actual_tool_name
            )
            
            # Update statistics
            self._update_tool_stats(tool_metadata, result)
            
            # Cache successful results
            if result.success and use_cache:
                self._cache_result(actual_tool_name, arguments, result)
            
            return result
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            
            result = ToolResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                server_name=tool_metadata.server_name,
                tool_name=actual_tool_name
            )
            
            # Update statistics for failed calls
            self._update_tool_stats(tool_metadata, result)
            
            return result
    
    async def call_tool_with_retry(self,
                                  tool_name: str,
                                  arguments: Optional[Dict[str, Any]] = None,
                                  max_retries: int = 3,
                                  retry_delay: float = 1.0) -> ToolResult:
        """
        Call a tool with automatic retry logic.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
            
        Returns:
            Tool execution result
        """
        last_result = None
        
        for attempt in range(max_retries + 1):
            result = await self.call_tool(tool_name, arguments, use_cache=(attempt == 0))
            
            if result.success:
                return result
            
            last_result = result
            
            if attempt < max_retries:
                self.logger.warning(
                    f"Tool {tool_name} failed (attempt {attempt + 1}/{max_retries + 1}): {result.error}"
                )
                await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
        
        return last_result or ToolResult(
            success=False,
            error="All retry attempts failed",
            tool_name=tool_name
        )
    
    def get_available_tools(self, server_name: Optional[str] = None) -> List[ToolMetadata]:
        """
        Get list of available tools.
        
        Args:
            server_name: Optional server name to filter by
            
        Returns:
            List of tool metadata
        """
        if server_name:
            return [tool for tool in self._tools.values() if tool.server_name == server_name]
        return list(self._tools.values())
    
    def get_tool_info(self, tool_name: str) -> Optional[ToolMetadata]:
        """Get information about a specific tool."""
        actual_tool_name = self._tool_aliases.get(tool_name, tool_name)
        return self._tools.get(actual_tool_name)
    
    def get_performance_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get performance statistics for all tools."""
        return self._performance_stats.copy()
    
    def clear_cache(self) -> None:
        """Clear the result cache."""
        self._result_cache.clear()
        self.logger.info("Tool result cache cleared")
    
    def _create_tool_aliases(self, tool_name: str) -> None:
        """Create aliases for a tool based on common patterns."""
        # Create lowercase alias
        self._tool_aliases[tool_name.lower()] = tool_name
        
        # Create underscore to dash alias
        if '_' in tool_name:
            self._tool_aliases[tool_name.replace('_', '-')] = tool_name
        
        # Create dash to underscore alias
        if '-' in tool_name:
            self._tool_aliases[tool_name.replace('-', '_')] = tool_name
    
    def _validate_arguments(self, tool_metadata: ToolMetadata, arguments: Optional[Dict[str, Any]]) -> Optional[str]:
        """Validate tool arguments against schema."""
        if not arguments and not tool_metadata.input_schema:
            return None
        
        # Basic validation - in a real implementation, use jsonschema
        schema = tool_metadata.input_schema
        required_fields = schema.get("required", [])
        
        if not arguments:
            arguments = {}
        
        for field in required_fields:
            if field not in arguments:
                return f"Missing required field: {field}"
        
        return None
    
    def _get_cached_result(self, tool_name: str, arguments: Optional[Dict[str, Any]]) -> Optional[ToolResult]:
        """Get cached result if available and not expired."""
        cache_key = self._make_cache_key(tool_name, arguments)
        
        if cache_key in self._result_cache:
            result = self._result_cache[cache_key]
            # Check if cache is still valid (simplified - in real implementation, store timestamp)
            return result
        
        return None
    
    def _cache_result(self, tool_name: str, arguments: Optional[Dict[str, Any]], result: ToolResult) -> None:
        """Cache a tool result."""
        cache_key = self._make_cache_key(tool_name, arguments)
        self._result_cache[cache_key] = result
        
        # Simple cache size management
        if len(self._result_cache) > 1000:
            # Remove oldest entries (simplified)
            keys_to_remove = list(self._result_cache.keys())[:100]
            for key in keys_to_remove:
                del self._result_cache[key]
    
    def _make_cache_key(self, tool_name: str, arguments: Optional[Dict[str, Any]]) -> str:
        """Create a cache key for tool call."""
        import hashlib
        import json
        
        key_data = {
            "tool": tool_name,
            "args": arguments or {}
        }
        
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _update_tool_stats(self, tool_metadata: ToolMetadata, result: ToolResult) -> None:
        """Update performance statistics for a tool."""
        tool_name = tool_metadata.name
        
        if tool_name not in self._performance_stats:
            self._performance_stats[tool_name] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0,
                "average_execution_time": 0.0,
                "last_used": None
            }
        
        stats = self._performance_stats[tool_name]
        stats["total_calls"] += 1
        stats["last_used"] = datetime.now()
        
        if result.success:
            stats["successful_calls"] += 1
        else:
            stats["failed_calls"] += 1
        
        # Update average execution time
        current_avg = stats["average_execution_time"]
        total_calls = stats["total_calls"]
        stats["average_execution_time"] = (current_avg * (total_calls - 1) + result.execution_time) / total_calls
        
        # Update tool metadata
        tool_metadata.usage_count += 1
        tool_metadata.last_used = datetime.now()
        tool_metadata.success_rate = stats["successful_calls"] / stats["total_calls"]
    
    async def _maybe_rediscover_tools(self) -> None:
        """Rediscover tools if enough time has passed."""
        if datetime.now() - self._last_discovery > self._discovery_interval:
            await self.discover_tools()
