"""
Enhanced MCP (Model Context Protocol) integration for the agent framework.

This module provides comprehensive MCP client functionality following the
latest MCP 2025-06-18 specification, including:
- Server discovery and connection management
- Error handling and retry mechanisms
- Resource, prompt, and tool management
- Multi-server coordination
"""

from .client import MCPClient
from .connection_manager import MCPConnectionManager
from .server_discovery import MCPServerDiscovery
from .types import MCPServerInfo, MCPConnectionStatus, MCPError

__all__ = [
    "MCPClient",
    "MCPConnectionManager", 
    "MCPServerDiscovery",
    "MCPServerInfo",
    "MCPConnectionStatus",
    "MCPError"
]
