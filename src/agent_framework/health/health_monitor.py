"""
Comprehensive health monitoring and readiness probes for the agent framework.

Provides health checks for all system components, dependency monitoring,
and Kubernetes-compatible health endpoints.
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    duration_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class HealthCheckConfig:
    """Configuration for a health check."""
    name: str
    check_function: Callable
    timeout_seconds: float = 30.0
    interval_seconds: float = 60.0
    critical: bool = True
    enabled: bool = True
    retry_count: int = 3
    retry_delay: float = 1.0


class HealthMonitor:
    """
    Comprehensive health monitoring system.
    
    Features:
    - Component health checks (database, cache, external services)
    - System resource monitoring
    - Dependency health tracking
    - Kubernetes-compatible endpoints (/health, /ready)
    - Configurable health check intervals
    - Circuit breaker integration
    - Health history and trends
    """
    
    def __init__(self, 
                 check_interval: float = 30.0,
                 history_size: int = 100):
        """Initialize the health monitor."""
        self.logger = logging.getLogger(__name__)
        self.check_interval = check_interval
        self.history_size = history_size
        
        # Health checks registry
        self._health_checks: Dict[str, HealthCheckConfig] = {}
        
        # Health state
        self._health_results: Dict[str, HealthCheckResult] = {}
        self._health_history: List[Dict[str, Any]] = []
        self._overall_status = HealthStatus.UNKNOWN
        
        # Monitoring state
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # System thresholds
        self._system_thresholds = {
            'cpu_percent_warning': 80.0,
            'cpu_percent_critical': 95.0,
            'memory_percent_warning': 80.0,
            'memory_percent_critical': 95.0,
            'disk_percent_warning': 80.0,
            'disk_percent_critical': 95.0,
            'load_average_warning': 2.0,
            'load_average_critical': 5.0
        }
        
        # Initialize default health checks
        self._register_default_checks()
    
    def _register_default_checks(self) -> None:
        """Register default system health checks."""
        # System resource checks
        self.register_health_check(HealthCheckConfig(
            name="system_resources",
            check_function=self._check_system_resources,
            timeout_seconds=10.0,
            interval_seconds=30.0,
            critical=True
        ))
        
        # Memory check
        self.register_health_check(HealthCheckConfig(
            name="memory_usage",
            check_function=self._check_memory_usage,
            timeout_seconds=5.0,
            interval_seconds=30.0,
            critical=True
        ))
        
        # Disk space check
        self.register_health_check(HealthCheckConfig(
            name="disk_space",
            check_function=self._check_disk_space,
            timeout_seconds=5.0,
            interval_seconds=60.0,
            critical=True
        ))
    
    def register_health_check(self, config: HealthCheckConfig) -> None:
        """Register a health check."""
        self._health_checks[config.name] = config
        self.logger.info(f"Registered health check: {config.name}")
    
    def unregister_health_check(self, name: str) -> bool:
        """Unregister a health check."""
        if name in self._health_checks:
            del self._health_checks[name]
            if name in self._health_results:
                del self._health_results[name]
            self.logger.info(f"Unregistered health check: {name}")
            return True
        return False
    
    async def start_monitoring(self) -> None:
        """Start health monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Health monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop health monitoring."""
        if not self._monitoring:
            return
        
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Health monitoring stopped")
    
    async def _monitoring_loop(self) -> None:
        """Main health monitoring loop."""
        while self._monitoring:
            try:
                await self._run_health_checks()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _run_health_checks(self) -> None:
        """Run all registered health checks."""
        tasks = []
        
        for name, config in self._health_checks.items():
            if config.enabled:
                task = asyncio.create_task(self._run_single_check(config))
                tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, HealthCheckResult):
                    self._health_results[result.name] = result
                elif isinstance(result, Exception):
                    self.logger.error(f"Health check failed: {result}")
        
        # Update overall status
        self._update_overall_status()
        
        # Store in history
        self._store_health_snapshot()
    
    async def _run_single_check(self, config: HealthCheckConfig) -> HealthCheckResult:
        """Run a single health check with retries."""
        for attempt in range(config.retry_count):
            try:
                start_time = time.time()
                
                # Run check with timeout
                result = await asyncio.wait_for(
                    config.check_function(),
                    timeout=config.timeout_seconds
                )
                
                duration_ms = (time.time() - start_time) * 1000
                
                if isinstance(result, HealthCheckResult):
                    result.duration_ms = duration_ms
                    return result
                else:
                    # Convert simple result to HealthCheckResult
                    return HealthCheckResult(
                        name=config.name,
                        status=HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY,
                        duration_ms=duration_ms
                    )
                
            except asyncio.TimeoutError:
                if attempt < config.retry_count - 1:
                    await asyncio.sleep(config.retry_delay)
                    continue
                
                return HealthCheckResult(
                    name=config.name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check timed out after {config.timeout_seconds}s"
                )
            
            except Exception as e:
                if attempt < config.retry_count - 1:
                    await asyncio.sleep(config.retry_delay)
                    continue
                
                return HealthCheckResult(
                    name=config.name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}"
                )
        
        # Should not reach here
        return HealthCheckResult(
            name=config.name,
            status=HealthStatus.UNKNOWN,
            message="Unknown health check error"
        )
    
    def _update_overall_status(self) -> None:
        """Update overall system health status."""
        if not self._health_results:
            self._overall_status = HealthStatus.UNKNOWN
            return
        
        critical_checks = [
            result for name, result in self._health_results.items()
            if self._health_checks.get(name, {}).critical
        ]
        
        # If any critical check is unhealthy, system is unhealthy
        if any(check.status == HealthStatus.UNHEALTHY for check in critical_checks):
            self._overall_status = HealthStatus.UNHEALTHY
        # If any critical check is degraded, system is degraded
        elif any(check.status == HealthStatus.DEGRADED for check in critical_checks):
            self._overall_status = HealthStatus.DEGRADED
        # If all critical checks are healthy, system is healthy
        elif all(check.status == HealthStatus.HEALTHY for check in critical_checks):
            self._overall_status = HealthStatus.HEALTHY
        else:
            self._overall_status = HealthStatus.UNKNOWN
    
    def _store_health_snapshot(self) -> None:
        """Store current health state in history."""
        snapshot = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': self._overall_status.value,
            'checks': {
                name: result.to_dict() 
                for name, result in self._health_results.items()
            }
        }
        
        self._health_history.append(snapshot)
        
        # Maintain history size
        if len(self._health_history) > self.history_size:
            self._health_history.pop(0)
    
    async def _check_system_resources(self) -> HealthCheckResult:
        """Check overall system resource health."""
        if not PSUTIL_AVAILABLE:
            return HealthCheckResult(
                name="system_resources",
                status=HealthStatus.UNKNOWN,
                message="psutil not available"
            )
        
        try:
            issues = []
            details = {}
            
            # CPU check
            cpu_percent = psutil.cpu_percent(interval=1)
            details['cpu_percent'] = cpu_percent
            
            if cpu_percent > self._system_thresholds['cpu_percent_critical']:
                issues.append(f"CPU usage critical: {cpu_percent:.1f}%")
            elif cpu_percent > self._system_thresholds['cpu_percent_warning']:
                issues.append(f"CPU usage high: {cpu_percent:.1f}%")
            
            # Load average (Unix only)
            try:
                load_avg = psutil.getloadavg()
                details['load_average_1m'] = load_avg[0]
                
                if load_avg[0] > self._system_thresholds['load_average_critical']:
                    issues.append(f"Load average critical: {load_avg[0]:.2f}")
                elif load_avg[0] > self._system_thresholds['load_average_warning']:
                    issues.append(f"Load average high: {load_avg[0]:.2f}")
            except (AttributeError, OSError):
                pass
            
            # Determine status
            if any("critical" in issue for issue in issues):
                status = HealthStatus.UNHEALTHY
            elif issues:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.HEALTHY
            
            return HealthCheckResult(
                name="system_resources",
                status=status,
                message="; ".join(issues) if issues else "System resources healthy",
                details=details
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="system_resources",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check system resources: {e}"
            )
    
    async def _check_memory_usage(self) -> HealthCheckResult:
        """Check memory usage."""
        if not PSUTIL_AVAILABLE:
            return HealthCheckResult(
                name="memory_usage",
                status=HealthStatus.UNKNOWN,
                message="psutil not available"
            )
        
        try:
            memory = psutil.virtual_memory()
            details = {
                'percent': memory.percent,
                'used_bytes': memory.used,
                'available_bytes': memory.available,
                'total_bytes': memory.total
            }
            
            if memory.percent > self._system_thresholds['memory_percent_critical']:
                status = HealthStatus.UNHEALTHY
                message = f"Memory usage critical: {memory.percent:.1f}%"
            elif memory.percent > self._system_thresholds['memory_percent_warning']:
                status = HealthStatus.DEGRADED
                message = f"Memory usage high: {memory.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Memory usage normal: {memory.percent:.1f}%"
            
            return HealthCheckResult(
                name="memory_usage",
                status=status,
                message=message,
                details=details
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="memory_usage",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check memory usage: {e}"
            )
    
    async def _check_disk_space(self) -> HealthCheckResult:
        """Check disk space usage."""
        if not PSUTIL_AVAILABLE:
            return HealthCheckResult(
                name="disk_space",
                status=HealthStatus.UNKNOWN,
                message="psutil not available"
            )
        
        try:
            disk = psutil.disk_usage('/')
            details = {
                'percent': disk.percent,
                'used_bytes': disk.used,
                'free_bytes': disk.free,
                'total_bytes': disk.total
            }
            
            if disk.percent > self._system_thresholds['disk_percent_critical']:
                status = HealthStatus.UNHEALTHY
                message = f"Disk usage critical: {disk.percent:.1f}%"
            elif disk.percent > self._system_thresholds['disk_percent_warning']:
                status = HealthStatus.DEGRADED
                message = f"Disk usage high: {disk.percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"Disk usage normal: {disk.percent:.1f}%"
            
            return HealthCheckResult(
                name="disk_space",
                status=status,
                message=message,
                details=details
            )
            
        except Exception as e:
            return HealthCheckResult(
                name="disk_space",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check disk space: {e}"
            )
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current health status."""
        # Run immediate health check if no recent results
        if not self._health_results:
            await self._run_health_checks()
        
        return {
            'status': self._overall_status.value,
            'timestamp': datetime.now().isoformat(),
            'checks': {
                name: result.to_dict() 
                for name, result in self._health_results.items()
            }
        }
    
    async def get_readiness_status(self) -> Dict[str, Any]:
        """Get readiness status (for Kubernetes readiness probes)."""
        # Readiness is more strict - all critical components must be healthy
        critical_results = [
            result for name, result in self._health_results.items()
            if self._health_checks.get(name, {}).critical
        ]
        
        ready = all(
            result.status == HealthStatus.HEALTHY 
            for result in critical_results
        ) if critical_results else False
        
        return {
            'ready': ready,
            'status': 'ready' if ready else 'not_ready',
            'timestamp': datetime.now().isoformat(),
            'critical_checks': {
                result.name: result.status.value 
                for result in critical_results
            }
        }
    
    def get_health_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get health check history."""
        return self._health_history[-limit:]
    
    def set_system_thresholds(self, thresholds: Dict[str, float]) -> None:
        """Update system health thresholds."""
        self._system_thresholds.update(thresholds)
        self.logger.info(f"Updated system thresholds: {thresholds}")
    
    def get_system_thresholds(self) -> Dict[str, float]:
        """Get current system thresholds."""
        return dict(self._system_thresholds)
