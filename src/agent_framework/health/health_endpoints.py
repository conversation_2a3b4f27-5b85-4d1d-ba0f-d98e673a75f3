"""
Health check endpoints for web integration.

Provides FastAPI-compatible health and readiness endpoints
for Kubernetes and load balancer integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

try:
    from fastapi import FastAPI, Response, HTTPException, Depends
    from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse, PlainTextResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from .health_monitor import HealthMonitor, HealthStatus


class HealthEndpoints:
    """
    Health check endpoints for web integration.
    
    Provides Kubernetes-compatible health endpoints:
    - /health - Liveness probe (is the service running?)
    - /ready - Readiness probe (is the service ready to serve traffic?)
    - /health/detailed - Detailed health information
    - /metrics - Prometheus metrics endpoint
    """
    
    def __init__(self, health_monitor: HealthMonitor):
        """Initialize health endpoints."""
        self.health_monitor = health_monitor
        self.logger = logging.getLogger(__name__)
    
    def register_routes(self, app: 'FastAPI') -> None:
        """Register health routes with FastAPI app."""
        if not FASTAPI_AVAILABLE:
            self.logger.warning("FastAPI not available - health endpoints disabled")
            return
        
        # Liveness probe - basic health check
        @app.get("/health", 
                response_class=JSONResponse,
                tags=["health"],
                summary="Liveness probe",
                description="Check if the service is alive and running")
        async def health_check():
            """Basic health check endpoint for liveness probes."""
            try:
                health_status = await self.health_monitor.get_health_status()
                
                # For liveness, we're more lenient - degraded is still alive
                is_alive = health_status['status'] in ['healthy', 'degraded']
                
                if is_alive:
                    return JSONResponse(
                        status_code=200,
                        content={
                            "status": "ok",
                            "timestamp": datetime.now().isoformat(),
                            "service": "agent-framework"
                        }
                    )
                else:
                    return JSONResponse(
                        status_code=503,
                        content={
                            "status": "unhealthy",
                            "timestamp": datetime.now().isoformat(),
                            "service": "agent-framework"
                        }
                    )
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                return JSONResponse(
                    status_code=503,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        # Readiness probe - strict health check
        @app.get("/ready",
                response_class=JSONResponse,
                tags=["health"],
                summary="Readiness probe", 
                description="Check if the service is ready to serve traffic")
        async def readiness_check():
            """Readiness check endpoint for readiness probes."""
            try:
                readiness_status = await self.health_monitor.get_readiness_status()
                
                if readiness_status['ready']:
                    return JSONResponse(
                        status_code=200,
                        content={
                            "status": "ready",
                            "timestamp": datetime.now().isoformat(),
                            "service": "agent-framework"
                        }
                    )
                else:
                    return JSONResponse(
                        status_code=503,
                        content={
                            "status": "not_ready",
                            "timestamp": datetime.now().isoformat(),
                            "critical_checks": readiness_status.get('critical_checks', {}),
                            "service": "agent-framework"
                        }
                    )
            except Exception as e:
                self.logger.error(f"Readiness check failed: {e}")
                return JSONResponse(
                    status_code=503,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        # Detailed health information
        @app.get("/health/detailed",
                response_class=JSONResponse,
                tags=["health"],
                summary="Detailed health information",
                description="Get detailed health information for all components")
        async def detailed_health():
            """Detailed health information endpoint."""
            try:
                health_status = await self.health_monitor.get_health_status()
                return JSONResponse(
                    status_code=200,
                    content=health_status
                )
            except Exception as e:
                self.logger.error(f"Detailed health check failed: {e}")
                return JSONResponse(
                    status_code=500,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        # Health history
        @app.get("/health/history",
                response_class=JSONResponse,
                tags=["health"],
                summary="Health check history",
                description="Get recent health check history")
        async def health_history(limit: int = 50):
            """Health check history endpoint."""
            try:
                history = self.health_monitor.get_health_history(limit)
                return JSONResponse(
                    status_code=200,
                    content={
                        "history": history,
                        "count": len(history),
                        "timestamp": datetime.now().isoformat()
                    }
                )
            except Exception as e:
                self.logger.error(f"Health history failed: {e}")
                return JSONResponse(
                    status_code=500,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        # System thresholds
        @app.get("/health/thresholds",
                response_class=JSONResponse,
                tags=["health"],
                summary="System health thresholds",
                description="Get current system health thresholds")
        async def get_thresholds():
            """Get system health thresholds."""
            try:
                thresholds = self.health_monitor.get_system_thresholds()
                return JSONResponse(
                    status_code=200,
                    content={
                        "thresholds": thresholds,
                        "timestamp": datetime.now().isoformat()
                    }
                )
            except Exception as e:
                return JSONResponse(
                    status_code=500,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        # Update system thresholds
        @app.put("/health/thresholds",
                response_class=JSONResponse,
                tags=["health"],
                summary="Update system health thresholds",
                description="Update system health monitoring thresholds")
        async def update_thresholds(thresholds: Dict[str, float]):
            """Update system health thresholds."""
            try:
                self.health_monitor.set_system_thresholds(thresholds)
                return JSONResponse(
                    status_code=200,
                    content={
                        "status": "updated",
                        "thresholds": self.health_monitor.get_system_thresholds(),
                        "timestamp": datetime.now().isoformat()
                    }
                )
            except Exception as e:
                return JSONResponse(
                    status_code=500,
                    content={
                        "status": "error",
                        "message": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                )
        
        self.logger.info("Health endpoints registered")
    
    async def create_database_health_check(self, database_pool) -> None:
        """Create a health check for database connectivity."""
        from .health_monitor import HealthCheckConfig, HealthCheckResult, HealthStatus
        
        async def check_database():
            """Check database connectivity."""
            try:
                # Test database connection
                async with database_pool.get_connection() as conn:
                    if hasattr(conn, 'fetchval'):
                        # PostgreSQL
                        result = await conn.fetchval("SELECT 1")
                    else:
                        # SQLite
                        cursor = await conn.execute("SELECT 1")
                        result = await cursor.fetchone()
                
                return HealthCheckResult(
                    name="database",
                    status=HealthStatus.HEALTHY,
                    message="Database connection healthy",
                    details={"connection_test": "passed"}
                )
            except Exception as e:
                return HealthCheckResult(
                    name="database",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Database connection failed: {str(e)}",
                    details={"error": str(e)}
                )
        
        config = HealthCheckConfig(
            name="database",
            check_function=check_database,
            timeout_seconds=10.0,
            interval_seconds=30.0,
            critical=True
        )
        
        self.health_monitor.register_health_check(config)
    
    async def create_cache_health_check(self, cache_manager) -> None:
        """Create a health check for cache system."""
        from .health_monitor import HealthCheckConfig, HealthCheckResult, HealthStatus
        
        async def check_cache():
            """Check cache system health."""
            try:
                # Test cache operations
                test_key = "__health_check__"
                test_value = "test"
                
                # Test set operation
                await cache_manager.set(test_key, test_value, ttl=10)
                
                # Test get operation
                retrieved_value = await cache_manager.get(test_key)
                
                if retrieved_value == test_value:
                    # Clean up
                    await cache_manager.delete(test_key)
                    
                    return HealthCheckResult(
                        name="cache",
                        status=HealthStatus.HEALTHY,
                        message="Cache system healthy",
                        details={"operations_test": "passed"}
                    )
                else:
                    return HealthCheckResult(
                        name="cache",
                        status=HealthStatus.UNHEALTHY,
                        message="Cache operations failed",
                        details={"expected": test_value, "actual": retrieved_value}
                    )
            except Exception as e:
                return HealthCheckResult(
                    name="cache",
                    status=HealthStatus.UNHEALTHY,
                    message=f"Cache system failed: {str(e)}",
                    details={"error": str(e)}
                )
        
        config = HealthCheckConfig(
            name="cache",
            check_function=check_cache,
            timeout_seconds=5.0,
            interval_seconds=60.0,
            critical=False
        )
        
        self.health_monitor.register_health_check(config)
    
    async def create_external_service_health_check(self, 
                                                  service_name: str,
                                                  health_url: str,
                                                  timeout: float = 10.0) -> None:
        """Create a health check for external service."""
        from .health_monitor import HealthCheckConfig, HealthCheckResult, HealthStatus
        import aiohttp
        
        async def check_external_service():
            """Check external service health."""
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(health_url, timeout=timeout) as response:
                        if response.status == 200:
                            return HealthCheckResult(
                                name=f"external_service_{service_name}",
                                status=HealthStatus.HEALTHY,
                                message=f"External service {service_name} healthy",
                                details={"status_code": response.status, "url": health_url}
                            )
                        else:
                            return HealthCheckResult(
                                name=f"external_service_{service_name}",
                                status=HealthStatus.DEGRADED,
                                message=f"External service {service_name} returned {response.status}",
                                details={"status_code": response.status, "url": health_url}
                            )
            except Exception as e:
                return HealthCheckResult(
                    name=f"external_service_{service_name}",
                    status=HealthStatus.UNHEALTHY,
                    message=f"External service {service_name} failed: {str(e)}",
                    details={"error": str(e), "url": health_url}
                )
        
        config = HealthCheckConfig(
            name=f"external_service_{service_name}",
            check_function=check_external_service,
            timeout_seconds=timeout + 5.0,
            interval_seconds=60.0,
            critical=False
        )
        
        self.health_monitor.register_health_check(config)
