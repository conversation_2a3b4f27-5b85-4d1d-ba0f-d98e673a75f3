#!/usr/bin/env python3
"""
Integration test script for all advanced capabilities.
Tests the complete advanced agent framework with all improvements.
"""

import sys
import os
import asyncio
import tempfile
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/agent-test')

try:
    from agent_framework.core.agent_orchestrator import (
        AdvancedAgentOrchestrator, AgentCapabilities
    )
    from agent_framework.core.automatic_bug_fix_loop import AutomaticBugF<PERSON>Loop
    from agent_framework.core.automatic_evaluation_cycles import AutomaticEvaluationCycles
    from agent_framework.cli.commands.enhance import EnhanceCommand
    from agent_framework.cli.commands.debug import DebugCommand
    from agent_framework.cli.commands.analyze import AnalyzeCommand
    
    print("✓ All imports successful")
    
    async def test_advanced_integration():
        """Test the complete advanced agent framework integration."""
        print("\n=== Testing Advanced Agent Framework Integration ===")
        
        # Test sample code with various issues
        test_code = '''
def calculate_something(a, b, c, d, e, f):
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:
                        result = a * b + c - d / e
                        return result + f
    return 0

class DataProcessor:
    def process_data(self, data):
        processed = []
        for item in data:
            if item:
                processed.append(item * 2)
        return processed
    
    def validate_input(self, input_data):
        return input_data is not None
    
    def transform_data(self, data):
        return [x for x in data if x > 0]
    
    def filter_data(self, data, threshold):
        return [x for x in data if x > threshold]
    
    def sort_data(self, data):
        return sorted(data)
    
    def aggregate_data(self, data):
        return sum(data) / len(data) if data else 0
'''
        
        print("\n🔧 Testing Advanced Agent Orchestrator...")

        # Test 1: Advanced Agent Orchestrator
        capabilities = AgentCapabilities(
            enable_advanced_code_generation=True,
            enable_comprehensive_testing=True,
            enable_automatic_bug_fixing=True,
            enable_automatic_evaluation=True,
            max_fix_iterations=2,
            evaluation_on_every_change=True,
            rollback_on_critical_issues=True
        )

        orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
        print("✓ Advanced orchestrator created with all capabilities")
        
        # Test full advanced cycle
        requirements = {
            "type": "enhancement",
            "goals": ["quality", "maintainability", "performance"],
            "description": "Comprehensive enhancement focusing on code quality and maintainability"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_code)
            temp_file = f.name
        
        try:
            print("\n🚀 Running full advanced cycle...")
            enhancement_results = await orchestrator.run_full_advanced_cycle(
                code_content=test_code,
                file_path=temp_file,
                requirements=requirements
            )

            print(f"✓ Advanced cycle completed")
            print(f"✓ Success: {enhancement_results.get('success', False)}")
            
            if enhancement_results.get('success'):
                result = enhancement_results.get('result', {})
                print(f"✓ Advanced code generated: {len(result.get('advanced_code', '')) > 0}")
                print(f"✓ Analysis performed: {'analysis_results' in result}")
                print(f"✓ Bug fixing attempted: {'bug_fix_results' in result}")
                print(f"✓ Evaluation completed: {'evaluation_results' in result}")
                
                # Check evaluation results
                if 'evaluation_results' in result:
                    eval_result = result['evaluation_results']
                    print(f"  - Overall quality: {eval_result.get('overall_quality', 'N/A')}")
                    print(f"  - Overall score: {eval_result.get('overall_score', 0):.2f}")
                    print(f"  - Evaluations run: {len(eval_result.get('evaluations', {}))}")
            
            print("\n🔍 Testing Automatic Bug Fix Loop...")
            
            # Test 2: Automatic Bug Fix Loop
            bug_fix_loop = AutomaticBugFixLoop()
            
            buggy_code = '''
def divide_numbers(a, b):
    return a / b  # Division by zero possible

def process_list(items):
    result = []
    for i in range(len(items) + 1):  # Index out of bounds
        result.append(items[i])
    return result
'''
            
            # Create a mock error for testing
            mock_error = Exception("Integration test - buggy code")
            fix_results = await bug_fix_loop.start_fix_loop(
                error=mock_error,
                code_content=buggy_code,
                file_path="buggy_test.py"
            )
            
            print(f"✓ Bug fix session completed")
            print(f"✓ Final status: {fix_results.final_status}")
            print(f"✓ Fix attempts: {len(fix_results.fix_attempts)}")
            print(f"✓ Success rate: {fix_results.success_rate:.2f}")
            print(f"✓ Total time: {fix_results.total_time:.2f}s")
            
            print("\n📊 Testing Automatic Evaluation Cycles...")
            
            # Test 3: Automatic Evaluation Cycles
            evaluation_cycles = AutomaticEvaluationCycles(enable_advanced_features=True)
            
            eval_cycle = await evaluation_cycles.run_evaluation_cycle(
                code_content=test_code,
                file_path=temp_file,
                evaluation_types=["static_analysis", "code_quality", "maintainability_analysis", "documentation_quality"]
            )
            
            print(f"✓ Evaluation cycle completed: {eval_cycle.cycle_id}")
            print(f"✓ Overall quality: {eval_cycle.overall_quality.value}")
            print(f"✓ Overall score: {eval_cycle.overall_score:.2f}")
            print(f"✓ Evaluations run: {len(eval_cycle.evaluations)}")
            print(f"✓ Critical issues: {len(eval_cycle.critical_issues)}")
            print(f"✓ Improvement plan items: {len(eval_cycle.improvement_plan)}")
            
            # Test evaluation history and metrics
            history = evaluation_cycles.get_evaluation_history()
            metrics = evaluation_cycles.get_comprehensive_metrics()
            trends = evaluation_cycles.get_quality_trends()
            
            print(f"✓ Evaluation history: {len(history)} cycles")
            print(f"✓ Performance metrics available: {metrics.get('total_evaluations', 0)} total")
            print(f"✓ Quality trends: {trends.get('trend', 'N/A')}")
            
            print("\n🎯 Testing CLI Command Integration...")
            
            # Test 4: CLI Commands Integration
            # Note: We can't easily test the full CLI without mocking argparse,
            # but we can test the command classes directly
            
            enhance_cmd = EnhanceCommand()
            debug_cmd = DebugCommand()
            analyze_cmd = AnalyzeCommand()
            
            print(f"✓ Enhance command: {enhance_cmd.name} - {enhance_cmd.description}")
            print(f"✓ Debug command: {debug_cmd.name} - {debug_cmd.description}")
            print(f"✓ Analyze command: {analyze_cmd.name} - {analyze_cmd.description}")
            
            # Test help text generation
            enhance_help = enhance_cmd.get_help_text()
            debug_help = debug_cmd.get_help_text()
            analyze_help = analyze_cmd.get_help_text()
            
            print(f"✓ Help text generated for all commands")
            print(f"  - Enhance help length: {len(enhance_help)} chars")
            print(f"  - Debug help length: {len(debug_help)} chars")
            print(f"  - Analyze help length: {len(analyze_help)} chars")
            
            # Verify advanced features are mentioned in help
            assert "comprehensive" in enhance_help.lower()
            assert "auto-fix" in debug_help.lower()
            assert "evaluation" in analyze_help.lower()
            
            print("\n🔗 Testing Component Integration...")
            
            # Test 5: Component Integration
            # Verify that all components can work together
            
            # Test orchestrator status
            status = await orchestrator.get_advanced_status()
            print(f"✓ Orchestrator status available")
            print(f"  - Capabilities enabled: {len([k for k, v in status.get('capabilities', {}).items() if v])}")
            print(f"  - Components initialized: {len(status.get('components', {}))}")
            
            # Test performance metrics
            bug_fix_metrics = bug_fix_loop.get_performance_metrics()
            eval_metrics = evaluation_cycles.get_comprehensive_metrics()
            
            print(f"✓ Performance metrics collected")
            print(f"  - Bug fix sessions: {bug_fix_metrics.get('total_fix_sessions', 0)}")
            print(f"  - Evaluation cycles: {eval_metrics.get('total_evaluations', 0)}")
            
            print("\n=== All Advanced Integration Tests Passed! ===")
            return True
            
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file)
            except:
                pass
    
    # Run the integration test
    if __name__ == "__main__":
        result = asyncio.run(test_advanced_integration())
        if result:
            print("\n🎉 Advanced Agent Framework Integration is working correctly!")
            print("\n📋 Integration Summary:")
            print("  • Advanced Agent Orchestrator with full capability support")
            print("  • Automatic Bug Fix Loop with iterative debugging")
            print("  • Automatic Evaluation Cycles with comprehensive analysis")
            print("  • CLI Commands with advanced features integration")
            print("  • Component interoperability and performance tracking")
            print("  • Comprehensive error handling and rollback capabilities")
            print("  • Advanced logging and metrics collection")
            print("  • Quality assessment and improvement recommendations")
            sys.exit(0)
        else:
            print("\n❌ Integration tests failed")
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Note: Some dependencies may not be available in the current environment")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
