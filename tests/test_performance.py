"""
Performance and load tests for the enhanced plugin system.

Tests system behavior under load, concurrent operations, and performance
characteristics of plugin loading, function calling, and hot reload operations.
"""

import asyncio
import pytest
import time
import statistics
import tempfile
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import sys

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_framework.core.config import FrameworkConfig
from agent_framework.plugins import (
    PluginManager, FunctionRegistry, ModelIntegration, FunctionCallRequest
)


@pytest.fixture
def performance_config():
    """Create configuration optimized for performance testing."""
    config = FrameworkConfig()
    config.plugins.plugin_directories = ["test_plugins"]
    config.plugins.hot_reload_enabled = True
    config.plugins.function_exposure_enabled = True
    config.plugins.function_cache_ttl = 60  # Shorter cache for testing
    return config


@pytest.fixture
def temp_plugin_dir():
    """Create temporary directory for performance test plugins."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


def create_test_plugin(plugin_dir: Path, plugin_name: str, function_count: int = 5):
    """Create a test plugin with specified number of functions."""
    plugin_file = plugin_dir / f"{plugin_name}.py"
    
    plugin_code = f'''
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, int_param, returns
from agent_framework.plugins.function_registry import ParameterType
import asyncio

class {plugin_name.title()}Plugin(EnhancedPlugin):
    PLUGIN_NAME = "{plugin_name}"
    
    @property
    def name(self):
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config):
        pass
    
    async def _cleanup_plugin(self):
        pass
    
    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="test")
'''
    
    # Add multiple functions
    for i in range(function_count):
        plugin_code += f'''
    
    @exposed_function(description="Test function {i}")
    @string_param("input", "Input parameter")
    @returns(ParameterType.STRING, "Output")
    async def function_{i}(self, input: str) -> str:
        await asyncio.sleep(0.001)  # Simulate small processing time
        return f"result_{i}_{input}"
'''
    
    plugin_file.write_text(plugin_code)
    return plugin_file


class TestPluginLoadingPerformance:
    """Test performance of plugin loading operations."""
    
    @pytest.mark.asyncio
    async def test_single_plugin_loading_time(self, performance_config, temp_plugin_dir):
        """Test time to load a single plugin."""
        # Create test plugin
        create_test_plugin(temp_plugin_dir, "perf_test", function_count=10)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        # Measure loading time
        start_time = time.time()
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        loading_time = time.time() - start_time
        
        try:
            # Verify plugin loaded
            status = await plugin_manager.get_plugin_status()
            assert status['loaded_count'] > 0
            
            # Loading should be reasonably fast (under 5 seconds)
            assert loading_time < 5.0, f"Plugin loading took {loading_time:.2f}s, expected < 5.0s"
            
            print(f"Single plugin loading time: {loading_time:.3f}s")
            
        finally:
            await plugin_manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_multiple_plugins_loading_time(self, performance_config, temp_plugin_dir):
        """Test time to load multiple plugins."""
        # Create multiple test plugins
        plugin_count = 10
        for i in range(plugin_count):
            create_test_plugin(temp_plugin_dir, f"perf_test_{i}", function_count=5)
        
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        # Measure loading time
        start_time = time.time()
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        loading_time = time.time() - start_time
        
        try:
            # Verify plugins loaded
            status = await plugin_manager.get_plugin_status()
            assert status['loaded_count'] >= plugin_count
            
            # Loading should scale reasonably (under 10 seconds for 10 plugins)
            assert loading_time < 10.0, f"Multiple plugin loading took {loading_time:.2f}s, expected < 10.0s"
            
            print(f"Multiple plugins ({plugin_count}) loading time: {loading_time:.3f}s")
            print(f"Average per plugin: {loading_time/plugin_count:.3f}s")
            
        finally:
            await plugin_manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_function_discovery_performance(self, performance_config, temp_plugin_dir):
        """Test performance of function discovery."""
        # Create plugin with many functions
        create_test_plugin(temp_plugin_dir, "many_functions", function_count=50)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            # Measure function discovery time
            start_time = time.time()
            functions = plugin_manager.get_available_functions()
            discovery_time = time.time() - start_time
            
            assert len(functions) >= 50
            assert discovery_time < 1.0, f"Function discovery took {discovery_time:.2f}s, expected < 1.0s"
            
            print(f"Function discovery time for {len(functions)} functions: {discovery_time:.3f}s")
            
        finally:
            await plugin_manager.shutdown()


class TestFunctionCallPerformance:
    """Test performance of function calling operations."""
    
    @pytest.mark.asyncio
    async def test_single_function_call_latency(self, performance_config, temp_plugin_dir):
        """Test latency of single function calls."""
        create_test_plugin(temp_plugin_dir, "latency_test", function_count=1)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            model_integration = ModelIntegration(plugin_manager)
            functions = model_integration.get_available_functions()
            
            if functions:
                test_function = functions[0]
                
                # Measure multiple calls
                call_times = []
                for _ in range(100):
                    request = FunctionCallRequest(
                        function_name=test_function.name,
                        arguments={"input": "test"}
                    )
                    
                    start_time = time.time()
                    result = await model_integration.call_function(request)
                    call_time = time.time() - start_time
                    
                    assert result.success is True
                    call_times.append(call_time)
                
                # Analyze performance
                avg_time = statistics.mean(call_times)
                median_time = statistics.median(call_times)
                p95_time = sorted(call_times)[int(len(call_times) * 0.95)]
                
                print(f"Function call performance (100 calls):")
                print(f"  Average: {avg_time*1000:.2f}ms")
                print(f"  Median: {median_time*1000:.2f}ms")
                print(f"  95th percentile: {p95_time*1000:.2f}ms")
                
                # Performance assertions
                assert avg_time < 0.1, f"Average call time {avg_time:.3f}s too high"
                assert p95_time < 0.2, f"95th percentile {p95_time:.3f}s too high"
                
        finally:
            await plugin_manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_concurrent_function_calls(self, performance_config, temp_plugin_dir):
        """Test performance under concurrent function calls."""
        create_test_plugin(temp_plugin_dir, "concurrent_test", function_count=5)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            model_integration = ModelIntegration(plugin_manager)
            functions = model_integration.get_available_functions()
            
            if functions:
                test_function = functions[0]
                
                # Test concurrent calls
                concurrent_calls = 50
                
                async def make_call(call_id):
                    request = FunctionCallRequest(
                        function_name=test_function.name,
                        arguments={"input": f"test_{call_id}"}
                    )
                    start_time = time.time()
                    result = await model_integration.call_function(request)
                    call_time = time.time() - start_time
                    return result.success, call_time
                
                # Execute concurrent calls
                start_time = time.time()
                tasks = [make_call(i) for i in range(concurrent_calls)]
                results = await asyncio.gather(*tasks)
                total_time = time.time() - start_time
                
                # Analyze results
                successful_calls = sum(1 for success, _ in results if success)
                call_times = [call_time for _, call_time in results]
                
                avg_call_time = statistics.mean(call_times)
                throughput = successful_calls / total_time
                
                print(f"Concurrent calls performance ({concurrent_calls} calls):")
                print(f"  Total time: {total_time:.3f}s")
                print(f"  Successful calls: {successful_calls}/{concurrent_calls}")
                print(f"  Average call time: {avg_call_time*1000:.2f}ms")
                print(f"  Throughput: {throughput:.1f} calls/second")
                
                # Performance assertions
                assert successful_calls == concurrent_calls, "Some calls failed"
                assert throughput > 10, f"Throughput {throughput:.1f} calls/s too low"
                
        finally:
            await plugin_manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_function_call_scaling(self, performance_config, temp_plugin_dir):
        """Test how function call performance scales with load."""
        create_test_plugin(temp_plugin_dir, "scaling_test", function_count=1)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            model_integration = ModelIntegration(plugin_manager)
            functions = model_integration.get_available_functions()
            
            if functions:
                test_function = functions[0]
                
                # Test different load levels
                load_levels = [1, 5, 10, 20, 50]
                results = {}
                
                for load in load_levels:
                    async def make_call():
                        request = FunctionCallRequest(
                            function_name=test_function.name,
                            arguments={"input": "test"}
                        )
                        start_time = time.time()
                        result = await model_integration.call_function(request)
                        return time.time() - start_time
                    
                    # Execute calls at this load level
                    start_time = time.time()
                    tasks = [make_call() for _ in range(load)]
                    call_times = await asyncio.gather(*tasks)
                    total_time = time.time() - start_time
                    
                    avg_call_time = statistics.mean(call_times)
                    throughput = load / total_time
                    
                    results[load] = {
                        'avg_call_time': avg_call_time,
                        'throughput': throughput,
                        'total_time': total_time
                    }
                    
                    print(f"Load {load}: {avg_call_time*1000:.2f}ms avg, {throughput:.1f} calls/s")
                
                # Check that performance doesn't degrade too much with load
                baseline_throughput = results[1]['throughput']
                high_load_throughput = results[50]['throughput']
                
                # Throughput should not drop below 50% of baseline
                throughput_ratio = high_load_throughput / baseline_throughput
                assert throughput_ratio > 0.5, f"Throughput degraded too much: {throughput_ratio:.2f}"
                
        finally:
            await plugin_manager.shutdown()


class TestHotReloadPerformance:
    """Test performance of hot reload operations."""
    
    @pytest.mark.asyncio
    async def test_plugin_reload_time(self, performance_config, temp_plugin_dir):
        """Test time required for plugin reload."""
        plugin_file = create_test_plugin(temp_plugin_dir, "reload_perf", function_count=10)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            # Measure reload time
            start_time = time.time()
            reload_event = await plugin_manager.reload_plugin("reload_perf", force=True)
            reload_time = time.time() - start_time
            
            assert reload_event.success is True
            assert reload_time < 2.0, f"Plugin reload took {reload_time:.2f}s, expected < 2.0s"
            
            print(f"Plugin reload time: {reload_time:.3f}s")
            
        finally:
            await plugin_manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_multiple_reload_performance(self, performance_config, temp_plugin_dir):
        """Test performance of multiple consecutive reloads."""
        create_test_plugin(temp_plugin_dir, "multi_reload", function_count=5)
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            reload_times = []
            
            # Perform multiple reloads
            for i in range(10):
                start_time = time.time()
                reload_event = await plugin_manager.reload_plugin("multi_reload", force=True)
                reload_time = time.time() - start_time
                
                assert reload_event.success is True
                reload_times.append(reload_time)
            
            avg_reload_time = statistics.mean(reload_times)
            max_reload_time = max(reload_times)
            
            print(f"Multiple reloads performance (10 reloads):")
            print(f"  Average: {avg_reload_time:.3f}s")
            print(f"  Maximum: {max_reload_time:.3f}s")
            
            # Performance assertions
            assert avg_reload_time < 1.0, f"Average reload time {avg_reload_time:.3f}s too high"
            assert max_reload_time < 2.0, f"Maximum reload time {max_reload_time:.3f}s too high"
            
        finally:
            await plugin_manager.shutdown()


class TestMemoryPerformance:
    """Test memory usage and performance characteristics."""
    
    @pytest.mark.asyncio
    async def test_memory_usage_scaling(self, performance_config, temp_plugin_dir):
        """Test memory usage with increasing number of plugins."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # Baseline memory
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create multiple plugins
        plugin_count = 20
        for i in range(plugin_count):
            create_test_plugin(temp_plugin_dir, f"memory_test_{i}", function_count=10)
        
        performance_config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(performance_config)
        await plugin_manager.initialize()
        
        try:
            # Measure memory after loading
            loaded_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = loaded_memory - baseline_memory
            
            print(f"Memory usage:")
            print(f"  Baseline: {baseline_memory:.1f} MB")
            print(f"  After loading {plugin_count} plugins: {loaded_memory:.1f} MB")
            print(f"  Increase: {memory_increase:.1f} MB")
            print(f"  Per plugin: {memory_increase/plugin_count:.2f} MB")
            
            # Memory increase should be reasonable (less than 100MB for 20 plugins)
            assert memory_increase < 100, f"Memory increase {memory_increase:.1f}MB too high"
            
        finally:
            await plugin_manager.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
