"""
Tests for shared infrastructure components.

This module tests the shared abstractions and utilities that form
the foundation of the integrated agent framework.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock

from agent_framework.shared.base_service import BaseService, ServiceState
from agent_framework.shared.base_processor import BaseProcessor, ProcessingContext
from agent_framework.shared.base_validator import BaseValidator, ValidationResult, ValidationSeverity
from agent_framework.shared.base_metrics import BaseMetrics, MetricType
from agent_framework.shared.base_cache import BaseCache, CachePolicy
from agent_framework.shared.data_models import (
    UnifiedTask, StandardResult, CapabilityModel, ContextModel,
    TaskPriority, TaskStatus, CapabilityType
)


class MockService(BaseService):
    """Mock service for testing."""
    
    def __init__(self, name: str, config: dict):
        super().__init__(name, config)
        self.initialized = False
        self.shutdown_called = False
    
    async def _initialize_service(self) -> None:
        self.initialized = True
    
    async def _shutdown_service(self) -> None:
        self.shutdown_called = True


class MockProcessor(BaseProcessor[dict, str]):
    """Mock processor for testing."""
    
    def __init__(self, name: str):
        super().__init__(name)
        self.validation_result = True
        self.process_result = "processed"
        self.error_result = None
    
    async def _validate_input(self, input_data: dict, context: ProcessingContext) -> bool:
        return self.validation_result
    
    async def _process_data(self, input_data: dict, context: ProcessingContext) -> str:
        if self.error_result:
            raise self.error_result
        return self.process_result
    
    async def _handle_error(self, error: Exception, input_data: dict, context: ProcessingContext) -> str:
        return f"recovered: {str(error)}"


class MockValidator(BaseValidator):
    """Mock validator for testing."""
    
    async def validate(self, data: dict) -> ValidationResult:
        self.reset()
        
        if not data:
            self.add_error("data", "Data cannot be empty")
        
        if "invalid" in data:
            self.add_error("invalid", "Invalid field detected")
        
        if "warning" in data:
            self.add_warning("warning", "Warning field detected")
        
        return self.get_result()


class TestBaseService:
    """Test cases for BaseService."""
    
    @pytest.mark.asyncio
    async def test_service_lifecycle(self):
        """Test service initialization and shutdown."""
        service = MockService("test_service", {"test_config": "value"})
        
        # Initial state
        assert service.state == ServiceState.UNINITIALIZED
        assert not service.is_running()
        assert not service.is_healthy()
        
        # Initialize
        await service.initialize()
        assert service.state == ServiceState.RUNNING
        assert service.is_running()
        assert service.is_healthy()
        assert service.initialized
        
        # Shutdown
        await service.shutdown()
        assert service.state == ServiceState.STOPPED
        assert not service.is_running()
        assert service.shutdown_called
    
    @pytest.mark.asyncio
    async def test_service_configuration(self):
        """Test service configuration management."""
        config = {"key1": "value1", "key2": "value2"}
        service = MockService("test_service", config)
        
        # Test initial config
        assert service.get_config("key1") == "value1"
        assert service.get_config("nonexistent", "default") == "default"
        
        # Test config update
        service.update_config({"key3": "value3"})
        assert service.get_config("key3") == "value3"
        assert service.get_config("key1") == "value1"  # Original config preserved
    
    @pytest.mark.asyncio
    async def test_service_metrics(self):
        """Test service metrics collection."""
        service = MockService("test_service", {})
        await service.initialize()
        
        metrics = await service.get_metrics()
        assert metrics.metrics["service_name"] == "test_service"
        assert metrics.metrics["service_state"] == ServiceState.RUNNING
        assert metrics.metrics["error_count"] == 0
        
        await service.shutdown()
    
    @pytest.mark.asyncio
    async def test_service_health_check(self):
        """Test service health check."""
        service = MockService("test_service", {})
        
        # Before initialization
        health = await service.health_check()
        assert not health["healthy"]
        assert health["state"] == ServiceState.UNINITIALIZED
        
        # After initialization
        await service.initialize()
        health = await service.health_check()
        assert health["healthy"]
        assert health["state"] == ServiceState.RUNNING
        
        await service.shutdown()


class TestBaseProcessor:
    """Test cases for BaseProcessor."""
    
    @pytest.mark.asyncio
    async def test_processor_initialization(self):
        """Test processor initialization."""
        processor = MockProcessor("test_processor")
        
        assert not processor._is_initialized
        
        await processor.initialize()
        assert processor._is_initialized
        
        await processor.shutdown()
        assert not processor._is_initialized
    
    @pytest.mark.asyncio
    async def test_successful_processing(self):
        """Test successful data processing."""
        processor = MockProcessor("test_processor")
        await processor.initialize()
        
        input_data = {"test": "data"}
        result = await processor.process(input_data)
        
        assert result.success
        assert result.result == "processed"
        assert result.execution_time is not None
        assert result.execution_time > 0
        
        await processor.shutdown()
    
    @pytest.mark.asyncio
    async def test_validation_failure(self):
        """Test processing with validation failure."""
        processor = MockProcessor("test_processor")
        processor.validation_result = False
        await processor.initialize()
        
        input_data = {"test": "data"}
        result = await processor.process(input_data)
        
        assert not result.success
        assert "validation failed" in result.error.lower()
        
        await processor.shutdown()
    
    @pytest.mark.asyncio
    async def test_processing_error_with_recovery(self):
        """Test error handling and recovery."""
        processor = MockProcessor("test_processor")
        processor.error_result = ValueError("Test error")
        await processor.initialize()
        
        input_data = {"test": "data"}
        result = await processor.process(input_data)
        
        assert result.success
        assert result.result == "recovered: Test error"
        
        await processor.shutdown()
    
    @pytest.mark.asyncio
    async def test_batch_processing(self):
        """Test batch processing functionality."""
        processor = MockProcessor("test_processor")
        await processor.initialize()
        
        input_batch = [{"item": i} for i in range(5)]
        results = await processor.batch_process(input_batch, max_concurrent=2)
        
        assert len(results) == 5
        assert all(result.success for result in results)
        assert all(result.result == "processed" for result in results)
        
        await processor.shutdown()
    
    @pytest.mark.asyncio
    async def test_processor_metrics(self):
        """Test processor metrics collection."""
        processor = MockProcessor("test_processor")
        await processor.initialize()
        
        # Process some data
        await processor.process({"test": "data"})
        await processor.process({"test": "data"})
        
        metrics = await processor.get_metrics()
        assert metrics.metrics["processor_name"] == "test_processor"
        assert metrics.metrics["total_processed"] == 2
        assert metrics.metrics["total_errors"] == 0
        
        await processor.shutdown()


class TestBaseValidator:
    """Test cases for BaseValidator."""
    
    @pytest.mark.asyncio
    async def test_successful_validation(self):
        """Test successful validation."""
        validator = MockValidator("test_validator")
        
        data = {"valid": "data"}
        result = await validator.validate(data)
        
        assert result.is_valid
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    @pytest.mark.asyncio
    async def test_validation_errors(self):
        """Test validation with errors."""
        validator = MockValidator("test_validator")
        
        data = {"invalid": "data"}
        result = await validator.validate(data)
        
        assert not result.is_valid
        assert len(result.errors) == 1
        assert result.errors[0].field == "invalid"
        assert result.errors[0].severity == ValidationSeverity.ERROR
    
    @pytest.mark.asyncio
    async def test_validation_warnings(self):
        """Test validation with warnings."""
        validator = MockValidator("test_validator")
        
        data = {"warning": "data"}
        result = await validator.validate(data)
        
        assert result.is_valid  # Warnings don't make validation fail
        assert len(result.warnings) == 1
        assert result.warnings[0].field == "warning"
        assert result.warnings[0].severity == ValidationSeverity.WARNING
    
    @pytest.mark.asyncio
    async def test_empty_data_validation(self):
        """Test validation of empty data."""
        validator = MockValidator("test_validator")
        
        result = await validator.validate({})
        
        assert not result.is_valid
        assert len(result.errors) == 1
        assert "empty" in result.errors[0].message.lower()
    
    def test_validation_utilities(self):
        """Test validation utility methods."""
        validator = MockValidator("test_validator")
        
        # Test required field validation
        data = {"field1": "value1"}
        assert validator.validate_required(data, "field1")
        assert not validator.validate_required(data, "field2")
        
        # Test type validation
        assert validator.validate_type(data, "field1", str)
        assert not validator.validate_type({"field": 123}, "field", str)
        
        # Test string length validation
        assert validator.validate_string_length({"field": "test"}, "field", min_length=2, max_length=10)
        assert not validator.validate_string_length({"field": "a"}, "field", min_length=2)
        
        # Test choices validation
        assert validator.validate_choices({"field": "option1"}, "field", ["option1", "option2"])
        assert not validator.validate_choices({"field": "option3"}, "field", ["option1", "option2"])


class TestBaseMetrics:
    """Test cases for BaseMetrics."""
    
    @pytest.mark.asyncio
    async def test_metrics_initialization(self):
        """Test metrics initialization."""
        metrics = BaseMetrics("test_namespace")
        
        await metrics.initialize()
        assert metrics._is_initialized
        assert metrics._collection_enabled
        
        await metrics.shutdown()
        assert not metrics._collection_enabled
    
    @pytest.mark.asyncio
    async def test_counter_metrics(self):
        """Test counter metrics."""
        metrics = BaseMetrics("test_namespace")
        await metrics.initialize()
        
        metrics.record_counter("test_counter", 5.0)
        metrics.increment_counter("test_counter")
        
        assert metrics.get_counter("test_counter") == 6.0
        
        await metrics.shutdown()
    
    @pytest.mark.asyncio
    async def test_gauge_metrics(self):
        """Test gauge metrics."""
        metrics = BaseMetrics("test_namespace")
        await metrics.initialize()
        
        metrics.record_gauge("test_gauge", 42.0)
        assert metrics.get_gauge("test_gauge") == 42.0
        
        metrics.record_gauge("test_gauge", 24.0)
        assert metrics.get_gauge("test_gauge") == 24.0
        
        await metrics.shutdown()
    
    @pytest.mark.asyncio
    async def test_timer_metrics(self):
        """Test timer metrics."""
        metrics = BaseMetrics("test_namespace")
        await metrics.initialize()
        
        # Test timer context
        with metrics.start_timer("test_timer"):
            await asyncio.sleep(0.01)  # Small delay
        
        stats = metrics.get_timer_stats("test_timer")
        assert stats is not None
        assert stats["count"] == 1
        assert stats["min_ms"] > 0
        
        await metrics.shutdown()
    
    @pytest.mark.asyncio
    async def test_histogram_metrics(self):
        """Test histogram metrics."""
        metrics = BaseMetrics("test_namespace")
        await metrics.initialize()
        
        # Record some values
        for value in [1, 2, 3, 4, 5]:
            metrics.record_histogram("test_histogram", value)
        
        stats = metrics.get_histogram_stats("test_histogram")
        assert stats is not None
        assert stats["count"] == 5
        assert stats["min"] == 1
        assert stats["max"] == 5
        assert stats["avg"] == 3.0
        
        await metrics.shutdown()
    
    @pytest.mark.asyncio
    async def test_metrics_snapshot(self):
        """Test metrics snapshot."""
        metrics = BaseMetrics("test_namespace")
        await metrics.initialize()
        
        metrics.record_counter("counter", 10)
        metrics.record_gauge("gauge", 20)
        metrics.record_histogram("histogram", 30)
        
        snapshot = await metrics.get_snapshot()
        assert "test_namespace.counter" in snapshot.metrics
        assert "test_namespace.gauge" in snapshot.metrics
        assert "test_namespace.histogram.count" in snapshot.metrics
        
        await metrics.shutdown()


class TestBaseCache:
    """Test cases for BaseCache."""
    
    @pytest.mark.asyncio
    async def test_cache_basic_operations(self):
        """Test basic cache operations."""
        cache = BaseCache("test_cache", max_size=10)
        await cache.initialize()
        
        # Test set and get
        await cache.set("key1", "value1")
        value = await cache.get("key1")
        assert value == "value1"
        
        # Test non-existent key
        value = await cache.get("nonexistent", "default")
        assert value == "default"
        
        # Test exists
        assert await cache.exists("key1")
        assert not await cache.exists("nonexistent")
        
        await cache.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_ttl(self):
        """Test cache TTL functionality."""
        cache = BaseCache("test_cache", max_size=10)
        await cache.initialize()
        
        # Set with short TTL
        await cache.set("key1", "value1", ttl=1)  # 1 second
        
        # Should exist immediately
        assert await cache.exists("key1")
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Should be expired
        assert not await cache.exists("key1")
        
        await cache.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_eviction(self):
        """Test cache eviction policies."""
        cache = BaseCache("test_cache", max_size=3, policy=CachePolicy.LRU)
        await cache.initialize()
        
        # Fill cache to capacity
        await cache.set("key1", "value1")
        await cache.set("key2", "value2")
        await cache.set("key3", "value3")
        
        # Add one more item (should evict least recently used)
        await cache.set("key4", "value4")
        
        # key1 should be evicted (least recently used)
        assert not await cache.exists("key1")
        assert await cache.exists("key2")
        assert await cache.exists("key3")
        assert await cache.exists("key4")
        
        await cache.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_bulk_operations(self):
        """Test cache bulk operations."""
        cache = BaseCache("test_cache", max_size=10)
        await cache.initialize()
        
        # Test set_many
        items = {"key1": "value1", "key2": "value2", "key3": "value3"}
        await cache.set_many(items)
        
        # Test get_many
        result = await cache.get_many(["key1", "key2", "key3", "nonexistent"])
        assert len(result) == 3
        assert result["key1"] == "value1"
        assert result["key2"] == "value2"
        assert result["key3"] == "value3"
        
        # Test delete_many
        deleted_count = await cache.delete_many(["key1", "key2"])
        assert deleted_count == 2
        assert not await cache.exists("key1")
        assert not await cache.exists("key2")
        assert await cache.exists("key3")
        
        await cache.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_get_or_set(self):
        """Test cache get_or_set functionality."""
        cache = BaseCache("test_cache", max_size=10)
        await cache.initialize()
        
        call_count = 0
        
        def factory():
            nonlocal call_count
            call_count += 1
            return f"generated_value_{call_count}"
        
        # First call should generate value
        value1 = await cache.get_or_set("key1", factory)
        assert value1 == "generated_value_1"
        assert call_count == 1
        
        # Second call should use cached value
        value2 = await cache.get_or_set("key1", factory)
        assert value2 == "generated_value_1"
        assert call_count == 1  # Factory not called again
        
        await cache.shutdown()
    
    def test_cache_stats(self):
        """Test cache statistics."""
        cache = BaseCache("test_cache", max_size=10)
        
        # Initially empty
        stats = cache.get_stats()
        assert stats.total_entries == 0
        assert stats.hit_count == 0
        assert stats.miss_count == 0
        
        # Reset stats
        cache.reset_stats()
        stats = cache.get_stats()
        assert stats.hit_count == 0
        assert stats.miss_count == 0


class TestDataModels:
    """Test cases for unified data models."""
    
    def test_unified_task_creation(self):
        """Test UnifiedTask creation and properties."""
        task = UnifiedTask(
            name="Test Task",
            description="A test task",
            task_type="test",
            priority=TaskPriority.HIGH,
            parameters={"param1": "value1"}
        )
        
        assert task.name == "Test Task"
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.PENDING
        assert task.parameters["param1"] == "value1"
        assert task.can_retry()
        assert not task.is_expired()
    
    def test_unified_task_lifecycle(self):
        """Test UnifiedTask lifecycle management."""
        task = UnifiedTask(name="Test", task_type="test")
        
        # Start task
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        # Complete task
        task.status = TaskStatus.COMPLETED
        task.completed_at = datetime.now()
        
        assert task.duration_seconds is not None
        assert task.duration_seconds > 0
    
    def test_standard_result_creation(self):
        """Test StandardResult creation."""
        # Success result
        success_result = StandardResult.success_result(
            result="test_result",
            execution_time=1.5,
            metadata={"key": "value"}
        )
        
        assert success_result.success
        assert success_result.result == "test_result"
        assert success_result.execution_time == 1.5
        assert success_result.metadata["key"] == "value"
        
        # Error result
        error_result = StandardResult.error_result(
            error="Test error",
            error_code="TEST_ERROR"
        )
        
        assert not error_result.success
        assert error_result.error == "Test error"
        assert error_result.error_code == "TEST_ERROR"
    
    def test_capability_model(self):
        """Test CapabilityModel functionality."""
        capability = CapabilityModel(
            name="test_capability",
            capability_type=CapabilityType.CODE_ANALYSIS,
            description="Test capability",
            supported_languages=["python", "javascript"]
        )
        
        assert capability.name == "test_capability"
        assert capability.capability_type == CapabilityType.CODE_ANALYSIS
        assert "python" in capability.supported_languages
    
    def test_context_model(self):
        """Test ContextModel functionality."""
        context = ContextModel(
            context_type="test_context",
            scope="session",
            data={"key1": "value1"}
        )
        
        # Test data operations
        context.update_data("key2", "value2")
        assert context.get_data("key2") == "value2"
        assert context.get_data("nonexistent", "default") == "default"
        
        # Test expiration
        assert not context.is_expired()
        
        context.expires_at = datetime.now() - timedelta(seconds=1)
        assert context.is_expired()
    
    def test_context_model_merging(self):
        """Test ContextModel merging functionality."""
        context1 = ContextModel(
            data={"key1": "value1", "key2": "value2"},
            metadata={"meta1": "metavalue1"}
        )
        
        context2 = ContextModel(
            data={"key2": "updated_value2", "key3": "value3"},
            metadata={"meta2": "metavalue2"}
        )
        
        context1.merge_context(context2)
        
        assert context1.get_data("key1") == "value1"
        assert context1.get_data("key2") == "updated_value2"  # Updated
        assert context1.get_data("key3") == "value3"  # Added
        assert context1.metadata["meta1"] == "metavalue1"
        assert context1.metadata["meta2"] == "metavalue2"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
