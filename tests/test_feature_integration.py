"""
Integration tests for feature coupling and communication.

This module tests the integration between agents, plugins, and shared
infrastructure components.
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from agent_framework.communication.enhanced_broker import Enhanced<PERSON><PERSON>age<PERSON><PERSON><PERSON>, TypedEvent, EventPriority
from agent_framework.agents.integrated_agent import IntegratedAgent
from agent_framework.plugins.integrated_plugin import IntegratedPlugin
from agent_framework.shared.base_cache import BaseCache, CachePolicy
from agent_framework.shared.data_models import (
    UnifiedTask, StandardResult, CapabilityModel, ContextModel,
    TaskPriority, TaskStatus, CapabilityType, UnifiedRequest
)
from agent_framework.core.config import FrameworkConfig
from agent_framework.core.types import PluginRequest, PluginResponse, PluginCapability


class MockIntegratedPlugin(IntegratedPlugin):
    """Mock integrated plugin for testing."""
    
    def __init__(self, name: str = "mock_plugin"):
        super().__init__(name, "1.0.0")
        
        # Add test capability
        self.add_capability(PluginCapability(
            name="test_capability",
            description="Test capability for integration testing",
            input_schema={
                "type": "object",
                "properties": {
                    "input": {"type": "string"}
                },
                "required": ["input"]
            },
            output_schema={
                "type": "object",
                "properties": {
                    "output": {"type": "string"}
                }
            }
        ))
    
    async def _execute_capability(self, request: PluginRequest, context) -> dict:
        """Execute the test capability."""
        input_value = request.parameters.get("input", "")
        return {"output": f"processed: {input_value}"}


class MockIntegratedAgent(IntegratedAgent):
    """Mock integrated agent for testing."""
    
    def __init__(self, agent_id: str = "mock_agent"):
        capabilities = [
            CapabilityModel(
                name="code_analysis",
                capability_type=CapabilityType.CODE_ANALYSIS,
                description="Code analysis capability"
            )
        ]
        super().__init__(agent_id, capabilities)
    
    async def _execute_task_by_type(self, task: UnifiedTask, context) -> dict:
        """Execute task based on type."""
        return {
            "task_id": task.id,
            "task_type": task.task_type,
            "result": f"Agent processed: {task.name}",
            "timestamp": datetime.now().isoformat()
        }


@pytest_asyncio.fixture
async def test_config():
    """Create test configuration."""
    return FrameworkConfig()


@pytest_asyncio.fixture
async def enhanced_broker(test_config):
    """Create and initialize enhanced message broker."""
    broker = EnhancedMessageBroker(test_config)
    await broker.initialize()
    yield broker
    await broker.shutdown()


@pytest_asyncio.fixture
async def test_cache():
    """Create and initialize test cache."""
    cache = BaseCache("test_cache", max_size=100, policy=CachePolicy.LRU)
    await cache.initialize()
    yield cache
    await cache.shutdown()


@pytest_asyncio.fixture
async def integrated_plugin(enhanced_broker, test_cache):
    """Create and initialize integrated plugin."""
    plugin = MockIntegratedPlugin()
    plugin.message_broker = enhanced_broker
    plugin.cache = test_cache
    await plugin.initialize({})
    yield plugin
    await plugin.cleanup()


@pytest_asyncio.fixture
async def integrated_agent(enhanced_broker, test_cache):
    """Create and initialize integrated agent."""
    agent = MockIntegratedAgent()
    agent.message_broker = enhanced_broker
    agent.cache = test_cache
    await agent.initialize({})
    yield agent
    await agent.shutdown()


class TestEnhancedMessageBroker:
    """Test cases for enhanced message broker."""
    
    @pytest.mark.asyncio
    async def test_broker_initialization(self, test_config):
        """Test broker initialization and shutdown."""
        broker = EnhancedMessageBroker(test_config)
        
        assert not broker.is_running()
        
        await broker.initialize()
        assert broker.is_running()
        
        await broker.shutdown()
        assert not broker.is_running()
    
    @pytest.mark.asyncio
    async def test_typed_event_publishing(self, enhanced_broker):
        """Test typed event publishing and handling."""
        events_received = []
        
        def event_handler(event: TypedEvent):
            events_received.append(event)
        
        # Subscribe to events
        enhanced_broker.subscribe(
            event_type="test_event",
            handler=event_handler,
            subscriber_id="test_subscriber"
        )
        
        # Publish event
        test_event = TypedEvent(
            event_type="test_event",
            payload={"message": "test"},
            source="test_source",
            priority=EventPriority.HIGH
        )
        
        await enhanced_broker.publish_event(test_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify event was received
        assert len(events_received) == 1
        assert events_received[0].payload["message"] == "test"
        assert events_received[0].priority == EventPriority.HIGH
    
    @pytest.mark.asyncio
    async def test_event_filtering(self, enhanced_broker):
        """Test event filtering functionality."""
        filtered_events = []
        all_events = []
        
        def filtered_handler(event: TypedEvent):
            filtered_events.append(event)
        
        def all_handler(event: TypedEvent):
            all_events.append(event)
        
        # Subscribe with filter
        enhanced_broker.subscribe(
            event_type="test_event",
            handler=filtered_handler,
            subscriber_id="filtered_subscriber",
            filter_func=lambda e: e.payload.get("important", False)
        )
        
        # Subscribe to all events
        enhanced_broker.subscribe(
            event_type="test_event",
            handler=all_handler,
            subscriber_id="all_subscriber"
        )
        
        # Publish events
        important_event = TypedEvent(
            event_type="test_event",
            payload={"message": "important", "important": True},
            source="test"
        )
        
        normal_event = TypedEvent(
            event_type="test_event",
            payload={"message": "normal", "important": False},
            source="test"
        )
        
        await enhanced_broker.publish_event(important_event)
        await enhanced_broker.publish_event(normal_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify filtering
        assert len(filtered_events) == 1
        assert filtered_events[0].payload["message"] == "important"
        assert len(all_events) == 2
    
    @pytest.mark.asyncio
    async def test_request_response_pattern(self, enhanced_broker):
        """Test request-response communication pattern."""
        # Set up response handler
        async def request_handler(event: TypedEvent):
            if event.event_type == "request":
                request = event.payload
                response = StandardResult.success_result(
                    result=f"Response to {request.id}"
                )
                await enhanced_broker.send_response(request.id, response)
        
        enhanced_broker.subscribe(
            event_type="request",
            handler=request_handler,
            subscriber_id="response_handler"
        )
        
        # Send request
        request = UnifiedRequest(
            request_type="test_request",
            target="test_target",
            payload={"data": "test"}
        )
        
        response = await enhanced_broker.send_request(request, timeout=5.0)
        
        assert response.success
        assert f"Response to {request.id}" in response.result
    
    @pytest.mark.asyncio
    async def test_broker_statistics(self, enhanced_broker):
        """Test broker statistics collection."""
        # Publish some events
        for i in range(5):
            event = TypedEvent(
                event_type="test_event",
                payload={"index": i},
                source="test"
            )
            await enhanced_broker.publish_event(event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        stats = enhanced_broker.get_broker_stats()
        assert stats["events_published"] == 5
        assert stats["events_processed"] >= 5
        assert stats["events_failed"] == 0


class TestAgentPluginIntegration:
    """Test cases for agent-plugin integration."""
    
    @pytest.mark.asyncio
    async def test_agent_plugin_communication(self, integrated_agent, integrated_plugin):
        """Test communication between agent and plugin."""
        # Create a task that would use the plugin
        task = UnifiedTask(
            name="Test Task",
            task_type="test",
            parameters={"input": "test data"}
        )
        
        # Process task with agent
        result = await integrated_agent.process(task)
        
        assert result.success
        assert "Agent processed" in result.result["result"]
    
    @pytest.mark.asyncio
    async def test_plugin_capability_execution(self, integrated_plugin):
        """Test plugin capability execution."""
        request = PluginRequest(
            capability="test_capability",
            parameters={"input": "test input"}
        )
        
        response = await integrated_plugin.execute(request)
        
        assert response.success
        assert response.result["output"] == "processed: test input"
        assert response.metadata["plugin_name"] == "mock_plugin"
    
    @pytest.mark.asyncio
    async def test_plugin_caching(self, integrated_plugin, test_cache):
        """Test plugin result caching."""
        request = PluginRequest(
            capability="test_capability",
            parameters={"input": "cached input"}
        )
        
        # First execution
        response1 = await integrated_plugin.execute(request)
        assert response1.success
        
        # Second execution (should use cache)
        response2 = await integrated_plugin.execute(request)
        assert response2.success
        assert response2.result == response1.result
        
        # Verify cache was used (check cache directly)
        cache_key = integrated_plugin._generate_cache_key(request)
        cached_response = await test_cache.get(cache_key)
        assert cached_response is not None
    
    @pytest.mark.asyncio
    async def test_agent_event_handling(self, integrated_agent, enhanced_broker):
        """Test agent event handling."""
        # Create task assignment event
        task_data = {
            "id": "test_task_id",
            "name": "Event Task",
            "task_type": "test",
            "parameters": {"data": "event data"}
        }
        
        event = TypedEvent(
            event_type="task_assignment",
            payload=task_data,
            source="test_source",
            target="mock_agent",
            correlation_id="test_correlation"
        )
        
        # Publish event
        await enhanced_broker.publish_event(event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify agent processed the task
        # (In a real implementation, we'd check the response)
        assert True  # Placeholder assertion
    
    @pytest.mark.asyncio
    async def test_capability_query_response(self, integrated_agent, enhanced_broker):
        """Test capability query and response."""
        responses = []
        
        def response_handler(event: TypedEvent):
            if event.event_type == "capability_response":
                responses.append(event)
        
        enhanced_broker.subscribe(
            event_type="capability_response",
            handler=response_handler,
            subscriber_id="test_response_handler"
        )
        
        # Send capability query
        query_event = TypedEvent(
            event_type="capability_query",
            payload={},
            source="test_source",
            correlation_id="test_query"
        )
        
        await enhanced_broker.publish_event(query_event)
        
        # Wait for response
        await asyncio.sleep(0.1)
        
        # Verify response
        assert len(responses) == 1
        response = responses[0]
        assert response.payload["agent_id"] == "mock_agent"
        assert len(response.payload["capabilities"]) > 0


class TestSharedInfrastructureIntegration:
    """Test cases for shared infrastructure integration."""
    
    @pytest.mark.asyncio
    async def test_shared_cache_usage(self, integrated_agent, integrated_plugin, test_cache):
        """Test shared cache usage across components."""
        # Agent caches a result
        await test_cache.set("shared_key", "agent_value")
        
        # Plugin retrieves the cached value
        value = await test_cache.get("shared_key")
        assert value == "agent_value"
        
        # Plugin updates the cache
        await test_cache.set("shared_key", "plugin_value")
        
        # Agent retrieves updated value
        value = await test_cache.get("shared_key")
        assert value == "plugin_value"
    
    @pytest.mark.asyncio
    async def test_metrics_collection_integration(self, integrated_agent, integrated_plugin):
        """Test metrics collection across integrated components."""
        # Execute operations to generate metrics
        task = UnifiedTask(name="Metrics Test", task_type="test")
        await integrated_agent.process(task)
        
        request = PluginRequest(
            capability="test_capability",
            parameters={"input": "metrics test"}
        )
        await integrated_plugin.execute(request)
        
        # Get metrics from both components
        agent_metrics = await integrated_agent.get_metrics()
        plugin_stats = integrated_plugin.get_plugin_stats()
        
        # Verify metrics were collected
        assert agent_metrics.metrics["total_processed"] >= 1
        assert plugin_stats["requests_processed"] >= 1
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, integrated_agent, enhanced_broker):
        """Test error handling across integrated components."""
        error_events = []
        
        def error_handler(event: TypedEvent):
            if event.event_type == "task_failed":
                error_events.append(event)
        
        enhanced_broker.subscribe(
            event_type="task_failed",
            handler=error_handler,
            subscriber_id="error_handler"
        )
        
        # Create a task that will cause an error
        # (This would need to be implemented in the mock agent)
        task = UnifiedTask(
            name="Error Task",
            task_type="error_test",
            parameters={"cause_error": True}
        )
        
        # Process task (should handle error gracefully)
        result = await integrated_agent.process(task)
        
        # Wait for error event
        await asyncio.sleep(0.1)
        
        # Verify error was handled
        # (Implementation depends on how errors are handled)
        assert True  # Placeholder assertion


class TestConcurrencyAndPerformance:
    """Test cases for concurrency and performance."""
    
    @pytest.mark.asyncio
    async def test_concurrent_task_processing(self, integrated_agent):
        """Test concurrent task processing."""
        tasks = [
            UnifiedTask(
                name=f"Concurrent Task {i}",
                task_type="test",
                parameters={"index": i}
            )
            for i in range(10)
        ]
        
        # Process tasks concurrently
        results = await asyncio.gather(*[
            integrated_agent.process(task) for task in tasks
        ])
        
        # Verify all tasks completed successfully
        assert len(results) == 10
        assert all(result.success for result in results)
    
    @pytest.mark.asyncio
    async def test_plugin_concurrent_requests(self, integrated_plugin):
        """Test concurrent plugin requests."""
        requests = [
            PluginRequest(
                capability="test_capability",
                parameters={"input": f"concurrent input {i}"}
            )
            for i in range(10)
        ]
        
        # Execute requests concurrently
        responses = await asyncio.gather(*[
            integrated_plugin.execute(request) for request in requests
        ])
        
        # Verify all requests completed successfully
        assert len(responses) == 10
        assert all(response.success for response in responses)
    
    @pytest.mark.asyncio
    async def test_message_broker_throughput(self, enhanced_broker):
        """Test message broker throughput."""
        events_received = []
        
        def event_handler(event: TypedEvent):
            events_received.append(event)
        
        enhanced_broker.subscribe(
            event_type="throughput_test",
            handler=event_handler,
            subscriber_id="throughput_handler"
        )
        
        # Publish many events
        num_events = 100
        events = [
            TypedEvent(
                event_type="throughput_test",
                payload={"index": i},
                source="test"
            )
            for i in range(num_events)
        ]
        
        start_time = asyncio.get_event_loop().time()
        
        # Publish all events
        await asyncio.gather(*[
            enhanced_broker.publish_event(event) for event in events
        ])
        
        # Wait for processing
        await asyncio.sleep(1.0)
        
        end_time = asyncio.get_event_loop().time()
        
        # Verify throughput
        assert len(events_received) == num_events
        processing_time = end_time - start_time
        throughput = num_events / processing_time
        
        # Should process at least 50 events per second
        assert throughput > 50


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
