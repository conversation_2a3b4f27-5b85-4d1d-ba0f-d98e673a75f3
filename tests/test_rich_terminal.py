"""
Tests for rich terminal interface features.

Tests the enhanced CLI formatting, progress tracking, and workflow visualization.
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

# Test imports with fallbacks
try:
    from agent_framework.cli.rich_formatter import RichTerminalFormatter
    from agent_framework.cli.progress_tracker import AdvancedProgressTracker, MultiStepProgressTracker
    from agent_framework.cli.workflow_visualizer import WorkflowVisualizer, WorkflowPhase, StepStatus
    from agent_framework.cli.interactive_ux import InteractiveUX, MessageType, ActionableError
    from agent_framework.cli.utils import CLIUtils, ProgressIndicator
    _RICH_AVAILABLE = True
except ImportError:
    _RICH_AVAILABLE = False
    pytest.skip("Rich library not available", allow_module_level=True)


class TestRichTerminalFormatter:
    """Test rich terminal formatter functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.formatter = RichTerminalFormatter()
    
    def test_initialization(self):
        """Test formatter initialization."""
        assert self.formatter is not None
        assert self.formatter.console is not None
        assert self.formatter.error_console is not None
        assert 'success' in self.formatter.colors
        assert 'error' in self.formatter.colors
    
    def test_print_methods(self, capsys):
        """Test various print methods."""
        self.formatter.print_success("Test success")
        self.formatter.print_error("Test error")
        self.formatter.print_warning("Test warning")
        self.formatter.print_info("Test info")
        
        # Note: Rich output is complex to test directly, 
        # so we mainly test that methods don't raise exceptions
        assert True  # If we get here, no exceptions were raised
    
    def test_table_creation(self):
        """Test table creation and formatting."""
        headers = ["Name", "Value", "Status"]
        rows = [
            ["Item 1", "100", "Active"],
            ["Item 2", "200", "Inactive"]
        ]
        
        # Test that table creation doesn't raise exceptions
        self.formatter.print_table(headers, rows, "Test Table")
        assert True
    
    def test_code_formatting(self):
        """Test code block formatting."""
        code = """
def hello_world():
    print("Hello, World!")
    return True
        """
        
        self.formatter.print_code(code, "python", "Test Code")
        assert True
    
    def test_json_formatting(self):
        """Test JSON formatting."""
        data = {
            "name": "test",
            "value": 123,
            "active": True,
            "items": ["a", "b", "c"]
        }
        
        self.formatter.print_json(data, "Test JSON")
        assert True
    
    def test_panel_and_layout(self):
        """Test panel and layout features."""
        self.formatter.print_panel("Test content", "Test Panel")
        self.formatter.print_header("Test Header", "Test Subtitle")
        self.formatter.print_section("Test Section")
        assert True
    
    def test_interactive_prompts(self):
        """Test interactive prompt methods."""
        # Mock input for testing
        with patch('builtins.input', return_value='test'):
            # These would normally require user input, so we mock it
            assert True
    
    def test_status_context(self):
        """Test status context manager."""
        with self.formatter.status("Testing status") as status:
            time.sleep(0.1)  # Brief pause
            assert status is not None


class TestAdvancedProgressTracker:
    """Test advanced progress tracker functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.tracker = AdvancedProgressTracker()
    
    def test_operation_lifecycle(self):
        """Test complete operation lifecycle."""
        op_id = "test_operation"
        
        # Create operation
        self.tracker.create_operation(op_id, "Test Op", "Testing operation", 100)
        assert op_id in self.tracker.operations
        
        # Start operation
        self.tracker.start_operation(op_id)
        operation = self.tracker.operations[op_id]
        assert operation.status.value == "running"
        assert operation.start_time is not None
        
        # Update progress
        self.tracker.update_operation(op_id, completed_work=50)
        assert operation.completed_work == 50
        
        # Complete operation
        self.tracker.complete_operation(op_id, "Operation completed")
        assert operation.status.value == "completed"
        assert operation.end_time is not None
    
    def test_operation_failure(self):
        """Test operation failure handling."""
        op_id = "failing_operation"
        
        self.tracker.create_operation(op_id, "Failing Op", "This will fail")
        self.tracker.start_operation(op_id)
        self.tracker.fail_operation(op_id, "Test error message")
        
        operation = self.tracker.operations[op_id]
        assert operation.status.value == "failed"
        assert operation.error_message == "Test error message"
    
    def test_progress_context(self):
        """Test progress context manager."""
        op_id = "context_operation"
        self.tracker.create_operation(op_id, "Context Op", "Testing context", 10)
        
        with self.tracker.progress_context(op_id, title="Test Progress") as progress:
            assert progress is not None
            self.tracker.start_operation(op_id)
            self.tracker.update_operation(op_id, completed_work=5)
            self.tracker.complete_operation(op_id)
    
    def test_summary_generation(self):
        """Test summary generation."""
        # Create multiple operations
        for i in range(3):
            op_id = f"op_{i}"
            self.tracker.create_operation(op_id, f"Operation {i}", f"Test operation {i}")
            self.tracker.start_operation(op_id)
            if i < 2:
                self.tracker.complete_operation(op_id)
            else:
                self.tracker.fail_operation(op_id, "Test failure")
        
        # Test summary printing (should not raise exceptions)
        self.tracker.print_summary()
        assert True


class TestMultiStepProgressTracker:
    """Test multi-step progress tracker."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.tracker = MultiStepProgressTracker()
    
    def test_workflow_setup(self):
        """Test workflow setup and step addition."""
        steps = [
            ("step1", "Step 1", "First step", 2.0),
            ("step2", "Step 2", "Second step", 3.0),
            ("step3", "Step 3", "Third step", 1.0)
        ]
        
        for step_id, name, desc, duration in steps:
            self.tracker.add_step(step_id, name, desc, duration)
        
        assert len(self.tracker.steps) == 3
        assert self.tracker.steps[0]['id'] == 'step1'
    
    def test_workflow_execution(self):
        """Test workflow execution flow."""
        self.tracker.add_step("step1", "Step 1", "First step")
        self.tracker.add_step("step2", "Step 2", "Second step")
        
        self.tracker.start_workflow("Test Workflow")
        
        # Execute steps
        step_id = self.tracker.start_next_step()
        assert step_id == "step1"
        
        self.tracker.complete_current_step("Step 1 completed")
        
        step_id = self.tracker.start_next_step()
        assert step_id == "step2"
        
        self.tracker.complete_current_step("Step 2 completed")
        
        # No more steps
        step_id = self.tracker.start_next_step()
        assert step_id is None


class TestWorkflowVisualizer:
    """Test workflow visualizer functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.visualizer = WorkflowVisualizer()
    
    def test_step_management(self):
        """Test step addition and management."""
        self.visualizer.add_step(
            "init", "Initialize", "Initialize system",
            WorkflowPhase.INITIALIZATION
        )
        self.visualizer.add_step(
            "process", "Process", "Process data",
            WorkflowPhase.EXECUTION, dependencies=["init"]
        )
        
        assert len(self.visualizer.steps) == 2
        assert "init" in self.visualizer.steps
        assert "process" in self.visualizer.steps
        assert self.visualizer.steps["process"].dependencies == ["init"]
    
    def test_workflow_execution(self):
        """Test workflow execution and status tracking."""
        self.visualizer.add_step("step1", "Step 1", "First step", WorkflowPhase.EXECUTION)
        self.visualizer.start_workflow("Test Workflow")
        
        # Start step
        success = self.visualizer.start_step("step1")
        assert success is True
        assert self.visualizer.steps["step1"].status == StepStatus.RUNNING
        
        # Complete step
        self.visualizer.complete_step("step1", "Completed successfully")
        assert self.visualizer.steps["step1"].status == StepStatus.COMPLETED
    
    def test_dependency_checking(self):
        """Test dependency checking."""
        self.visualizer.add_step("step1", "Step 1", "First step", WorkflowPhase.EXECUTION)
        self.visualizer.add_step("step2", "Step 2", "Second step", WorkflowPhase.EXECUTION, ["step1"])
        
        # Should not be able to start step2 before step1
        success = self.visualizer.start_step("step2")
        assert success is False
        
        # Complete step1, then step2 should be startable
        self.visualizer.start_step("step1")
        self.visualizer.complete_step("step1")
        
        success = self.visualizer.start_step("step2")
        assert success is True
    
    def test_progress_calculation(self):
        """Test workflow progress calculation."""
        self.visualizer.add_step("step1", "Step 1", "First step", WorkflowPhase.EXECUTION)
        self.visualizer.add_step("step2", "Step 2", "Second step", WorkflowPhase.EXECUTION)
        
        # Initially 0% progress
        progress = self.visualizer.get_workflow_progress()
        assert progress == 0.0
        
        # Complete one step - 50% progress
        self.visualizer.start_step("step1")
        self.visualizer.complete_step("step1")
        progress = self.visualizer.get_workflow_progress()
        assert progress == 50.0
        
        # Complete second step - 100% progress
        self.visualizer.start_step("step2")
        self.visualizer.complete_step("step2")
        progress = self.visualizer.get_workflow_progress()
        assert progress == 100.0


class TestInteractiveUX:
    """Test interactive UX features."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.ux = InteractiveUX()
    
    def test_message_display(self):
        """Test message display methods."""
        self.ux.show_success("Success message")
        self.ux.show_error("Error message")
        self.ux.show_warning("Warning message")
        self.ux.show_info("Info message")
        assert True  # No exceptions raised
    
    def test_actionable_error(self):
        """Test actionable error display."""
        error = ActionableError(
            message="Test error occurred",
            suggestions=["Try this", "Or try that"],
            error_code="E001",
            documentation_url="https://docs.example.com"
        )
        
        self.ux.show_actionable_error(error)
        assert True
    
    def test_loading_spinner(self):
        """Test loading spinner functionality."""
        def dummy_task():
            time.sleep(0.1)
            return "completed"
        
        result = self.ux.show_loading_spinner("Testing...", dummy_task)
        assert result == "completed"
    
    def test_welcome_banner(self):
        """Test welcome banner display."""
        self.ux.show_welcome_banner(
            "Test App",
            version="1.0.0",
            description="Test application"
        )
        assert True


class TestCLIUtilsIntegration:
    """Test CLI utils integration with rich formatting."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.utils_rich = CLIUtils(use_rich=True)
        self.utils_fallback = CLIUtils(use_rich=False)
    
    def test_rich_availability(self):
        """Test rich availability detection."""
        assert self.utils_rich.has_rich_support() is True
        assert self.utils_fallback.has_rich_support() is False
    
    def test_message_methods(self):
        """Test message methods with both rich and fallback."""
        for utils in [self.utils_rich, self.utils_fallback]:
            utils.print_success("Success")
            utils.print_error("Error")
            utils.print_warning("Warning")
            utils.print_info("Info")
        
        assert True
    
    def test_table_formatting(self):
        """Test table formatting with both modes."""
        headers = ["Name", "Value"]
        rows = [["Test", "123"], ["Another", "456"]]
        
        for utils in [self.utils_rich, self.utils_fallback]:
            utils.print_table(headers, rows, "Test Table")
        
        assert True
    
    def test_progress_indicator(self):
        """Test progress indicator with rich support."""
        progress_rich = ProgressIndicator(use_rich=True)
        progress_fallback = ProgressIndicator(use_rich=False)
        
        for progress in [progress_rich, progress_fallback]:
            with progress.spinner("Testing..."):
                time.sleep(0.1)
        
        assert True


class TestIntegrationScenarios:
    """Test integration scenarios combining multiple components."""
    
    def test_complete_workflow_scenario(self):
        """Test a complete workflow scenario."""
        # Setup components
        visualizer = WorkflowVisualizer()
        tracker = MultiStepProgressTracker()
        ux = InteractiveUX()
        
        # Define workflow
        steps = [
            ("init", "Initialize", "Setup system", WorkflowPhase.INITIALIZATION),
            ("process", "Process", "Process data", WorkflowPhase.EXECUTION, ["init"]),
            ("validate", "Validate", "Validate results", WorkflowPhase.VALIDATION, ["process"]),
            ("cleanup", "Cleanup", "Clean up resources", WorkflowPhase.CLEANUP, ["validate"])
        ]
        
        # Add steps to both systems
        for step_data in steps:
            step_id, name, desc, phase = step_data[:4]
            deps = step_data[4] if len(step_data) > 4 else []
            
            visualizer.add_step(step_id, name, desc, phase, deps)
            tracker.add_step(step_id, name, desc, 1.0)
        
        # Start workflows
        visualizer.start_workflow("Integration Test")
        tracker.start_workflow("Integration Test")
        
        # Execute workflow
        available_steps = visualizer.get_next_available_steps()
        assert "init" in available_steps
        
        # Complete init step
        visualizer.start_step("init")
        visualizer.complete_step("init")
        
        # Now process should be available
        available_steps = visualizer.get_next_available_steps()
        assert "process" in available_steps
        
        assert True  # Workflow executed without errors
