#!/usr/bin/env python3
"""
Standalone Rich Terminal Test

Tests the rich terminal formatting without framework dependencies.
"""

import time
import sys

try:
    from rich.console import Console
    from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich.syntax import Syntax
    from rich.status import Status
    from rich.rule import Rule
    from rich import box
    print("✅ Rich library imported successfully!")
except ImportError as e:
    print(f"❌ Rich library not available: {e}")
    sys.exit(1)


def test_basic_formatting():
    """Test basic rich formatting."""
    console = Console()
    
    # Header
    console.print(Rule("[bold blue]Rich Terminal Interface Test[/]"))
    console.print()
    
    # Basic messages
    console.print("[green]✓ Success message[/]")
    console.print("[red]✗ Error message[/]")
    console.print("[yellow]⚠ Warning message[/]")
    console.print("[blue]ℹ Info message[/]")
    console.print()
    
    # Table
    table = Table(title="System Status", box=box.ROUNDED)
    table.add_column("Component", style="cyan")
    table.add_column("Status", justify="center")
    table.add_column("Version")
    
    table.add_row("Rich Formatter", "[green]✅ Active[/]", "13.7.0")
    table.add_row("Progress Tracker", "[green]✅ Active[/]", "1.0.0")
    table.add_row("CLI Utils", "[yellow]⚠ Beta[/]", "0.9.0")
    
    console.print(table)
    console.print()
    
    # Code block
    code = '''
def hello_rich():
    """Demo function with rich formatting."""
    console = Console()
    console.print("[bold green]Hello, Rich![/]")
    return True
    '''
    
    syntax = Syntax(code, "python", theme="monokai", line_numbers=True)
    panel = Panel(syntax, title="Python Code Example", border_style="blue")
    console.print(panel)
    console.print()
    
    # Panel with content
    content = """
[bold]Rich Terminal Features:[/]
• [green]Color-coded output[/]
• [blue]Progress bars and spinners[/]
• [cyan]Formatted tables[/]
• [magenta]Syntax highlighting[/]
• [yellow]Interactive prompts[/]
    """
    
    panel = Panel(content, title="Features", border_style="green", box=box.DOUBLE)
    console.print(panel)
    console.print()


def test_progress_indicators():
    """Test progress indicators."""
    console = Console()
    
    console.print(Rule("[bold blue]Progress Indicators Test[/]"))
    console.print()
    
    # Status spinner
    with console.status("[blue]Loading configuration...[/]", spinner="dots"):
        time.sleep(2)
    
    console.print("[green]✓ Configuration loaded[/]")
    console.print()
    
    # Progress bar
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        
        # Multiple tasks
        tasks = [
            ("Downloading files", 100),
            ("Processing data", 50),
            ("Generating report", 25)
        ]
        
        task_ids = []
        for desc, total in tasks:
            task_id = progress.add_task(desc, total=total)
            task_ids.append(task_id)
        
        # Simulate work
        for i, (task_id, (desc, total)) in enumerate(zip(task_ids, tasks)):
            for completed in range(0, total + 1, 5):
                progress.update(task_id, completed=completed)
                time.sleep(0.05)
            
            progress.update(task_id, description=f"✓ {desc}")
    
    console.print()
    console.print("[green]✅ All tasks completed successfully![/]")
    console.print()


def test_interactive_elements():
    """Test interactive elements."""
    console = Console()
    
    console.print(Rule("[bold blue]Interactive Elements Test[/]"))
    console.print()
    
    # Centered text
    console.print(Text("Welcome to Rich Terminal Interface", style="bold magenta", justify="center"))
    console.print()
    
    # Multiple columns layout
    from rich.columns import Columns
    from rich.align import Align
    
    panels = [
        Panel("Left panel\ncontent here", title="Panel 1", border_style="red"),
        Panel("Middle panel\nwith more content", title="Panel 2", border_style="green"),
        Panel("Right panel\nfinal content", title="Panel 3", border_style="blue")
    ]
    
    console.print(Columns(panels, equal=True))
    console.print()
    
    # Tree structure
    from rich.tree import Tree
    
    tree = Tree("🚀 Rich Terminal Features")
    
    formatting = tree.add("🎨 Formatting")
    formatting.add("Colors and styles")
    formatting.add("Tables and panels")
    formatting.add("Code highlighting")
    
    progress = tree.add("📊 Progress Tracking")
    progress.add("Progress bars")
    progress.add("Spinners")
    progress.add("Multi-task tracking")
    
    interactive = tree.add("🖱️ Interactive")
    interactive.add("Prompts")
    interactive.add("Menus")
    interactive.add("Confirmations")
    
    console.print(tree)
    console.print()


def test_error_handling():
    """Test error handling and actionable messages."""
    console = Console()
    
    console.print(Rule("[bold blue]Error Handling Test[/]"))
    console.print()
    
    # Error message
    console.print("[red]✗ Connection failed to remote server[/]")
    console.print()
    
    # Actionable suggestions
    suggestions = [
        "Check your internet connection",
        "Verify the server URL is correct",
        "Ensure firewall allows outbound connections",
        "Try using a different network"
    ]
    
    suggestions_text = "\n".join([f"[yellow]•[/] {suggestion}" for suggestion in suggestions])
    
    panel = Panel(
        suggestions_text,
        title="[bold yellow]Suggested Actions[/]",
        border_style="yellow",
        box=box.ROUNDED
    )
    console.print(panel)
    console.print()
    
    # Documentation link
    console.print("[dim]📖 Documentation: https://docs.example.com/troubleshooting[/]")
    console.print()


def main():
    """Run all tests."""
    console = Console()
    
    try:
        # Main header
        console.print()
        console.print(Panel(
            "[bold blue]Rich Terminal Interface Test Suite[/]\n"
            "[dim]Testing enhanced CLI capabilities[/]",
            box=box.DOUBLE,
            border_style="blue"
        ))
        console.print()
        
        # Run tests
        tests = [
            ("Basic Formatting", test_basic_formatting),
            ("Progress Indicators", test_progress_indicators),
            ("Interactive Elements", test_interactive_elements),
            ("Error Handling", test_error_handling)
        ]
        
        for i, (name, test_func) in enumerate(tests, 1):
            console.print(f"[blue]Running test {i}/{len(tests)}: {name}[/]")
            
            try:
                test_func()
                console.print(f"[green]✅ {name} test passed[/]")
            except Exception as e:
                console.print(f"[red]❌ {name} test failed: {str(e)}[/]")
            
            if i < len(tests):
                console.print(Rule(style="dim"))
        
        # Final summary
        console.print()
        console.print(Panel(
            "[bold green]🎉 All tests completed![/]\n"
            "[dim]Rich terminal interface is working correctly[/]",
            box=box.DOUBLE,
            border_style="green"
        ))
        
        # Feature summary
        console.print()
        console.print("[bold]✨ Enhanced CLI Features Available:[/]")
        features = [
            "Color-coded output for different message types",
            "Progress bars for long-running operations", 
            "Formatted tables for structured data",
            "Syntax highlighting for code snippets",
            "Interactive panels and layouts",
            "Tree structures for hierarchical data",
            "Status indicators and spinners",
            "Actionable error reporting"
        ]
        
        for feature in features:
            console.print(f"  [green]•[/] {feature}")
        
        console.print()
        console.print("[bold blue]🚀 Ready to enhance your CLI experience![/]")
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Test interrupted by user[/]")
    except Exception as e:
        console.print(f"\n[red]Test suite failed: {str(e)}[/]")
        raise


if __name__ == "__main__":
    main()
