#!/usr/bin/env python3
"""
Simple test for shared utilities to validate our implementation.

This test validates the shared utilities we've created without
requiring the full framework integration.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_code_analysis_utils():
    """Test code analysis utilities."""
    print("🔄 Testing code analysis utilities...")
    
    try:
        from agent_framework.utils.code_analysis_utils import (
            calculate_complexity, analyze_code_quality, extract_functions
        )
        
        # Test with simple code
        test_code = '''
def hello_world():
    """A simple function."""
    if True:
        print("Hello, World!")
    return "done"

class TestClass:
    def method1(self):
        for i in range(10):
            if i % 2 == 0:
                print(i)
'''
        
        # Test complexity calculation
        complexity_result = calculate_complexity(test_code)
        assert "total_complexity" in complexity_result
        assert complexity_result["total_complexity"] > 0
        print("✅ Complexity calculation works")
        
        # Test quality analysis
        quality_metrics = analyze_code_quality(test_code)
        assert hasattr(quality_metrics, 'complexity')
        assert hasattr(quality_metrics, 'lines_of_code')
        assert quality_metrics.lines_of_code > 0
        print("✅ Quality analysis works")
        
        # Test function extraction
        functions = extract_functions(test_code)
        assert len(functions) >= 1
        assert any(func.name == "hello_world" for func in functions)
        print("✅ Function extraction works")
        
        return True
        
    except Exception as e:
        print(f"❌ Code analysis utils test failed: {e}")
        return False


def test_file_utils():
    """Test file utilities."""
    print("🔄 Testing file utilities...")
    
    try:
        from agent_framework.utils.file_utils import (
            read_file_safe, write_file_safe, ensure_directory, create_temp_file
        )
        
        # Test temp file creation
        temp_file = create_temp_file(content="test content")
        assert temp_file is not None
        assert temp_file.exists()
        print("✅ Temp file creation works")
        
        # Test file reading
        content = read_file_safe(temp_file)
        assert content == "test content"
        print("✅ File reading works")
        
        # Test directory creation
        test_dir = temp_file.parent / "test_dir"
        assert ensure_directory(test_dir)
        assert test_dir.exists()
        print("✅ Directory creation works")
        
        # Cleanup
        temp_file.unlink()
        test_dir.rmdir()
        
        return True
        
    except Exception as e:
        print(f"❌ File utils test failed: {e}")
        return False


def test_validation_utils():
    """Test validation utilities."""
    print("🔄 Testing validation utilities...")
    
    try:
        from agent_framework.utils.validation_utils import (
            validate_python_syntax, validate_email, validate_url
        )
        
        # Test Python syntax validation
        valid_code = "def test(): pass"
        invalid_code = "def test( pass"
        
        valid_result = validate_python_syntax(valid_code)
        assert valid_result["is_valid"] == True
        print("✅ Valid Python syntax detection works")
        
        invalid_result = validate_python_syntax(invalid_code)
        assert invalid_result["is_valid"] == False
        assert invalid_result["error"] is not None
        print("✅ Invalid Python syntax detection works")
        
        # Test email validation
        assert validate_email("<EMAIL>") == True
        assert validate_email("invalid-email") == False
        print("✅ Email validation works")
        
        # Test URL validation
        assert validate_url("https://example.com") == True
        assert validate_url("not-a-url") == False
        print("✅ URL validation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation utils test failed: {e}")
        return False


def test_data_models():
    """Test unified data models."""
    print("🔄 Testing data models...")
    
    try:
        from agent_framework.shared.data_models import (
            UnifiedTask, StandardResult, CapabilityModel, ContextModel,
            TaskPriority, TaskStatus, CapabilityType
        )
        
        # Test UnifiedTask
        task = UnifiedTask(
            name="Test Task",
            task_type="test",
            priority=TaskPriority.HIGH,
            parameters={"key": "value"}
        )
        assert task.name == "Test Task"
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.PENDING
        print("✅ UnifiedTask creation works")
        
        # Test StandardResult
        success_result = StandardResult.success_result("test result")
        assert success_result.success == True
        assert success_result.result == "test result"
        print("✅ StandardResult creation works")
        
        error_result = StandardResult.error_result("test error")
        assert error_result.success == False
        assert error_result.error == "test error"
        print("✅ StandardResult error handling works")
        
        # Test CapabilityModel
        capability = CapabilityModel(
            name="test_capability",
            capability_type=CapabilityType.CODE_ANALYSIS,
            description="Test capability"
        )
        assert capability.name == "test_capability"
        assert capability.capability_type == CapabilityType.CODE_ANALYSIS
        print("✅ CapabilityModel creation works")
        
        # Test ContextModel
        context = ContextModel(
            context_type="test",
            data={"key": "value"}
        )
        context.update_data("new_key", "new_value")
        assert context.get_data("new_key") == "new_value"
        print("✅ ContextModel operations work")
        
        return True
        
    except Exception as e:
        print(f"❌ Data models test failed: {e}")
        return False


def test_base_abstractions():
    """Test base abstractions."""
    print("🔄 Testing base abstractions...")
    
    try:
        from agent_framework.shared.base_validator import BaseValidator, ValidationResult
        from agent_framework.shared.base_metrics import BaseMetrics
        from agent_framework.shared.base_cache import BaseCache
        
        # Test BaseValidator
        class TestValidator(BaseValidator):
            async def validate(self, data):
                self.reset()
                if not data:
                    self.add_error("data", "Data is required")
                return self.get_result()
        
        validator = TestValidator("test")
        # Note: We can't easily test async methods in this simple test
        print("✅ BaseValidator instantiation works")
        
        # Test BaseMetrics
        metrics = BaseMetrics("test")
        metrics.record_counter("test_counter", 1.0)
        assert metrics.get_counter("test_counter") == 1.0
        print("✅ BaseMetrics basic operations work")
        
        # Test BaseCache
        cache = BaseCache("test", max_size=10)
        # Note: We can't easily test async methods in this simple test
        print("✅ BaseCache instantiation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Base abstractions test failed: {e}")
        return False


def main():
    """Run all utility tests."""
    print("🧪 Testing Shared Utilities Implementation")
    print("=" * 50)
    
    tests = [
        ("Code Analysis Utils", test_code_analysis_utils),
        ("File Utils", test_file_utils),
        ("Validation Utils", test_validation_utils),
        ("Data Models", test_data_models),
        ("Base Abstractions", test_base_abstractions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"💥 {test_name} - CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All utility tests passed! Shared infrastructure is working.")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed. Please review implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
