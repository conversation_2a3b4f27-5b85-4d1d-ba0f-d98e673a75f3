"""
Comprehensive test suite for security enhancements.

Tests all security components including input validation,
secrets management, and security monitoring.
"""

import pytest
import asyncio
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path

from agent_framework.security import (
    InputValidator, ValidationResult, SecurityConfig,
    SecretsManager, SecurityMonitor, SecurityManager,
    SecurityEventType, SecuritySeverity
)


class TestInputValidator:
    """Test cases for input validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = InputValidator()
    
    def test_valid_string_input(self):
        """Test validation of valid string input."""
        result = self.validator.validate_string("Hello, world!")
        assert result.is_valid
        assert result.sanitized_value == "Hello, world!"
        assert len(result.errors) == 0
    
    def test_string_length_validation(self):
        """Test string length validation."""
        long_string = "x" * 20000
        result = self.validator.validate_string(long_string)
        assert not result.is_valid
        assert "length exceeds maximum" in result.errors[0]
        assert result.severity == SecuritySeverity.HIGH
    
    def test_sql_injection_detection(self):
        """Test SQL injection detection."""
        malicious_input = "'; DROP TABLE users; --"
        result = self.validator.validate_string(malicious_input)
        assert not result.is_valid
        assert "SQL injection" in result.errors[0]
        assert result.severity == SecuritySeverity.CRITICAL
    
    def test_xss_detection(self):
        """Test XSS detection."""
        xss_input = "<script>alert('xss')</script>"
        result = self.validator.validate_string(xss_input)
        assert not result.is_valid
        assert "Blocked pattern detected" in result.errors[0]
        assert result.severity == SecuritySeverity.CRITICAL
    
    def test_command_injection_detection(self):
        """Test command injection detection."""
        command_input = "test; rm -rf /"
        result = self.validator.validate_string(command_input)
        assert not result.is_valid
        assert "command injection" in result.errors[0]
        assert result.severity == SecuritySeverity.CRITICAL
    
    def test_file_path_validation(self):
        """Test file path validation."""
        # Valid path
        result = self.validator.validate_file_path("test.txt")
        assert result.is_valid
        
        # Directory traversal
        result = self.validator.validate_file_path("../../../etc/passwd")
        assert not result.is_valid
        assert "Directory traversal" in result.errors[0]
        
        # Invalid extension
        result = self.validator.validate_file_path("malware.exe")
        assert not result.is_valid
        assert "File extension not allowed" in result.errors[0]
    
    def test_json_validation(self):
        """Test JSON validation."""
        # Valid JSON
        result = self.validator.validate_json('{"key": "value"}')
        assert result.is_valid
        assert result.sanitized_value == {"key": "value"}
        
        # Invalid JSON
        result = self.validator.validate_json('{"invalid": json}')
        assert not result.is_valid
        assert "Invalid JSON" in result.errors[0]
    
    def test_python_code_validation(self):
        """Test Python code validation."""
        # Safe code
        result = self.validator.validate_python_code("x = 1 + 2")
        assert result.is_valid
        
        # Dangerous code
        result = self.validator.validate_python_code("exec('malicious code')")
        assert not result.is_valid
        assert "Dangerous function detected" in result.errors[0]
        
        # Syntax error
        result = self.validator.validate_python_code("if True")
        assert not result.is_valid
        assert "Syntax error" in result.errors[0]
    
    def test_url_validation(self):
        """Test URL validation."""
        # Valid URLs
        assert self.validator.validate_url("https://example.com").is_valid
        assert self.validator.validate_url("http://localhost:8080").is_valid
        
        # Invalid URLs
        assert not self.validator.validate_url("javascript:alert('xss')").is_valid
        assert not self.validator.validate_url("not-a-url").is_valid


class TestSecretsManager:
    """Test cases for secrets management."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Use temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.close()
        self.secrets_manager = SecretsManager(
            storage_path=self.temp_file.name,
            master_key="test_master_key_for_testing_only",
            use_keyring=False
        )
    
    def teardown_method(self):
        """Clean up test fixtures."""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_store_and_retrieve_secret(self):
        """Test storing and retrieving secrets."""
        # Store secret
        success = self.secrets_manager.store_secret("test_key", "test_value")
        assert success
        
        # Retrieve secret
        value = self.secrets_manager.get_secret("test_key")
        assert value == "test_value"
    
    def test_secret_expiration(self):
        """Test secret expiration."""
        # Store secret with short expiration
        success = self.secrets_manager.store_secret(
            "expiring_key", 
            "expiring_value",
            expires_in_days=0  # Expires immediately
        )
        assert success
        
        # Should not be retrievable after expiration
        value = self.secrets_manager.get_secret("expiring_key")
        assert value is None
    
    def test_secret_rotation(self):
        """Test secret rotation."""
        # Store initial secret
        self.secrets_manager.store_secret("rotate_key", "old_value")
        
        # Rotate secret
        success = self.secrets_manager.rotate_secret("rotate_key", "new_value")
        assert success
        
        # Verify new value
        value = self.secrets_manager.get_secret("rotate_key")
        assert value == "new_value"
    
    def test_list_secrets(self):
        """Test listing secrets."""
        # Store multiple secrets
        self.secrets_manager.store_secret("key1", "value1")
        self.secrets_manager.store_secret("key2", "value2")
        
        # List secrets
        secrets = self.secrets_manager.list_secrets()
        assert "key1" in secrets
        assert "key2" in secrets
    
    def test_delete_secret(self):
        """Test deleting secrets."""
        # Store and delete secret
        self.secrets_manager.store_secret("delete_key", "delete_value")
        success = self.secrets_manager.delete_secret("delete_key")
        assert success
        
        # Verify deletion
        value = self.secrets_manager.get_secret("delete_key")
        assert value is None


class TestSecurityMonitor:
    """Test cases for security monitoring."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.monitor = SecurityMonitor()
    
    def teardown_method(self):
        """Clean up test fixtures."""
        asyncio.run(self.monitor.stop_monitoring())
    
    def test_log_security_event(self):
        """Test logging security events."""
        # Log an event
        self.monitor.log_event(
            SecurityEventType.AUTHENTICATION_FAILURE,
            SecuritySeverity.HIGH,
            "test_source",
            {"user": "test_user"},
            user_id="test_user",
            ip_address="***********"
        )
        
        # Verify event was logged
        events = self.monitor.get_events(limit=1)
        assert len(events) == 1
        assert events[0].event_type == SecurityEventType.AUTHENTICATION_FAILURE
        assert events[0].severity == SecuritySeverity.HIGH
        assert events[0].user_id == "test_user"
    
    def test_threat_detection(self):
        """Test threat indicator detection."""
        alert_triggered = False
        
        def alert_callback(alert_data):
            nonlocal alert_triggered
            alert_triggered = True
        
        self.monitor.add_alert_callback(alert_callback)
        
        # Log event with SQL injection pattern
        self.monitor.log_event(
            SecurityEventType.INVALID_INPUT,
            SecuritySeverity.MEDIUM,
            "test_source",
            {"input": "SELECT * FROM users WHERE id = 1 OR 1=1"}
        )
        
        # Verify alert was triggered
        assert alert_triggered
    
    def test_rate_limiting(self):
        """Test rate limiting detection."""
        alert_triggered = False
        
        def alert_callback(alert_data):
            nonlocal alert_triggered
            if "rate limit" in alert_data.get("message", "").lower():
                alert_triggered = True
        
        self.monitor.add_alert_callback(alert_callback)
        
        # Generate many events from same IP
        for i in range(15):  # Exceed default threshold of 10
            self.monitor.log_event(
                SecurityEventType.AUTHENTICATION_FAILURE,
                SecuritySeverity.LOW,
                "test_source",
                ip_address="***********00"
            )
        
        # Verify rate limit alert was triggered
        assert alert_triggered
    
    def test_event_filtering(self):
        """Test event filtering."""
        # Log events of different types
        self.monitor.log_event(
            SecurityEventType.AUTHENTICATION_FAILURE,
            SecuritySeverity.HIGH,
            "test_source"
        )
        self.monitor.log_event(
            SecurityEventType.INVALID_INPUT,
            SecuritySeverity.MEDIUM,
            "test_source"
        )
        
        # Filter by event type
        auth_events = self.monitor.get_events(
            event_type=SecurityEventType.AUTHENTICATION_FAILURE
        )
        assert len(auth_events) == 1
        assert auth_events[0].event_type == SecurityEventType.AUTHENTICATION_FAILURE
        
        # Filter by severity
        high_events = self.monitor.get_events(severity=SecuritySeverity.HIGH)
        assert len(high_events) == 1
        assert high_events[0].severity == SecuritySeverity.HIGH
    
    @pytest.mark.asyncio
    async def test_monitoring_lifecycle(self):
        """Test monitoring start/stop lifecycle."""
        # Start monitoring
        await self.monitor.start_monitoring()
        assert self.monitor._is_monitoring
        
        # Stop monitoring
        await self.monitor.stop_monitoring()
        assert not self.monitor._is_monitoring


class TestSecurityManager:
    """Test cases for integrated security manager."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.close()
        
        self.security_manager = SecurityManager(
            secrets_storage_path=self.temp_file.name
        )
    
    def teardown_method(self):
        """Clean up test fixtures."""
        asyncio.run(self.security_manager.stop())
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_integrated_validation_and_monitoring(self):
        """Test integrated validation and monitoring."""
        # Validate malicious input
        result = self.security_manager.validate_and_monitor(
            "'; DROP TABLE users; --",
            input_type="string",
            source="test_source",
            user_id="test_user",
            ip_address="***********"
        )
        
        # Verify validation failed
        assert not result.is_valid
        
        # Verify security event was logged
        events = self.security_manager.monitor.get_events(limit=1)
        assert len(events) == 1
        assert events[0].event_type == SecurityEventType.INVALID_INPUT
    
    def test_secure_secret_operations(self):
        """Test secure secret operations with logging."""
        # Store secret securely
        success = self.security_manager.store_secret_secure(
            "test_secret",
            "secret_value",
            source="test_source",
            user_id="test_user"
        )
        assert success
        
        # Retrieve secret securely
        value = self.security_manager.get_secret_secure(
            "test_secret",
            source="test_source",
            user_id="test_user"
        )
        assert value == "secret_value"
        
        # Verify access events were logged
        events = self.security_manager.monitor.get_events(
            event_type=SecurityEventType.DATA_ACCESS
        )
        assert len(events) == 2  # Store and retrieve
    
    @pytest.mark.asyncio
    async def test_security_manager_lifecycle(self):
        """Test security manager lifecycle."""
        # Start security services
        await self.security_manager.start()
        assert self.security_manager.monitor._is_monitoring
        
        # Stop security services
        await self.security_manager.stop()
        assert not self.security_manager.monitor._is_monitoring


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
