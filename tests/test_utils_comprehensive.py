"""
Comprehensive tests for utility modules to improve test coverage.

This module contains tests for various utility functions that were previously
under-tested, focusing on edge cases and error conditions.
"""

import pytest
import asyncio
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from agent_framework.utils.serialization_utils import (
    serialize_object, deserialize_object, safe_json_loads, safe_json_dumps,
    encode_base64, decode_base64, serialize_dict_to_json_file,
    deserialize_dict_from_json_file
)
from agent_framework.utils.testing_utils import (
    create_temp_file, create_temp_dir, cleanup_temp_path,
    TempFileContext, TempDirContext, mock_async_function,
    assert_eventually, time_function, create_mock_file_system,
    MockLogger
)
from agent_framework.utils.validation_utils import (
    validate_email, validate_url, validate_json_schema,
    validate_file_path, validate_port_number, validate_ip_address,
    validate_regex_pattern, sanitize_input, validate_python_syntax,
    validate_identifier, validate_version, validate_json_string
)
from agent_framework.utils.file_utils import (
    ensure_directory, read_file_safe, write_file_safe,
    copy_file, move_file, delete_file,
    get_file_info, get_file_hash, find_files,
    backup_file, create_temp_file, create_temp_directory
)


class TestSerializationUtils:
    """Test serialization utility functions."""
    
    def test_serialize_deserialize_json(self):
        """Test JSON serialization and deserialization."""
        test_data = {"key": "value", "number": 42, "list": [1, 2, 3]}
        
        # Test serialization
        serialized = serialize_object(test_data, "json")
        assert isinstance(serialized, str)
        
        # Test deserialization
        deserialized = deserialize_object(serialized, "json")
        assert deserialized == test_data
    
    def test_serialize_deserialize_pickle(self):
        """Test pickle serialization and deserialization."""
        test_data = {"key": "value", "complex": set([1, 2, 3])}
        
        # Test serialization
        serialized = serialize_object(test_data, "pickle")
        assert isinstance(serialized, str)
        
        # Test deserialization
        deserialized = deserialize_object(serialized, "pickle")
        assert deserialized == test_data
    
    def test_serialize_invalid_method(self):
        """Test serialization with invalid method."""
        with pytest.raises(ValueError, match="Unsupported serialization method"):
            serialize_object({"key": "value"}, "invalid")
    
    def test_deserialize_invalid_method(self):
        """Test deserialization with invalid method."""
        with pytest.raises(ValueError, match="Unsupported deserialization method"):
            deserialize_object("data", "invalid")
    
    def test_safe_json_loads_valid(self):
        """Test safe JSON loading with valid data."""
        json_str = '{"key": "value"}'
        result = safe_json_loads(json_str)
        assert result == {"key": "value"}
    
    def test_safe_json_loads_invalid(self):
        """Test safe JSON loading with invalid data."""
        result = safe_json_loads("invalid json", default={"error": True})
        assert result == {"error": True}
    
    def test_safe_json_dumps_valid(self):
        """Test safe JSON dumping with valid data."""
        data = {"key": "value"}
        result = safe_json_dumps(data)
        assert '"key": "value"' in result
    
    def test_safe_json_dumps_with_indent(self):
        """Test safe JSON dumping with indentation."""
        data = {"key": "value"}
        result = safe_json_dumps(data, indent=2)
        assert '\n' in result  # Indented JSON has newlines
    
    def test_encode_decode_base64(self):
        """Test base64 encoding and decoding."""
        test_string = "Hello, World!"
        
        # Test encoding
        encoded = encode_base64(test_string)
        assert isinstance(encoded, str)
        
        # Test decoding
        decoded = decode_base64(encoded)
        assert decoded.decode('utf-8') == test_string
    
    def test_encode_base64_bytes(self):
        """Test base64 encoding with bytes input."""
        test_bytes = b"Hello, World!"
        encoded = encode_base64(test_bytes)
        decoded = decode_base64(encoded)
        assert decoded == test_bytes
    
    def test_decode_base64_invalid(self):
        """Test base64 decoding with invalid data."""
        with pytest.raises(ValueError, match="Base64 decoding failed"):
            decode_base64("invalid base64!")
    
    def test_serialize_dict_to_json_file(self):
        """Test serializing dictionary to JSON file."""
        test_data = {"key": "value", "number": 42}
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            filepath = f.name
        
        try:
            serialize_dict_to_json_file(test_data, filepath)
            
            # Verify file was created and contains correct data
            with open(filepath, 'r') as f:
                loaded_data = json.load(f)
            assert loaded_data == test_data
        finally:
            os.unlink(filepath)
    
    def test_deserialize_dict_from_json_file(self):
        """Test deserializing dictionary from JSON file."""
        test_data = {"key": "value", "number": 42}
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            json.dump(test_data, f)
            filepath = f.name
        
        try:
            loaded_data = deserialize_dict_from_json_file(filepath)
            assert loaded_data == test_data
        finally:
            os.unlink(filepath)
    
    def test_serialize_dict_to_json_file_error(self):
        """Test serializing to JSON file with error."""
        with pytest.raises(ValueError, match="Failed to write JSON file"):
            serialize_dict_to_json_file({"key": "value"}, "/invalid/path/file.json")
    
    def test_deserialize_dict_from_json_file_error(self):
        """Test deserializing from non-existent JSON file."""
        with pytest.raises(ValueError, match="Failed to read JSON file"):
            deserialize_dict_from_json_file("/non/existent/file.json")


class TestTestingUtils:
    """Test testing utility functions."""
    
    def test_create_temp_file(self):
        """Test temporary file creation."""
        content = "test content"
        filepath = create_temp_file(suffix=".txt", prefix="test_", content=content)
        
        try:
            assert os.path.exists(filepath)
            assert str(filepath).endswith(".txt")
            assert "test_" in filepath.name
            
            with open(filepath, 'r') as f:
                assert f.read() == content
        finally:
            cleanup_temp_path(str(filepath))
    
    def test_create_temp_dir(self):
        """Test temporary directory creation."""
        dirpath = create_temp_dir(prefix="test_")
        
        try:
            assert os.path.exists(dirpath)
            assert os.path.isdir(dirpath)
            assert "test_" in os.path.basename(dirpath)
        finally:
            cleanup_temp_path(dirpath)
    
    def test_temp_file_context(self):
        """Test temporary file context manager."""
        content = "test content"
        
        with TempFileContext(content, suffix=".txt") as filepath:
            assert os.path.exists(filepath)
            with open(filepath, 'r') as f:
                assert f.read() == content
        
        # File should be cleaned up after context
        assert not os.path.exists(filepath)
    
    def test_temp_dir_context(self):
        """Test temporary directory context manager."""
        with TempDirContext(prefix="test_") as dirpath:
            assert os.path.exists(dirpath)
            assert os.path.isdir(dirpath)
            
            # Create a file in the directory
            test_file = os.path.join(dirpath, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
        
        # Directory should be cleaned up after context
        assert not os.path.exists(dirpath)
    
    def test_mock_async_function(self):
        """Test async function mocking."""
        mock_func = mock_async_function(return_value="test_result")
        
        # Test that it's an AsyncMock
        assert isinstance(mock_func, AsyncMock)
        assert mock_func.return_value == "test_result"
    
    def test_mock_async_function_with_side_effect(self):
        """Test async function mocking with side effect."""
        def side_effect():
            return "side_effect_result"
        
        mock_func = mock_async_function(side_effect=side_effect)
        assert mock_func.side_effect == side_effect
    
    @pytest.mark.asyncio
    async def test_assert_eventually_success(self):
        """Test assert_eventually with successful condition."""
        counter = 0
        
        def condition():
            nonlocal counter
            counter += 1
            return counter >= 3
        
        # Should not raise an exception
        await assert_eventually(condition, timeout=1.0, interval=0.1)
        assert counter >= 3
    
    @pytest.mark.asyncio
    async def test_assert_eventually_timeout(self):
        """Test assert_eventually with timeout."""
        def condition():
            return False  # Never true
        
        with pytest.raises(AssertionError, match="Condition was not met within timeout"):
            await assert_eventually(condition, timeout=0.2, interval=0.1)
    
    def test_time_function_decorator(self):
        """Test function timing decorator."""
        @time_function
        def test_func():
            return "result"
        
        result = test_func()
        assert result == "result"
    
    def test_create_mock_file_system(self):
        """Test mock file system creation."""
        with TempDirContext() as temp_dir:
            structure = {
                "file1.txt": "content1",
                "subdir": {
                    "file2.txt": "content2",
                    "file3.py": "print('hello')"
                }
            }
            
            create_mock_file_system(structure, temp_dir)
            
            # Verify files were created
            assert os.path.exists(os.path.join(temp_dir, "file1.txt"))
            assert os.path.exists(os.path.join(temp_dir, "subdir", "file2.txt"))
            assert os.path.exists(os.path.join(temp_dir, "subdir", "file3.py"))
            
            # Verify content
            with open(os.path.join(temp_dir, "file1.txt"), 'r') as f:
                assert f.read() == "content1"
    
    def test_mock_logger(self):
        """Test mock logger functionality."""
        logger = MockLogger()
        
        logger.debug("debug message")
        logger.info("info message")
        logger.warning("warning message")
        logger.error("error message")
        logger.critical("critical message")
        
        messages = logger.get_messages()
        assert len(messages) == 5
        assert messages[0] == ('DEBUG', 'debug message')
        assert messages[1] == ('INFO', 'info message')
        
        # Test filtering by level
        error_messages = logger.get_messages('ERROR')
        assert error_messages == ['error message']
        
        # Test clearing
        logger.clear()
        assert len(logger.get_messages()) == 0


class TestValidationUtils:
    """Test validation utility functions."""

    def test_validate_email_valid(self):
        """Test email validation with valid emails."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        for email in valid_emails:
            assert validate_email(email) is True

    def test_validate_email_invalid(self):
        """Test email validation with invalid emails."""
        invalid_emails = [
            "invalid-email",
            "@example.com",
            "test@"
        ]

        for email in invalid_emails:
            assert validate_email(email) is False

    def test_validate_url_valid(self):
        """Test URL validation with valid URLs."""
        valid_urls = [
            "https://example.com",
            "http://localhost:8080",
            "https://api.example.com/v1/endpoint"
        ]

        for url in valid_urls:
            assert validate_url(url) is True

    def test_validate_url_invalid(self):
        """Test URL validation with invalid URLs."""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",  # Only http/https allowed
            "https://",
            ""
        ]

        for url in invalid_urls:
            assert validate_url(url) is False

    def test_validate_port_number_valid(self):
        """Test port number validation with valid ports."""
        valid_ports = [80, 443, 8080, 3000, 65535]

        for port in valid_ports:
            assert validate_port_number(port) is True

    def test_validate_port_number_invalid(self):
        """Test port number validation with invalid ports."""
        invalid_ports = [0, -1, 65536, 100000]

        for port in invalid_ports:
            assert validate_port_number(port) is False

    def test_validate_ip_address_valid(self):
        """Test IP address validation with valid IPs."""
        valid_ips = [
            "***********",
            "127.0.0.1",
            "********",
            "***************"
        ]

        for ip in valid_ips:
            result = validate_ip_address(ip)
            assert result["is_valid"] is True

    def test_validate_ip_address_invalid(self):
        """Test IP address validation with invalid IPs."""
        invalid_ips = [
            "256.1.1.1",
            "192.168.1",
            "not-an-ip",
            "***********.1"
        ]

        for ip in invalid_ips:
            result = validate_ip_address(ip)
            assert result["is_valid"] is False

    def test_sanitize_input(self):
        """Test input sanitization."""
        # Test basic sanitization
        result = sanitize_input("test input", max_length=10)
        assert len(result) <= 10

        # Test with allowed characters
        result = sanitize_input("test123!@#", allowed_chars="abcdefghijklmnopqrstuvwxyz0123456789")
        assert result == "test123"

    def test_validate_file_path_existing(self):
        """Test file path validation with existing file."""
        with TempFileContext("test content") as filepath:
            result = validate_file_path(filepath, must_exist=True)
            assert result["is_valid"] is True

    def test_validate_file_path_non_existing(self):
        """Test file path validation with non-existing file."""
        result = validate_file_path("/non/existent/file.txt", must_exist=True)
        assert result["is_valid"] is False

    def test_validate_python_syntax_valid(self):
        """Test Python syntax validation with valid code."""
        code = "def hello():\n    print('Hello, World!')"
        result = validate_python_syntax(code)
        assert result["is_valid"] is True
        assert result["error"] is None

    def test_validate_python_syntax_invalid(self):
        """Test Python syntax validation with invalid code."""
        code = "def hello(\n    print('Hello, World!')"  # Missing closing parenthesis
        result = validate_python_syntax(code)
        assert result["is_valid"] is False
        assert result["error"] is not None

    def test_validate_json_string_valid(self):
        """Test JSON string validation with valid JSON."""
        json_str = '{"key": "value", "number": 42}'
        result = validate_json_string(json_str)
        assert result["is_valid"] is True

    def test_validate_json_string_invalid(self):
        """Test JSON string validation with invalid JSON."""
        json_str = '{"key": "value", "number": 42'  # Missing closing brace
        result = validate_json_string(json_str)
        assert result["is_valid"] is False

    def test_validate_regex_pattern_valid(self):
        """Test regex pattern validation with valid pattern."""
        pattern = r"^[a-zA-Z0-9]+$"
        result = validate_regex_pattern(pattern)
        assert result["is_valid"] is True

    def test_validate_regex_pattern_invalid(self):
        """Test regex pattern validation with invalid pattern."""
        pattern = r"[unclosed"  # Invalid regex
        result = validate_regex_pattern(pattern)
        assert result["is_valid"] is False


class TestFileUtils:
    """Test file utility functions."""

    def test_ensure_directory(self):
        """Test directory creation."""
        with TempDirContext() as temp_dir:
            test_dir = os.path.join(temp_dir, "new_directory")

            # Directory shouldn't exist initially
            assert not os.path.exists(test_dir)

            # Create directory
            success = ensure_directory(test_dir)
            assert success is True
            assert os.path.exists(test_dir)
            assert os.path.isdir(test_dir)

    def test_read_file_safe(self):
        """Test safe file reading."""
        content = "test file content"
        with TempFileContext(content) as filepath:
            result = read_file_safe(filepath)
            assert result == content

    def test_read_file_safe_non_existent(self):
        """Test safe file reading with non-existent file."""
        result = read_file_safe("/non/existent/file.txt")
        assert result is None

    def test_write_file_safe(self):
        """Test safe file writing."""
        content = "new content"
        with TempDirContext() as temp_dir:
            filepath = os.path.join(temp_dir, "test_file.txt")

            success = write_file_safe(filepath, content)
            assert success is True

            # Verify content was written
            with open(filepath, 'r') as f:
                assert f.read() == content

    def test_write_file_safe_invalid_path(self):
        """Test safe file writing with invalid path."""
        success = write_file_safe("/invalid/path/file.txt", "content")
        assert success is False

    def test_copy_file(self):
        """Test file copying."""
        content = "file to copy"
        with TempFileContext(content) as source_file:
            with TempDirContext() as temp_dir:
                dest_file = os.path.join(temp_dir, "copied_file.txt")

                success = copy_file(source_file, dest_file)
                assert success is True

                # Verify file was copied
                assert os.path.exists(dest_file)
                with open(dest_file, 'r') as f:
                    assert f.read() == content

    def test_copy_file_non_existent_source(self):
        """Test file copying with non-existent source."""
        success = copy_file("/non/existent/source.txt", "/tmp/dest.txt")
        assert success is False

    def test_get_file_info(self):
        """Test getting file information."""
        content = "test content for info"
        with TempFileContext(content) as filepath:
            info = get_file_info(filepath)
            assert info is not None
            assert info["size_bytes"] == len(content.encode('utf-8'))
            assert "modified_at" in info
            assert "created_at" in info

    def test_get_file_info_non_existent(self):
        """Test getting file info for non-existent file."""
        info = get_file_info("/non/existent/file.txt")
        assert info is None

    def test_get_file_hash(self):
        """Test getting file hash."""
        content = "test content for hash"
        with TempFileContext(content) as filepath:
            hash_value = get_file_hash(filepath)
            assert hash_value is not None
            assert len(hash_value) == 32  # MD5 hash length

    def test_get_file_hash_non_existent(self):
        """Test getting file hash for non-existent file."""
        hash_value = get_file_hash("/non/existent/file.txt")
        assert hash_value is None

    def test_find_files(self):
        """Test file finding functionality."""
        with TempDirContext() as temp_dir:
            # Create test file structure
            structure = {
                "file1.txt": "content1",
                "subdir": {
                    "file2.txt": "content2",
                    "file3.py": "content3"
                }
            }
            create_mock_file_system(structure, temp_dir)

            # Find all files
            files = find_files(temp_dir, "*", recursive=True)
            filenames = [f.name for f in files]

            assert "file1.txt" in filenames
            assert "file2.txt" in filenames
            assert "file3.py" in filenames

    def test_find_files_by_pattern(self):
        """Test finding files by pattern."""
        with TempDirContext() as temp_dir:
            # Create test files
            structure = {
                "test1.txt": "content",
                "test2.py": "content",
                "other.log": "content"
            }
            create_mock_file_system(structure, temp_dir)

            # Find .txt files
            txt_files = find_files(temp_dir, "*.txt")
            assert len(txt_files) == 1
            assert txt_files[0].name == "test1.txt"

    def test_backup_file(self):
        """Test file backup functionality."""
        content = "file to backup"
        with TempFileContext(content) as filepath:
            backup_path = backup_file(filepath)
            assert backup_path is not None
            assert os.path.exists(backup_path)

            # Verify backup content
            with open(backup_path, 'r') as f:
                assert f.read() == content

    def test_create_temp_file(self):
        """Test temporary file creation."""
        temp_file = create_temp_file(suffix=".txt", content="temp content")
        assert temp_file is not None
        assert os.path.exists(temp_file)
        assert str(temp_file).endswith(".txt")

        # Clean up
        os.unlink(temp_file)

    def test_create_temp_directory(self):
        """Test temporary directory creation."""
        temp_dir = create_temp_directory(prefix="test_")
        assert temp_dir is not None
        assert os.path.exists(temp_dir)
        assert os.path.isdir(temp_dir)

        # Clean up
        os.rmdir(temp_dir)
