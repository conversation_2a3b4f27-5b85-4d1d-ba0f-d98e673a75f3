#!/usr/bin/env python3
"""
Simple integration test for enhanced capabilities.
Tests basic functionality without complex async operations.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/agent-test')

def test_imports():
    """Test that all enhanced components can be imported."""
    print("🔧 Testing Enhanced Component Imports...")
    
    try:
        from agent_framework.core.agent_orchestrator import (
            AdvancedAgentOrchestrator, AgentCapabilities
        )
        print("✓ AdvancedAgentOrchestrator imported successfully")
        
        from agent_framework.core.automatic_bug_fix_loop import AutomaticBugFixLoop
        print("✓ AutomaticBugFixLoop imported successfully")
        
        from agent_framework.core.automatic_evaluation_cycles import AutomaticEvaluationCycles
        print("✓ AutomaticEvaluationCycles imported successfully")
        
        from agent_framework.cli.commands.enhance import EnhanceCommand
        from agent_framework.cli.commands.debug import DebugCommand
        from agent_framework.cli.commands.analyze import AnalyzeCommand
        print("✓ Advanced CLI commands imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test advanced capabilities configuration."""
    print("\n🔧 Testing Advanced Capabilities Configuration...")

    try:
        from agent_framework.core.agent_orchestrator import AgentCapabilities

        # Test default configuration
        default_caps = AgentCapabilities()
        print(f"✓ Default capabilities created")
        print(f"  - Bug fixing enabled: {default_caps.enable_automatic_bug_fixing}")
        print(f"  - Evaluation enabled: {default_caps.enable_automatic_evaluation}")
        print(f"  - Code generation enabled: {default_caps.enable_advanced_code_generation}")
        print(f"  - Testing enabled: {default_caps.enable_comprehensive_testing}")
        print(f"  - Max iterations: {default_caps.max_fix_iterations}")
        
        # Test custom configuration
        custom_caps = AgentCapabilities(
            enable_automatic_bug_fixing=True,
            enable_automatic_evaluation=True,
            enable_enhanced_code_generation=True,
            enable_comprehensive_testing=False,
            max_fix_iterations=3,
            evaluation_on_every_change=False,
            rollback_on_critical_issues=True
        )
        print(f"✓ Custom capabilities created")
        print(f"  - Max iterations: {custom_caps.max_fix_iterations}")
        print(f"  - Evaluation on change: {custom_caps.evaluation_on_every_change}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_orchestrator_creation():
    """Test enhanced orchestrator creation."""
    print("\n🔧 Testing Enhanced Orchestrator Creation...")
    
    try:
        from agent_framework.core.agent_orchestrator import (
            AdvancedAgentOrchestrator, AgentCapabilities
        )

        capabilities = AgentCapabilities(
            enable_advanced_code_generation=True,
            enable_comprehensive_testing=True,
            enable_automatic_bug_fixing=True,
            enable_automatic_evaluation=True,
            max_fix_iterations=2,
            evaluation_on_every_change=True,
            rollback_on_critical_issues=True
        )

        orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
        print("✓ Advanced orchestrator created successfully")
        
        # Test that components are initialized
        print(f"✓ Bug fix loop available: {orchestrator.bug_fix_loop is not None}")
        print(f"✓ Evaluation cycles available: {orchestrator.evaluation_cycles is not None}")
        print(f"✓ Capabilities configured: {orchestrator.capabilities is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator creation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_commands():
    """Test CLI command creation and help text."""
    print("\n🔧 Testing CLI Commands...")
    
    try:
        from agent_framework.cli.commands.enhance import EnhanceCommand
        from agent_framework.cli.commands.debug import DebugCommand
        from agent_framework.cli.commands.analyze import AnalyzeCommand
        
        # Test command creation
        enhance_cmd = EnhanceCommand()
        debug_cmd = DebugCommand()
        analyze_cmd = AnalyzeCommand()
        
        print(f"✓ Commands created successfully")
        print(f"  - Enhance: {enhance_cmd.name}")
        print(f"  - Debug: {debug_cmd.name}")
        print(f"  - Analyze: {analyze_cmd.name}")
        
        # Test help text generation
        enhance_help = enhance_cmd.get_help_text()
        debug_help = debug_cmd.get_help_text()
        analyze_help = analyze_cmd.get_help_text()
        
        print(f"✓ Help text generated")
        print(f"  - Enhance help: {len(enhance_help)} chars")
        print(f"  - Debug help: {len(debug_help)} chars")
        print(f"  - Analyze help: {len(analyze_help)} chars")
        
        # Verify enhanced features are mentioned
        assert "comprehensive" in enhance_help.lower(), "Enhance help should mention comprehensive features"
        assert "auto-fix" in debug_help.lower(), "Debug help should mention auto-fix"
        assert "evaluation" in analyze_help.lower(), "Analyze help should mention evaluation"
        
        print("✓ Enhanced features properly documented in help text")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI command error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_initialization():
    """Test individual component initialization."""
    print("\n🔧 Testing Component Initialization...")
    
    try:
        from agent_framework.core.automatic_bug_fix_loop import AutomaticBugFixLoop
        from agent_framework.core.automatic_evaluation_cycles import AutomaticEvaluationCycles
        
        # Test bug fix loop
        bug_fix_loop = AutomaticBugFixLoop(max_iterations=3)
        print("✓ AutomaticBugFixLoop initialized")
        print(f"  - Max iterations: {bug_fix_loop.max_iterations}")
        print(f"  - Timeout per attempt: {bug_fix_loop.timeout_per_attempt}")
        
        # Test evaluation cycles
        evaluation_cycles = AutomaticEvaluationCycles(enable_advanced_features=True)
        print("✓ AutomaticEvaluationCycles initialized")
        print(f"  - Advanced features enabled: {evaluation_cycles.enable_advanced_features}")
        
        # Test performance metrics
        bug_metrics = bug_fix_loop.get_comprehensive_metrics()
        eval_metrics = evaluation_cycles.get_comprehensive_metrics()
        
        print("✓ Performance metrics available")
        print(f"  - Bug fix sessions: {bug_metrics.get('total_fix_sessions', 0)}")
        print(f"  - Evaluation cycles: {eval_metrics.get('total_evaluations', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Component initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    print("=== Enhanced Agent Framework Simple Integration Test ===\n")
    
    tests = [
        test_imports,
        test_configuration,
        test_orchestrator_creation,
        test_cli_commands,
        test_component_initialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All Enhanced Integration Tests Passed!")
        print("\n📋 Integration Summary:")
        print("  • Enhanced Agent Orchestrator with full capability support")
        print("  • Automatic Bug Fix Loop with iterative debugging")
        print("  • Automatic Evaluation Cycles with comprehensive analysis")
        print("  • CLI Commands with enhanced features integration")
        print("  • Component initialization and configuration")
        print("  • Performance metrics and status tracking")
        return True
    else:
        print(f"\n❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
