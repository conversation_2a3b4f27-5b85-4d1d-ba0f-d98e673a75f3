#!/usr/bin/env python3
"""
Test script for the enhanced Automatic Evaluation Cycles implementation.
"""

import sys
import os
import asyncio
import tempfile
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/agent-test')

try:
    from agent_framework.core.automatic_evaluation_cycles import (
        AutomaticEvaluationCycles, EvaluationStatus, QualityLevel
    )
    
    print("✓ All imports successful")
    
    async def test_enhanced_evaluation_cycles():
        """Test the enhanced automatic evaluation cycles functionality."""
        print("\n=== Testing Enhanced Automatic Evaluation Cycles ===")
        
        # Initialize evaluation system
        evaluator = AutomaticEvaluationCycles(enable_advanced_features=True)
        print("✓ AutomaticEvaluationCycles initialized with advanced features")
        
        # Test performance metrics initialization
        metrics = evaluator.get_comprehensive_metrics()
        print(f"✓ Initial metrics: {metrics['total_evaluations']} evaluations")
        assert metrics['total_evaluations'] == 0
        
        # Create test code samples
        good_code = '''
"""
A well-documented module for mathematical operations.
"""

def calculate_area(length: float, width: float) -> float:
    """
    Calculate the area of a rectangle.
    
    Args:
        length: The length of the rectangle
        width: The width of the rectangle
        
    Returns:
        The area of the rectangle
    """
    if length <= 0 or width <= 0:
        raise ValueError("Length and width must be positive")
    
    return length * width


class Rectangle:
    """A class representing a rectangle."""
    
    def __init__(self, length: float, width: float):
        """Initialize a rectangle with given dimensions."""
        self.length = length
        self.width = width
    
    def area(self) -> float:
        """Calculate and return the area."""
        return calculate_area(self.length, self.width)
'''
        
        problematic_code = '''
def bad_function(a,b,c,d,e,f,g):
    if a > 0:
        if b > 0:
            if c > 0:
                if d > 0:
                    if e > 0:
                        if f > 0:
                            return g / a
    return 0

class VeryLargeClass:
    def method1(self): pass
    def method2(self): pass
    def method3(self): pass
    def method4(self): pass
    def method5(self): pass
    def method6(self): pass
    def method7(self): pass
    def method8(self): pass
    def method9(self): pass
    def method10(self): pass
    def method11(self): pass
    def method12(self): pass
    def method13(self): pass
    def method14(self): pass
    def method15(self): pass
    def method16(self): pass
    def method17(self): pass
    def method18(self): pass
    def method19(self): pass
    def method20(self): pass
    def method21(self): pass
'''
        
        print("\n🔍 Testing evaluation cycle with good code...")
        
        # Create temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(good_code)
            temp_file = f.name
        
        try:
            # Test evaluation cycle with good code
            cycle = await evaluator.run_evaluation_cycle(
                code_content=good_code,
                file_path=temp_file,
                evaluation_types=["static_analysis", "code_quality", "maintainability_analysis", "documentation_quality"]
            )
            
            print(f"✓ Good code evaluation completed: {cycle.cycle_id}")
            print(f"✓ Overall quality: {cycle.overall_quality.value}")
            print(f"✓ Overall score: {cycle.overall_score:.2f}")
            print(f"✓ Total time: {cycle.total_time:.2f}s")
            print(f"✓ Rollback recommended: {cycle.rollback_recommended}")
            print(f"✓ Number of evaluations: {len(cycle.evaluations)}")
            print(f"✓ Critical issues: {len(cycle.critical_issues)}")
            print(f"✓ Improvement plan items: {len(cycle.improvement_plan)}")
            
            # Verify cycle data
            assert cycle.cycle_id is not None
            assert cycle.overall_quality in [QualityLevel.EXCELLENT, QualityLevel.GOOD, QualityLevel.FAIR]
            assert cycle.overall_score > 0.5  # Should be decent quality
            assert cycle.total_time > 0
            assert len(cycle.evaluations) == 4  # We requested 4 evaluation types
            
            # Check individual evaluations
            for eval_type, result in cycle.evaluations.items():
                print(f"  - {eval_type}: {result.quality_level.value} (score: {result.score:.2f})")
                assert result.evaluation_type == eval_type
                assert result.status in [EvaluationStatus.COMPLETED, EvaluationStatus.FAILED]
                assert result.execution_time >= 0
            
            print("\n🔍 Testing evaluation cycle with problematic code...")
            
            # Test with problematic code
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(problematic_code)
                temp_file2 = f.name
            
            try:
                cycle2 = await evaluator.run_evaluation_cycle(
                    code_content=problematic_code,
                    file_path=temp_file2,
                    evaluation_types=["maintainability_analysis", "documentation_quality"]
                )
                
                print(f"✓ Problematic code evaluation completed: {cycle2.cycle_id}")
                print(f"✓ Overall quality: {cycle2.overall_quality.value}")
                print(f"✓ Overall score: {cycle2.overall_score:.2f}")
                print(f"✓ Critical issues: {len(cycle2.critical_issues)}")
                
                # Should have lower quality due to issues
                assert cycle2.overall_score < cycle.overall_score
                assert len(cycle2.critical_issues) >= 0  # May have critical issues
                
                # Test evaluation history
                history = evaluator.get_evaluation_history()
                print(f"✓ Evaluation history: {len(history)} cycles")
                assert len(history) == 2
                assert history[0].cycle_id == cycle.cycle_id
                assert history[1].cycle_id == cycle2.cycle_id
                
                # Test comprehensive metrics
                updated_metrics = evaluator.get_comprehensive_metrics()
                print(f"✓ Updated metrics: {updated_metrics['total_evaluations']} evaluations")
                assert updated_metrics['total_evaluations'] == 2
                assert updated_metrics['average_evaluation_time'] > 0
                
                # Test quality trends
                trends = evaluator.get_quality_trends()
                print(f"✓ Quality trends: {trends}")
                assert 'trend' in trends
                assert 'current_score' in trends
                
                print("\n=== Testing Advanced Features ===")
                
                # Test with all evaluation types (including advanced ones)
                cycle3 = await evaluator.run_evaluation_cycle(
                    code_content=good_code,
                    file_path=temp_file,
                    evaluation_types=None  # Use default (all types)
                )
                
                print(f"✓ Full evaluation completed: {cycle3.cycle_id}")
                print(f"✓ Number of evaluation types: {len(cycle3.evaluations)}")
                
                # Should have more evaluation types with advanced features enabled
                assert len(cycle3.evaluations) >= 6  # At least the basic 6 types
                
                # Test specific advanced evaluations
                if 'error_detection' in cycle3.evaluations:
                    error_result = cycle3.evaluations['error_detection']
                    print(f"  - Error detection: {error_result.quality_level.value}")
                    assert error_result.evaluation_type == 'error_detection'
                
                if 'optimization_analysis' in cycle3.evaluations:
                    opt_result = cycle3.evaluations['optimization_analysis']
                    print(f"  - Optimization analysis: {opt_result.quality_level.value}")
                    assert opt_result.evaluation_type == 'optimization_analysis'
                
                if 'maintainability_analysis' in cycle3.evaluations:
                    maint_result = cycle3.evaluations['maintainability_analysis']
                    print(f"  - Maintainability: {maint_result.quality_level.value}")
                    assert maint_result.evaluation_type == 'maintainability_analysis'
                    assert 'average_function_length' in maint_result.metrics
                
                if 'documentation_quality' in cycle3.evaluations:
                    doc_result = cycle3.evaluations['documentation_quality']
                    print(f"  - Documentation quality: {doc_result.quality_level.value}")
                    assert doc_result.evaluation_type == 'documentation_quality'
                    assert 'function_documentation_coverage' in doc_result.metrics
                
                print("\n=== All Enhanced Evaluation Cycles Tests Passed! ===")
                return True
                
            finally:
                # Clean up second temp file
                try:
                    os.unlink(temp_file2)
                except:
                    pass
                    
        finally:
            # Clean up first temp file
            try:
                os.unlink(temp_file)
            except:
                pass
    
    # Run the test
    if __name__ == "__main__":
        result = asyncio.run(test_enhanced_evaluation_cycles())
        if result:
            print("\n🎉 Enhanced Automatic Evaluation Cycles implementation is working correctly!")
            print("\n📊 Key Features Tested:")
            print("  • Enhanced logging with cycle context")
            print("  • Advanced evaluation types (error detection, optimization, maintainability, documentation)")
            print("  • Performance metrics tracking and quality trends")
            print("  • Comprehensive cycle management and history")
            print("  • Quality level determination and scoring")
            print("  • Rollback recommendations based on critical issues")
            print("  • Improvement plan generation")
            print("  • Multiple code quality assessments")
            sys.exit(0)
        else:
            print("\n❌ Tests failed")
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Note: Some dependencies may not be available in the current environment")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
