#!/usr/bin/env python3
"""
Test script for the advanced Automatic Bug Fix Loop implementation.
"""

import sys
import os
import asyncio
import tempfile
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/agent-test')

try:
    from agent_framework.core.automatic_bug_fix_loop import AutomaticBugFixLoop, FixAttemptStatus
    from agent_framework.core.debugger import DebugSuggestion
    
    print("✓ All imports successful")
    
    async def test_advanced_bug_fix_loop():
        """Test the advanced automatic bug fix loop functionality."""
        print("\n=== Testing Advanced Automatic Bug Fix Loop ===")
        
        # Initialize bug fix loop
        bug_fixer = AutomaticBugFixLoop(
            max_iterations=3,
            timeout_per_attempt=30,
            enable_advanced_analysis=True
        )
        print("✓ AutomaticBugFixLoop initialized")
        
        # Test performance metrics initialization
        metrics = bug_fixer.get_comprehensive_metrics()
        print(f"✓ Initial metrics: {metrics['total_fix_sessions']} sessions")
        assert metrics['total_fix_sessions'] == 0
        
        # Create a simple buggy code example
        buggy_code = '''
def divide_numbers(a, b):
    # Bug: No check for division by zero
    result = a / b
    return result

# Test the function
result = divide_numbers(10, 0)
print(f"Result: {result}")
'''
        
        # Create a simple error to simulate
        try:
            exec(buggy_code)
        except ZeroDivisionError as e:
            test_error = e
        
        print("✓ Created test error scenario")
        
        # Create temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(buggy_code)
            temp_file = f.name
        
        try:
            # Test the bug fix loop
            print("🔧 Starting bug fix loop...")
            
            session = await bug_fixer.start_fix_loop(
                error=test_error,
                code_content=buggy_code,
                file_path=temp_file,
                test_files=None,
                context_vars={'a': 10, 'b': 0}
            )
            
            print(f"✓ Bug fix session completed: {session.session_id}")
            print(f"✓ Final status: {session.final_status.value}")
            print(f"✓ Number of attempts: {len(session.fix_attempts)}")
            print(f"✓ Total time: {session.total_time:.2f}s")
            print(f"✓ Success rate: {session.success_rate:.2f}")
            
            # Verify session data
            assert session.session_id is not None
            assert session.initial_error is not None
            assert session.root_cause is not None
            assert len(session.fix_attempts) > 0
            assert session.total_time > 0
            
            # Test metrics after session
            updated_metrics = bug_fixer.get_comprehensive_metrics()
            print(f"✓ Updated metrics: {updated_metrics['total_fix_sessions']} sessions")
            assert updated_metrics['total_fix_sessions'] == 1
            
            # Test fix history
            history = bug_fixer.get_fix_history()
            print(f"✓ Fix history: {len(history)} sessions")
            assert len(history) == 1
            assert history[0].session_id == session.session_id
            
            # Test success statistics
            stats = bug_fixer.get_success_statistics()
            print(f"✓ Success statistics: {stats}")
            assert stats['total_sessions'] == 1
            assert stats['total_attempts'] >= 1
            
            # Test lessons learned
            if session.lessons_learned:
                print(f"✓ Lessons learned: {session.lessons_learned}")
            
            print("\n=== Testing Additional Features ===")
            
            # Test metrics reset
            bug_fixer.reset_metrics()
            reset_metrics = bug_fixer.get_comprehensive_metrics()
            print(f"✓ Metrics reset: {reset_metrics['total_fix_sessions']} sessions")
            assert reset_metrics['total_fix_sessions'] == 0
            
            # Test with different error type
            syntax_error_code = '''
def broken_function():
    print("Missing closing quote)
    return True
'''
            
            try:
                compile(syntax_error_code, '<string>', 'exec')
            except SyntaxError as syntax_error:
                print("🔧 Testing with syntax error...")
                
                syntax_session = await bug_fixer.start_fix_loop(
                    error=syntax_error,
                    code_content=syntax_error_code,
                    file_path=temp_file,
                    test_files=None
                )
                
                print(f"✓ Syntax error session: {syntax_session.final_status.value}")
                assert syntax_session.session_id is not None
            
            print("\n=== All Enhanced Bug Fix Loop Tests Passed! ===")
            return True
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file)
            except:
                pass
    
    # Run the test
    if __name__ == "__main__":
        result = asyncio.run(test_advanced_bug_fix_loop())
        if result:
            print("\n🎉 Advanced Automatic Bug Fix Loop implementation is working correctly!")
            print("\n📊 Key Features Tested:")
            print("  • Advanced logging with session context")
            print("  • Performance metrics tracking")
            print("  • Advanced error analysis integration")
            print("  • Comprehensive session management")
            print("  • Multiple error type handling")
            print("  • Lessons learned extraction")
            print("  • Success rate calculation")
            print("  • Fix history management")
            sys.exit(0)
        else:
            print("\n❌ Tests failed")
            sys.exit(1)
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Note: Some dependencies may not be available in the current environment")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
