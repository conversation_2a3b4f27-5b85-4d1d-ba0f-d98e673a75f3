#!/usr/bin/env python3
"""
Main CLI entry point for the agent framework.

This script provides the command-line interface for the comprehensive
programming assistant agent framework.
"""

import sys
from pathlib import Path

# Add the src directory to Python path for development
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from agent_framework.cli.core import main

if __name__ == '__main__':
    sys.exit(main())
