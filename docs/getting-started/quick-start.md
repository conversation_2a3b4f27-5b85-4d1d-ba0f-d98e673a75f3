# Quick Start Guide

This guide will help you get started with the Agent Framework quickly and efficiently.

## Prerequisites

Before you begin, make sure you have:

- Python 3.10 or higher installed
- An API key from OpenAI, Anthropic, or OpenRouter
- Completed the [Installation Guide](installation.md)

## Setup

If you haven't installed the framework yet, see the [Installation Guide](installation.md).

### Set Your API Key

```bash
# Set your API key (choose one)
export AGENT_API_KEY="your-openai-api-key"
export AGENT_MODEL="gpt-4o"

# Or for cost-effective option
export AGENT_API_KEY="your-openrouter-api-key"
export AGENT_MODEL="qwen/qwen3-coder:free"
export AGENT_BASE_URL="https://openrouter.ai/api/v1"
```

### Verify Installation

```bash
# Check if everything is working
agent-framework --help
```

## Basic Usage

### 1. Code Analysis

Analyze your code for quality, complexity, and potential issues:

```bash
# Analyze a single file
agent-framework analyze --file src/my_code.py --type all --detailed

# Analyze code directly
agent-framework analyze --code "def hello(): pass" --type quality
```

### 2. Code Enhancement

Improve your code with automatic enhancements:

```bash
# Enhance code quality and performance
agent-framework enhance --file src/my_code.py --goals quality performance

# Comprehensive enhancement with bug fixing and evaluation
agent-framework enhance --file src/my_code.py --comprehensive
```

### 3. Debugging Assistance

Get intelligent debugging help:

```bash
# Debug with automatic fixing
agent-framework debug --auto-fix --file src/buggy_code.py

# Check for potential errors
agent-framework debug --check-errors --file src/buggy_code.py
```

### 4. Code Generation

Generate new code with advanced capabilities:

```bash
# Generate a function
agent-framework generate function --name "process_data" --description "Process user data"

# Generate a class
agent-framework generate class --name "DataProcessor" --description "Process data efficiently"
```

## Programming Interface

### Basic Setup

```python
from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)

# Configure capabilities
capabilities = AgentCapabilities(
    enable_automatic_bug_fixing=True,
    enable_automatic_evaluation=True,
    enable_advanced_code_generation=True,
    enable_comprehensive_testing=True
)

# Initialize orchestrator
orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)
```

### Code Analysis Example

```python
# Analyze code comprehensively
code = '''
def calculate_average(numbers):
    total = sum(numbers)
    return total / len(numbers)
'''

analysis_result = await orchestrator.comprehensive_code_analysis(
    code_content=code,
    file_path="example.py",
    analysis_depth="comprehensive"
)

print(f"Quality Score: {analysis_result['quality_score']}")
print(f"Issues Found: {len(analysis_result['context'].potential_issues)}")
```

### Code Implementation Example

```python
# Implement new functionality
requirements = {
    "type": "function",
    "name": "validate_email",
    "description": "Validate email address format",
    "parameters": ["email"],
    "return_type": "bool"
}

implementation_result = await orchestrator.advanced_code_implementation(
    requirements=requirements,
    file_path="validators.py"
)

if implementation_result["success"]:
    print("✅ Implementation successful!")
    print(f"Generated code:\n{implementation_result['generated_code']}")
```

### Full Advanced Cycle Example

```python
# Run complete enhancement cycle
legacy_code = '''
def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result
'''

enhancement_requirements = {
    "type": "enhancement",
    "goals": ["add_type_hints", "add_error_handling", "improve_performance"],
    "description": "Modernize legacy data processing function"
}

results = await orchestrator.run_full_advanced_cycle(
    code_content=legacy_code,
    file_path="data_processor.py",
    requirements=enhancement_requirements
)

print(f"✅ Enhancement completed: {results['overall_success']}")
if results["implementation"]:
    print(f"📈 Quality improved: {results['implementation']['evaluation'].overall_score}")
```

## Advanced Features

### Automatic Bug Fixing

```python
# Enable automatic bug fixing
capabilities = AgentCapabilities(
    enable_automatic_bug_fixing=True,
    max_fix_iterations=5
)

orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

# The orchestrator will automatically attempt to fix bugs during code operations
```

### Quality Evaluation

```python
# Enable comprehensive evaluation
capabilities = AgentCapabilities(
    enable_automatic_evaluation=True,
    evaluation_on_every_change=True,
    rollback_on_critical_issues=True
)

# Evaluation will run automatically and provide detailed quality metrics
```

### Multi-Agent Coordination

```python
# The framework automatically coordinates multiple specialized agents:
# - Code Analysis Agent: Deep code understanding
# - Code Editor Agent: Safe code modifications
# - Debugger Agent: Intelligent error resolution
# - Testing Agent: Comprehensive test generation
# - Optimization Agent: Performance improvements
```

## Configuration

### Environment Variables

```bash
# Set default model configuration
export OPENAI_API_KEY="your-api-key"
export ANTHROPIC_API_KEY="your-api-key"

# Configure logging level
export LOG_LEVEL="INFO"

# Set working directory
export AGENT_WORKSPACE="/path/to/your/project"
```

### Configuration File

Create `config.yaml`:

```yaml
framework:
  log_level: INFO
  workspace_path: "/path/to/project"

advanced_capabilities:
  enabled: true
  enable_automatic_bug_fixing: true
  enable_automatic_evaluation: true
  enable_advanced_code_generation: true
  enable_comprehensive_testing: true
  max_fix_iterations: 5
  evaluation_on_every_change: true
  rollback_on_critical_issues: true

agents:
  code_analyzer:
    model: "gpt-4"
    temperature: 0.1
  
  code_editor:
    model: "claude-3-sonnet"
    temperature: 0.2
  
  debugger:
    model: "gpt-4"
    temperature: 0.1
```

## Common Use Cases

### 1. Legacy Code Modernization

```bash
# Modernize old Python code
agent-framework enhance --file legacy_module.py \
  --goals quality maintainability \
  --comprehensive
```

### 2. Code Quality Improvement

```bash
# Improve code quality with comprehensive analysis
agent-framework enhance --file src/ \
  --goals quality maintainability documentation \
  --enable-evaluation
```

### 3. Bug Detection and Fixing

```bash
# Automatically detect and fix bugs
agent-framework debug --auto-fix \
  --file problematic_code.py \
  --max-iterations 3
```

### 4. Test Generation

```bash
# Generate comprehensive tests
agent-framework generate tests \
  --file src/my_module.py \
  --framework pytest
```

## Next Steps

Now that you've completed the quick start:

1. **Learn Core Concepts**: Read [Basic Concepts](basic-concepts.md) to understand the framework better
2. **Explore Features**: Check out [Code Analysis](../features/code-analysis.md) and other feature guides
3. **Try Examples**: Work through [Basic Usage Examples](../examples/basic-usage.md)
4. **Advanced Setup**: Configure [Multi-Agent Setup](../user-guides/multi-agent-setup.md) for complex workflows
5. **Reference**: Use the [CLI Reference](../user-guides/cli-reference.md) for detailed command information

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`
2. **API Key Issues**: Set your API keys in environment variables
3. **Permission Errors**: Ensure the framework has write access to your project directory
4. **Memory Issues**: For large codebases, consider using `--analysis-depth basic` initially

### Getting Help

- **Documentation**: Browse the complete [documentation](../README.md)
- **CLI Help**: Run commands with `--help` for detailed usage information
- **Examples**: Review [practical examples](../examples/basic-usage.md)
- **Troubleshooting**: Check [common issues](../troubleshooting/common-issues.md) and [FAQ](../troubleshooting/faq.md)
- **Community**: Join our [Discord](https://discord.gg/example) or [GitHub Discussions](https://github.com/yourusername/agent-framework/discussions)

## Performance Tips

1. **Use Appropriate Analysis Depth**: Start with "basic" for large codebases
2. **Enable Caching**: The framework caches analysis results for better performance
3. **Batch Operations**: Process multiple files together when possible
4. **Configure Resource Limits**: Set appropriate timeout and iteration limits
