# Installation Guide

This guide will walk you through installing the Agent Framework and setting up your development environment.

## Prerequisites

### System Requirements

- **Python**: 3.10 or higher
- **Operating System**: Linux, macOS, or Windows
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: At least 2GB free space

### Required API Keys

You'll need at least one of the following API keys:

- **OpenAI API Key** (recommended for best performance)
- **Anthropic API Key** (for Claude models)
- **Azure OpenAI** (for enterprise deployments)
- **OpenRouter API Key** (for access to multiple models)

## Installation Methods

### Method 1: Using uv (Recommended)

[uv](https://github.com/astral-sh/uv) is a fast Python package manager that provides better dependency resolution and faster installs.

```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone the repository
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework

# Install dependencies
uv sync

# For development with additional tools
uv sync --dev
```

### Method 2: Using pip

```bash
# Clone the repository
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework

# Create a virtual environment (recommended)
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# For development
pip install -r requirements-dev.txt
```

### Method 3: Development Installation

For contributors and developers:

```bash
# Clone the repository
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework

# Install in development mode
pip install -e .

# Install pre-commit hooks
pre-commit install
```

## Configuration

### 1. Set Up API Keys

Choose one of the following methods to configure your API keys:

#### Environment Variables (Recommended)

```bash
# OpenAI (recommended)
export AGENT_API_KEY="your-openai-api-key"
export AGENT_MODEL="gpt-4o"
export AGENT_BASE_URL="https://api.openai.com/v1"

# Or Anthropic
export AGENT_API_KEY="your-anthropic-api-key"
export AGENT_MODEL="claude-3-5-sonnet-20241022"
export AGENT_BASE_URL="https://api.anthropic.com"

# Or OpenRouter (cost-effective)
export AGENT_API_KEY="your-openrouter-api-key"
export AGENT_MODEL="qwen/qwen3-coder:free"
export AGENT_BASE_URL="https://openrouter.ai/api/v1"
```

#### Configuration File

Create a `config.yaml` file in your project directory:

```yaml
name: "My Programming Assistant"
debug: false

model:
  model: "gpt-4o"
  api_key: "${AGENT_API_KEY}"
  base_url: "https://api.openai.com/v1"
  max_tokens: 4096
  temperature: 0.7

plugins:
  auto_load_plugins: true
  plugin_directories: ["plugins"]

logging:
  level: "INFO"
  file_path: "agent.log"

cache:
  enabled: true
  cache_type: "memory"
  ttl_seconds: 3600
```

### 2. Verify Installation

Test your installation:

```bash
# Check if the CLI is working
agent-framework --help

# Or using Python module
python -m agent_framework.cli.core --help

# Test with a simple command
agent-framework analyze --code "def hello(): print('Hello, World!')" --type complexity
```

## Optional Dependencies

### For Enhanced Features

```bash
# Rich terminal interface
pip install rich

# Additional model providers
pip install anthropic azure-openai

# Development tools
pip install pytest pytest-cov black isort mypy
```

### For MCP Integration

```bash
# Node.js for MCP servers (optional)
# Install Node.js from https://nodejs.org/

# Install MCP filesystem server
npx -y @modelcontextprotocol/server-filesystem

# Install MCP fetch server
pip install mcp-server-fetch
```

## Platform-Specific Instructions

### Windows

```cmd
# Use Command Prompt or PowerShell
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework

# Create virtual environment
python -m venv .venv
.venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables (PowerShell)
$env:AGENT_API_KEY="your-api-key"
```

### macOS

```bash
# Install using Homebrew (optional)
brew install python@3.11

# Follow standard installation
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

### Linux

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-venv git

# CentOS/RHEL/Fedora
sudo dnf install python3.11 python3-venv git

# Follow standard installation
git clone https://github.com/yourusername/agent-framework.git
cd agent-framework
python3.11 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## Troubleshooting Installation

### Common Issues

1. **Python Version Error**

   ```bash
   # Check Python version
   python --version
   # Should be 3.10 or higher
   ```

2. **Permission Errors**

   ```bash
   # Use virtual environment
   python -m venv .venv
   source .venv/bin/activate
   ```

3. **API Key Issues**

   ```bash
   # Verify API key is set
   echo $AGENT_API_KEY
   ```

4. **Import Errors**

   ```bash
   # Ensure you're in the correct directory and virtual environment
   which python
   pip list | grep agent
   ```

### Getting Help

If you encounter issues:

1. Check [Common Issues](../troubleshooting/common-issues.md)
2. Review [FAQ](../troubleshooting/faq.md)
3. Join our [Discord community](https://discord.gg/example)
4. Open an issue on [GitHub](https://github.com/yourusername/agent-framework/issues)

## Next Steps

After successful installation:

1. Read the [Quick Start Guide](quick-start.md)
2. Learn [Basic Concepts](basic-concepts.md)
3. Try the [First Steps Tutorial](first-steps.md)
4. Explore [CLI Reference](../user-guides/cli-reference.md)

---

**Installation complete! 🎉** You're ready to start using the Agent Framework.
