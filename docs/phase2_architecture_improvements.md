# Phase 2: Architecture Improvements & Observability

This document describes the comprehensive Phase 2 enhancements to the Agent Framework, focusing on architecture improvements, observability, and enterprise-grade capabilities.

## 🎯 Overview

Phase 2 transforms the Agent Framework into a production-ready, enterprise-grade system with:

- **Comprehensive Observability**: OpenTelemetry integration, distributed tracing, and Prometheus metrics
- **Enhanced Security**: Advanced threat detection, secrets management, and input validation
- **Resilience Patterns**: Circuit breakers, retry mechanisms, and bulkhead isolation
- **Advanced Plugin System**: Dependency resolution, versioning, and inter-plugin communication
- **Health Monitoring**: Kubernetes-compatible health checks and readiness probes
- **Configuration Management**: Hot-reload capabilities and environment-aware configuration
- **Code Quality**: Eliminated duplication through shared components and unified AST analysis

## 🏗️ Architecture Improvements

### Unified AST Analysis Engine

**Location**: `src/agent_framework/shared/ast_analysis_engine.py`

Eliminates code duplication across the framework by providing a centralized, extensible AST analysis system:

```python
from agent_framework.shared.ast_analysis_engine import ast_analysis_engine, AnalysisType

# Analyze code with multiple analyzers
results = ast_analysis_engine.analyze(
    source_code, 
    [AnalysisType.COMPLEXITY, AnalysisType.PATTERNS, AnalysisType.SECURITY]
)

# Access specific analysis results
complexity_data = results[AnalysisType.COMPLEXITY].data
patterns_data = results[AnalysisType.PATTERNS].data
```

**Features**:
- Pluggable analyzer architecture
- Pattern detection (Singleton, Factory, Observer, Anti-patterns)
- Complexity analysis with function-level metrics
- Import dependency analysis
- Backward compatibility with existing code

### Shared Error Handling Framework

**Location**: `src/agent_framework/shared/error_handling.py`

Provides consistent error handling patterns across all components:

```python
from agent_framework.shared.error_handling import (
    error_handler, AgentFrameworkError, ErrorSeverity, ErrorCategory
)

@error_handler("my_component", "process_data")
async def process_data(data):
    if not data:
        raise ValidationError("Data cannot be empty")
    return processed_data

# Custom error types with context
raise ProcessingError(
    "Failed to process request",
    severity=ErrorSeverity.HIGH,
    context=ErrorContext("api", "process_request", user_id="123"),
    suggested_action="Retry with valid input"
)
```

**Features**:
- Hierarchical exception types with context
- Automatic error categorization and severity assessment
- Recovery strategy registration
- Comprehensive error logging with correlation IDs
- Integration with observability systems

## 📊 Observability & Telemetry

### Comprehensive Telemetry Manager

**Location**: `src/agent_framework/observability/telemetry_manager.py`

Provides OpenTelemetry integration with distributed tracing and metrics:

```python
from agent_framework.observability import initialize_observability, trace_function

# Initialize observability
await initialize_observability(ObservabilityConfig(
    service_name="my-service",
    enable_tracing=True,
    jaeger_endpoint="http://localhost:14268/api/traces"
))

# Trace functions automatically
@trace_function("data_processing")
async def process_data(data):
    # Automatically traced with span creation
    return processed_data

# Manual span creation
async with trace_operation("complex_operation") as span:
    # Operation is traced
    result = await complex_operation()
```

**Features**:
- OpenTelemetry integration with Jaeger and Prometheus
- Automatic instrumentation for asyncio and logging
- Custom span and metric decorators
- Correlation ID tracking
- Performance monitoring and analysis

### Advanced Metrics Collection

**Location**: `src/agent_framework/observability/metrics_collector.py`

Comprehensive metrics collection with Prometheus integration:

```python
from agent_framework.observability import record_metric, MetricDefinition

# Record metrics
record_metric("requests_total", 1, {"endpoint": "/api/data"})
record_metric("request_duration", 0.5, {"endpoint": "/api/data"}, "histogram")

# Register custom metrics
custom_metric = MetricDefinition(
    name="business_metric",
    description="Custom business metric",
    metric_type="gauge",
    labels=["category", "region"]
)
observability_manager.register_custom_metric(custom_metric)
```

**Features**:
- System metrics (CPU, memory, disk, network)
- Application metrics (requests, tasks, errors)
- Custom business metrics
- Prometheus exposition format
- Real-time metric updates

### Health Monitoring System

**Location**: `src/agent_framework/health/health_monitor.py`

Kubernetes-compatible health checks and monitoring:

```python
from agent_framework.health import HealthMonitor, HealthCheckConfig

# Create health monitor
health_monitor = HealthMonitor()

# Register custom health check
async def check_database():
    # Custom health check logic
    return HealthCheckResult(
        name="database",
        status=HealthStatus.HEALTHY,
        message="Database connection healthy"
    )

health_monitor.register_health_check(HealthCheckConfig(
    name="database",
    check_function=check_database,
    timeout_seconds=10.0,
    critical=True
))

# Get health status
health = await health_monitor.get_health_status()
readiness = await health_monitor.get_readiness_status()
```

**Features**:
- Configurable health checks with timeouts and retries
- System resource monitoring with thresholds
- Kubernetes-compatible endpoints (`/health`, `/ready`)
- Health history and trend analysis
- Integration with external services

## 🛡️ Enhanced Security Framework

### Input Validation & Sanitization

**Location**: `src/agent_framework/security/input_validator.py`

Comprehensive input validation and attack prevention:

```python
from agent_framework.security import SecurityManager

security = SecurityManager()

# Validate input automatically
@security.validate_input
async def process_user_input(data: str):
    # Input is automatically validated for:
    # - SQL injection attempts
    # - XSS attacks
    # - Command injection
    # - Path traversal
    return processed_data

# Manual validation
validation_result = await security.validate_input(user_input)
if not validation_result.is_safe:
    raise SecurityError(f"Unsafe input detected: {validation_result.threats}")
```

### Secrets Management

**Location**: `src/agent_framework/security/secrets_manager.py`

Enterprise-grade secrets management with encryption:

```python
from agent_framework.security import SecretsManager

secrets = SecretsManager()

# Store encrypted secrets
await secrets.store_secret("api_key", "secret_value", ttl=3600)

# Retrieve secrets
api_key = await secrets.get_secret("api_key")

# Automatic rotation
await secrets.rotate_secret("api_key", new_value="new_secret_value")
```

**Features**:
- AES-256 encryption with key derivation
- Automatic secret rotation and expiration
- System keyring integration
- Comprehensive audit logging
- Secret lifecycle management

### Security Monitoring

**Location**: `src/agent_framework/security/security_monitor.py`

Real-time security monitoring and threat detection:

```python
from agent_framework.security import SecurityMonitor

monitor = SecurityMonitor()

# Configure threat detection
await monitor.configure_threat_detection({
    'failed_login_threshold': 5,
    'rate_limit_threshold': 100,
    'anomaly_detection': True
})

# Monitor security events
await monitor.start_monitoring()
```

**Features**:
- Real-time threat detection
- Rate limiting and anomaly detection
- Security event correlation
- Alert callbacks for external systems
- Compliance audit trails

## 🔄 Resilience Patterns

### Circuit Breaker Implementation

**Location**: `src/agent_framework/resilience/circuit_breaker.py`

Comprehensive resilience patterns for fault tolerance:

```python
from agent_framework.resilience import circuit_breaker, retry_with_backoff, bulkhead

# Circuit breaker decorator
@circuit_breaker("external_service", CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=60.0
))
async def call_external_service():
    # Protected by circuit breaker
    return await external_api_call()

# Retry with exponential backoff
@retry_with_backoff(max_attempts=3, base_delay=1.0)
async def unreliable_operation():
    # Automatically retried on failure
    return await operation()

# Bulkhead pattern for resource isolation
@bulkhead("database_operations", max_concurrent=10)
async def database_operation():
    # Limited concurrent executions
    return await db_query()
```

**Features**:
- Circuit breaker with automatic recovery
- Exponential backoff with jitter
- Bulkhead pattern for resource isolation
- Comprehensive statistics and monitoring
- Integration with metrics and alerting

## 🔌 Enhanced Plugin System

### Advanced Plugin Management

**Location**: `src/agent_framework/plugins/enhanced_plugin_system.py`

Enterprise-grade plugin system with dependency resolution:

```python
from agent_framework.plugins import EnhancedPluginManager, BasePlugin, PluginMetadata

class MyPlugin(BasePlugin):
    @classmethod
    def get_metadata(cls) -> PluginMetadata:
        return PluginMetadata(
            name="my_plugin",
            version=PluginVersion(1, 0, 0),
            dependencies=[
                PluginDependency("base_plugin", min_version=PluginVersion(1, 0, 0))
            ],
            provides=["data_processing"],
            requires=["logging"],
            hot_reload=True
        )
    
    async def initialize(self):
        # Register message handlers
        self.register_message_handler("data.process", self._handle_data)
    
    async def _handle_data(self, message):
        # Process inter-plugin message
        result = await self.process_data(message.payload)
        
        # Send response
        await self.send_message(
            recipient=message.sender,
            topic="data.processed",
            payload=result
        )

# Plugin manager with dependency resolution
plugin_manager = EnhancedPluginManager()
await plugin_manager.initialize()
```

**Features**:
- Dependency resolution and loading order
- Version compatibility checking
- Inter-plugin communication bus
- Hot-reload capabilities
- Plugin sandboxing and isolation

## ⚙️ Configuration Management

### Hot-Reload Configuration

**Location**: `src/agent_framework/config/config_manager.py`

Dynamic configuration with hot-reload capabilities:

```python
from agent_framework.config import ConfigManager

config = ConfigManager(["config"], auto_reload=True)

# Get configuration values
database_url = config.get("database.url", "sqlite:///default.db")
api_timeout = config.get("api.timeout", 30.0)

# Register change callbacks
def on_config_change(change_event):
    print(f"Config changed: {change_event.config_path}")

config.add_change_callback(on_config_change)

# Manual reload
await config.reload()

# Configuration rollback
config.rollback(steps=1)
```

**Features**:
- Multiple configuration file formats (JSON, YAML)
- Environment variable substitution
- File system watching for automatic reload
- Configuration validation with Pydantic
- Change notifications and rollback capabilities

## 📈 Monitoring Dashboards

### Pre-configured Grafana Dashboards

**Location**: `src/agent_framework/observability/dashboard_config.py`

Production-ready monitoring dashboards:

```python
from agent_framework.observability import DashboardGenerator

generator = DashboardGenerator()

# Generate all dashboards
dashboards = generator.export_all_dashboards("./dashboards")

# Available dashboards:
# - System Overview: CPU, memory, disk, network
# - Application Metrics: Requests, response times, errors
# - Security Monitoring: Security events, threats
# - Plugin Monitoring: Plugin operations, cache performance
```

**Features**:
- System resource monitoring
- Application performance metrics
- Security event tracking
- Plugin and cache performance
- Kubernetes deployment ready

## 🚀 Getting Started

### 1. Installation

```bash
# Run the Phase 2 setup script
python scripts/setup_phase2_enhancements.py
```

### 2. Start Observability Stack

```bash
# Start Prometheus, Grafana, and Jaeger
docker-compose -f docker-compose.observability.yml up -d
```

### 3. Initialize in Your Application

```python
from agent_framework.observability import initialize_observability, ObservabilityConfig
from agent_framework.security import SecurityManager
from agent_framework.plugins import EnhancedPluginManager

async def main():
    # Initialize observability
    await initialize_observability(ObservabilityConfig(
        service_name="my-service",
        environment="production"
    ))
    
    # Initialize security
    security = SecurityManager()
    await security.initialize()
    
    # Initialize plugins
    plugin_manager = EnhancedPluginManager()
    await plugin_manager.initialize()
    
    # Your application logic here
    
if __name__ == "__main__":
    asyncio.run(main())
```

### 4. Access Monitoring

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Jaeger**: http://localhost:16686

## 📚 Additional Documentation

- [Security Enhancements](security_enhancements.md)
- [Observability Guide](observability.md)
- [Plugin Development](plugin_development.md)
- [Configuration Reference](configuration.md)
- [Deployment Guide](deployment.md)

## 🔧 Configuration Files

All configuration files are created in the `config/` directory:

- `config/observability/config.yaml` - Observability settings
- `config/security/config.yaml` - Security configuration
- `config/plugins/config.yaml` - Plugin system settings
- `config/prometheus/prometheus.yml` - Prometheus configuration

## 🎯 Key Benefits

1. **Production Ready**: Enterprise-grade security, monitoring, and resilience
2. **Observability**: Complete visibility into system behavior and performance
3. **Maintainability**: Eliminated code duplication and improved architecture
4. **Scalability**: Plugin system and configuration management for growth
5. **Reliability**: Circuit breakers, health checks, and error handling
6. **Security**: Comprehensive threat detection and secrets management

The Phase 2 enhancements transform the Agent Framework into a robust, production-ready system suitable for enterprise deployments with comprehensive monitoring, security, and operational capabilities.
