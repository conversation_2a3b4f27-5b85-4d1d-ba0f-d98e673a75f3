# Security Enhancements Documentation

This document describes the comprehensive security enhancements implemented for the Agent Framework, including critical fixes, performance optimizations, and enterprise-grade security features.

## Overview

The security enhancements provide:
- **Input Validation & Sanitization**: Comprehensive protection against injection attacks
- **Secrets Management**: Encrypted storage and rotation of sensitive data
- **Security Monitoring**: Real-time threat detection and audit logging
- **Performance Optimizations**: Memory management and async pattern improvements
- **Database Security**: Connection pooling with security controls

## Components

### 1. Input Validator (`src/agent_framework/security/input_validator.py`)

Provides comprehensive input validation and sanitization:

```python
from agent_framework.security import InputValidator, SecurityConfig

# Initialize with custom configuration
config = SecurityConfig(
    max_string_length=5000,
    max_list_length=500,
    blocked_patterns=['<script.*?>.*?</script>']
)
validator = InputValidator(config)

# Validate different input types
result = validator.validate_string("user input")
if result.is_valid:
    safe_value = result.sanitized_value
else:
    print(f"Validation errors: {result.errors}")
```

**Features:**
- SQL injection detection
- XSS prevention
- Command injection protection
- Path traversal prevention
- JSON validation with depth limits
- Python code safety checks
- URL validation

### 2. Secrets Manager (`src/agent_framework/security/secrets_manager.py`)

Secure storage and management of sensitive data:

```python
from agent_framework.security import SecretsManager

# Initialize with encryption
secrets = SecretsManager(
    storage_path="secrets.enc",
    use_keyring=True
)

# Store secrets with expiration and rotation
secrets.store_secret(
    "api_key",
    "secret_value",
    expires_in_days=90,
    rotation_interval_days=30,
    tags=["api", "external"]
)

# Retrieve secrets
api_key = secrets.get_secret("api_key")

# Check for rotation needs
needs_rotation = secrets.check_rotation_needed()
```

**Features:**
- AES-256 encryption
- Automatic key derivation
- Secret rotation with configurable intervals
- Expiration management
- Access logging and audit trails
- System keyring integration

### 3. Security Monitor (`src/agent_framework/security/security_monitor.py`)

Real-time security monitoring and threat detection:

```python
from agent_framework.security import SecurityMonitor, SecurityEventType, SecuritySeverity

# Initialize monitoring
monitor = SecurityMonitor(log_file="security.log")

# Start monitoring
await monitor.start_monitoring()

# Log security events
monitor.log_event(
    SecurityEventType.AUTHENTICATION_FAILURE,
    SecuritySeverity.HIGH,
    "auth_service",
    {"user": "admin", "reason": "invalid_password"},
    user_id="admin",
    ip_address="*************"
)

# Add custom threat indicators
from agent_framework.security.security_monitor import ThreatIndicator

indicator = ThreatIndicator(
    name="Custom Threat",
    pattern=r"malicious_pattern",
    severity=SecuritySeverity.HIGH,
    description="Custom threat description"
)
monitor.add_threat_indicator(indicator)
```

**Features:**
- Real-time threat detection
- Configurable threat indicators
- Rate limiting detection
- System anomaly monitoring
- Audit logging with structured data
- Alert callbacks for integration

### 4. Database Connection Pool (`src/agent_framework/shared/database_pool.py`)

Secure database connection management:

```python
from agent_framework.shared.database_pool import DatabasePool, DatabaseConfig, DatabaseType

# Configure database pool
config = DatabaseConfig(
    database_type=DatabaseType.POSTGRESQL,
    connection_string="postgresql://user:pass@localhost/db",
    min_connections=5,
    max_connections=20,
    connection_timeout=30.0
)

# Initialize and use pool
pool = DatabasePool(config)
await pool.initialize()

# Use pooled connections
async with pool.get_connection() as conn:
    result = await conn.fetch("SELECT * FROM users")

# Execute queries with automatic pooling
result = await pool.execute_query("SELECT COUNT(*) FROM users")
```

**Features:**
- Connection pooling for PostgreSQL and SQLite
- Health monitoring and automatic recovery
- Connection timeout and lifecycle management
- Performance statistics and monitoring
- Graceful shutdown and cleanup

### 5. Unified Security Manager (`src/agent_framework/security/__init__.py`)

Integrated security management:

```python
from agent_framework.security import SecurityManager

# Initialize unified security manager
security = SecurityManager(
    secrets_storage_path="secrets.enc",
    monitor_log_file="security.log"
)

# Start all security services
await security.start()

# Validate input with monitoring
result = security.validate_and_monitor(
    "user input",
    input_type="string",
    source="web_api",
    user_id="user123",
    ip_address="*************"
)

# Secure secret operations
security.store_secret_secure("api_key", "value", source="admin", user_id="admin")
api_key = security.get_secret_secure("api_key", source="app", user_id="service")
```

## Installation

1. **Install Dependencies:**
   ```bash
   python scripts/install_security_dependencies.py
   ```

2. **Update Requirements:**
   The security enhancements require additional dependencies that have been added to `requirements.txt`:
   - `cryptography>=41.0.0` - Encryption and key management
   - `keyring>=24.0.0` - System keyring integration
   - `pydantic-settings>=2.0.0` - Configuration management
   - `slowapi>=0.1.9` - Rate limiting
   - `prometheus-client>=0.17.0` - Metrics and monitoring

3. **Configuration:**
   Review and customize the security configuration in `config/security/security.yaml`

## Performance Improvements

### 1. Async Pattern Fixes

**Problem:** Polling loops with fixed sleep intervals causing performance bottlenecks.

**Solution:** Event-driven task completion using `asyncio.Event`:

```python
# Before (polling)
while task_id not in self._task_results:
    await asyncio.sleep(0.1)  # Fixed polling interval

# After (event-driven)
await asyncio.wait_for(
    self._task_completion_events[task_id].wait(),
    timeout=timeout
)
```

### 2. Memory Management

**Problem:** Unbounded caches without proper eviction policies.

**Solution:** Memory-aware cache eviction with monitoring:

```python
# Added memory monitoring
def _should_evict_for_memory(self) -> bool:
    memory_usage = psutil.virtual_memory().percent
    return memory_usage > (self._memory_threshold * 100)

# Memory-aware eviction policy
elif self.policy == CachePolicy.MEMORY_AWARE:
    if self._should_evict_for_memory():
        # Evict largest, least recently used entries first
        entries_by_size = sorted(
            self._entries.items(),
            key=lambda x: (sys.getsizeof(x[1].value), x[1].last_accessed),
            reverse=True
        )
```

## Security Features

### Input Validation

- **SQL Injection Protection**: Detects and blocks SQL injection patterns
- **XSS Prevention**: Sanitizes HTML and blocks script injection
- **Command Injection Protection**: Prevents shell command execution
- **Path Traversal Prevention**: Blocks directory traversal attempts
- **Size Limits**: Configurable limits for strings, lists, and nested structures

### Secrets Management

- **Encryption**: AES-256 encryption for stored secrets
- **Key Management**: Secure key derivation and storage
- **Rotation**: Automatic secret rotation with configurable intervals
- **Expiration**: Time-based secret expiration
- **Audit Logging**: Complete audit trail for secret access

### Security Monitoring

- **Threat Detection**: Pattern-based threat indicator matching
- **Rate Limiting**: Automatic detection of rate limit violations
- **System Monitoring**: CPU, memory, and disk usage anomaly detection
- **Audit Logging**: Structured security event logging
- **Alerting**: Configurable alert callbacks for integration

## Testing

Run the comprehensive test suite:

```bash
# Run all security tests
pytest tests/security/test_security_enhancements.py -v

# Run specific test categories
pytest tests/security/test_security_enhancements.py::TestInputValidator -v
pytest tests/security/test_security_enhancements.py::TestSecretsManager -v
pytest tests/security/test_security_enhancements.py::TestSecurityMonitor -v
```

## Configuration

### Security Configuration (`config/security/security.yaml`)

```yaml
# Input validation settings
max_string_length: 10000
max_list_length: 1000
max_dict_depth: 10

# Rate limiting settings
rate_limit_requests_per_minute: 60
rate_limit_burst_size: 10

# Secrets management settings
secrets_rotation_interval_days: 90
secrets_encryption_enabled: true

# Monitoring settings
security_monitoring_enabled: true
audit_log_retention_days: 365
alert_threshold: 10

# Database connection pooling
db_min_connections: 5
db_max_connections: 20
db_connection_timeout: 30.0
```

## Integration Examples

### Web API Integration

```python
from fastapi import FastAPI, HTTPException, Request
from agent_framework.security import SecurityManager

app = FastAPI()
security = SecurityManager()

@app.middleware("http")
async def security_middleware(request: Request, call_next):
    # Validate request data
    if request.method == "POST":
        body = await request.body()
        result = security.validate_and_monitor(
            body.decode(),
            input_type="json",
            source="web_api",
            ip_address=request.client.host
        )
        
        if not result.is_valid:
            raise HTTPException(status_code=400, detail="Invalid input")
    
    response = await call_next(request)
    return response
```

### Plugin System Integration

```python
class SecurePlugin:
    def __init__(self):
        self.security = SecurityManager()
    
    async def execute_code(self, code: str, user_id: str):
        # Validate code before execution
        result = self.security.validate_and_monitor(
            code,
            input_type="python_code",
            source="plugin_system",
            user_id=user_id
        )
        
        if not result.is_valid:
            raise SecurityError(f"Code validation failed: {result.errors}")
        
        # Execute with monitoring
        return await self._safe_execute(result.sanitized_value)
```

## Monitoring and Alerting

### Metrics Integration

```python
from prometheus_client import Counter, Histogram

# Security metrics
security_events = Counter('security_events_total', 'Total security events', ['event_type', 'severity'])
validation_time = Histogram('input_validation_seconds', 'Input validation time')

# Integration with security monitor
def metrics_callback(alert_data):
    security_events.labels(
        event_type=alert_data.get('event_type', 'unknown'),
        severity=alert_data.get('severity', 'unknown')
    ).inc()

security.monitor.add_alert_callback(metrics_callback)
```

## Best Practices

1. **Regular Security Reviews**: Review security configurations and threat indicators regularly
2. **Secret Rotation**: Implement automated secret rotation for critical credentials
3. **Monitoring Integration**: Integrate with external SIEM systems for centralized monitoring
4. **Testing**: Run security tests as part of CI/CD pipeline
5. **Documentation**: Keep security documentation updated with configuration changes

## Troubleshooting

### Common Issues

1. **Keyring Access Errors**: Ensure system keyring is properly configured
2. **Database Connection Issues**: Verify connection strings and network connectivity
3. **Performance Impact**: Monitor memory usage and adjust cache policies as needed
4. **False Positives**: Fine-tune threat indicators to reduce false positive alerts

### Debug Mode

Enable debug logging for security components:

```python
import logging
logging.getLogger('agent_framework.security').setLevel(logging.DEBUG)
```

## Future Enhancements

- Integration with external threat intelligence feeds
- Machine learning-based anomaly detection
- Advanced encryption key management (HSM support)
- Distributed security monitoring across multiple instances
- Integration with identity providers (OAuth, SAML)
