# Advanced Scenarios and Real-World Examples

This guide demonstrates advanced usage patterns and real-world scenarios for the Agent Framework, covering complex workflows, enterprise integrations, and sophisticated automation patterns.

## Table of Contents

- [Advanced Scenarios and Real-World Examples](#advanced-scenarios-and-real-world-examples)
  - [Table of Contents](#table-of-contents)
  - [Enterprise Code Review Workflows](#enterprise-code-review-workflows)
    - [Automated Pull Request Analysis](#automated-pull-request-analysis)
    - [Team Code Review Assistant](#team-code-review-assistant)
  - [Automated Code Migration](#automated-code-migration)
    - [Python 2 to Python 3 Migration](#python-2-to-python-3-migration)
    - [Legacy Framework Migration](#legacy-framework-migration)

## Enterprise Code Review Workflows

### Automated Pull Request Analysis

```bash
#!/bin/bash
# pr-analysis.sh - Comprehensive PR analysis workflow

PR_NUMBER=$1
BASE_BRANCH=${2:-main}

echo "🔍 Analyzing Pull Request #$PR_NUMBER"

# 1. Get changed files
git diff --name-only origin/$BASE_BRANCH...HEAD | grep '\.py$' > changed_files.txt

# 2. Comprehensive analysis of each changed file
while IFS= read -r file; do
    echo "📊 Analyzing $file..."
    
    # Quality analysis
    python cli.py analyze --file "$file" \
        --type comprehensive \
        --output-format json \
        --save-report "reports/${file//\//_}_analysis.json"
    
    # Security scan
    python cli.py analyze --file "$file" \
        --type security \
        --detailed \
        --output-format json \
        --save-report "reports/${file//\//_}_security.json"
    
    # Performance analysis
    python cli.py analyze --file "$file" \
        --type performance \
        --include-suggestions \
        --save-report "reports/${file//\//_}_performance.md"
        
done < changed_files.txt

# 3. Generate comprehensive PR report
python cli.py generate report \
    --type pr-analysis \
    --input-dir reports/ \
    --output-file "pr_${PR_NUMBER}_analysis.html" \
    --format html \
    --include-metrics \
    --include-recommendations

# 4. Check quality gates
python scripts/quality_gate_check.py \
    --reports-dir reports/ \
    --min-quality-score 7.5 \
    --max-complexity 15 \
    --security-threshold medium

echo "✅ PR analysis complete. Report: pr_${PR_NUMBER}_analysis.html"
```

### Team Code Review Assistant

```python
#!/usr/bin/env python3
"""
Advanced code review assistant that provides intelligent feedback
and suggestions for code improvements.
"""

import asyncio
from pathlib import Path
from typing import List, Dict, Any

from agent_framework.core.agent_orchestrator import AdvancedAgentOrchestrator
from agent_framework.core.types import Task, TaskPriority

class CodeReviewAssistant:
    """Intelligent code review assistant for teams."""
    
    def __init__(self):
        self.orchestrator = AdvancedAgentOrchestrator()
        self.review_criteria = {
            'code_quality': {'weight': 0.3, 'threshold': 7.0},
            'security': {'weight': 0.25, 'threshold': 8.0},
            'performance': {'weight': 0.2, 'threshold': 7.5},
            'maintainability': {'weight': 0.15, 'threshold': 7.0},
            'documentation': {'weight': 0.1, 'threshold': 6.0}
        }
    
    async def review_pull_request(self, pr_files: List[str]) -> Dict[str, Any]:
        """Perform comprehensive review of pull request files."""
        
        review_results = {
            'overall_score': 0.0,
            'file_reviews': {},
            'recommendations': [],
            'blocking_issues': [],
            'approval_status': 'pending'
        }
        
        total_weighted_score = 0.0
        
        for file_path in pr_files:
            print(f"🔍 Reviewing {file_path}...")
            
            # Comprehensive analysis
            file_review = await self._analyze_file_comprehensive(file_path)
            review_results['file_reviews'][file_path] = file_review
            
            # Calculate weighted score
            file_score = self._calculate_weighted_score(file_review['metrics'])
            total_weighted_score += file_score
            
            # Check for blocking issues
            blocking_issues = self._check_blocking_issues(file_review)
            if blocking_issues:
                review_results['blocking_issues'].extend(blocking_issues)
        
        # Calculate overall score
        review_results['overall_score'] = total_weighted_score / len(pr_files)
        
        # Generate recommendations
        review_results['recommendations'] = await self._generate_recommendations(
            review_results['file_reviews']
        )
        
        # Determine approval status
        review_results['approval_status'] = self._determine_approval_status(
            review_results['overall_score'],
            review_results['blocking_issues']
        )
        
        return review_results
    
    async def _analyze_file_comprehensive(self, file_path: str) -> Dict[str, Any]:
        """Perform comprehensive analysis of a single file."""
        
        with open(file_path, 'r') as f:
            code_content = f.read()
        
        # Run multiple analysis types in parallel
        tasks = [
            self.orchestrator.comprehensive_code_analysis(
                code_content=code_content,
                file_path=file_path,
                analysis_depth="comprehensive"
            ),
            self._analyze_security(code_content, file_path),
            self._analyze_performance(code_content, file_path),
            self._analyze_documentation(code_content, file_path)
        ]
        
        results = await asyncio.gather(*tasks)
        
        return {
            'quality_analysis': results[0],
            'security_analysis': results[1],
            'performance_analysis': results[2],
            'documentation_analysis': results[3],
            'metrics': self._extract_metrics(results)
        }
    
    async def _analyze_security(self, code_content: str, file_path: str) -> Dict[str, Any]:
        """Analyze code for security vulnerabilities."""
        
        security_task = Task(
            name="security_analysis",
            description=f"Analyze {file_path} for security vulnerabilities",
            task_type="security_analysis",
            priority=TaskPriority.HIGH,
            parameters={
                "code_content": code_content,
                "file_path": file_path,
                "analysis_type": "comprehensive",
                "include_owasp_checks": True,
                "check_dependencies": True
            }
        )
        
        result = await self.orchestrator.execute_task(security_task)
        return result.result if result.success else {}
    
    async def _analyze_performance(self, code_content: str, file_path: str) -> Dict[str, Any]:
        """Analyze code for performance issues."""
        
        performance_task = Task(
            name="performance_analysis",
            description=f"Analyze {file_path} for performance bottlenecks",
            task_type="performance_analysis",
            priority=TaskPriority.NORMAL,
            parameters={
                "code_content": code_content,
                "file_path": file_path,
                "include_complexity_analysis": True,
                "check_algorithms": True,
                "memory_analysis": True
            }
        )
        
        result = await self.orchestrator.execute_task(performance_task)
        return result.result if result.success else {}
    
    async def _analyze_documentation(self, code_content: str, file_path: str) -> Dict[str, Any]:
        """Analyze code documentation quality."""
        
        doc_task = Task(
            name="documentation_analysis",
            description=f"Analyze documentation quality in {file_path}",
            task_type="documentation_analysis",
            priority=TaskPriority.LOW,
            parameters={
                "code_content": code_content,
                "file_path": file_path,
                "check_docstrings": True,
                "check_comments": True,
                "check_type_hints": True
            }
        )
        
        result = await self.orchestrator.execute_task(doc_task)
        return result.result if result.success else {}
    
    def _extract_metrics(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, float]:
        """Extract numerical metrics from analysis results."""
        
        metrics = {}
        
        # Extract quality metrics
        if analysis_results[0].get('success'):
            quality_data = analysis_results[0]
            metrics['code_quality'] = quality_data.get('quality_score', 0.0)
        
        # Extract security metrics
        if analysis_results[1]:
            security_data = analysis_results[1]
            metrics['security'] = security_data.get('security_score', 0.0)
        
        # Extract performance metrics
        if analysis_results[2]:
            performance_data = analysis_results[2]
            metrics['performance'] = performance_data.get('performance_score', 0.0)
        
        # Extract documentation metrics
        if analysis_results[3]:
            doc_data = analysis_results[3]
            metrics['documentation'] = doc_data.get('documentation_score', 0.0)
        
        return metrics
    
    def _calculate_weighted_score(self, metrics: Dict[str, float]) -> float:
        """Calculate weighted score based on review criteria."""
        
        total_score = 0.0
        
        for criterion, config in self.review_criteria.items():
            if criterion in metrics:
                score = metrics[criterion] * config['weight']
                total_score += score
        
        return total_score
    
    def _check_blocking_issues(self, file_review: Dict[str, Any]) -> List[str]:
        """Check for issues that should block the PR."""
        
        blocking_issues = []
        
        # Check security issues
        security_analysis = file_review.get('security_analysis', {})
        if security_analysis.get('critical_vulnerabilities', 0) > 0:
            blocking_issues.append("Critical security vulnerabilities found")
        
        # Check quality thresholds
        metrics = file_review.get('metrics', {})
        for criterion, config in self.review_criteria.items():
            if criterion in metrics:
                if metrics[criterion] < config['threshold']:
                    blocking_issues.append(
                        f"{criterion.title()} score ({metrics[criterion]:.1f}) "
                        f"below threshold ({config['threshold']})"
                    )
        
        return blocking_issues
    
    async def _generate_recommendations(self, file_reviews: Dict[str, Dict[str, Any]]) -> List[str]:
        """Generate actionable recommendations based on review results."""
        
        recommendations = []
        
        # Analyze patterns across all files
        common_issues = self._identify_common_issues(file_reviews)
        
        for issue_type, files in common_issues.items():
            if len(files) > 1:
                recommendations.append(
                    f"Consider addressing {issue_type} across multiple files: {', '.join(files)}"
                )
        
        # Generate specific recommendations for each file
        for file_path, review in file_reviews.items():
            file_recommendations = self._generate_file_recommendations(file_path, review)
            recommendations.extend(file_recommendations)
        
        return recommendations
    
    def _identify_common_issues(self, file_reviews: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """Identify issues that appear across multiple files."""
        
        issue_patterns = {}
        
        for file_path, review in file_reviews.items():
            quality_analysis = review.get('quality_analysis', {})
            if quality_analysis.get('success'):
                context = quality_analysis.get('context', {})
                issues = context.get('potential_issues', [])
                
                for issue in issues:
                    issue_type = self._categorize_issue(issue)
                    if issue_type not in issue_patterns:
                        issue_patterns[issue_type] = []
                    issue_patterns[issue_type].append(file_path)
        
        return issue_patterns
    
    def _categorize_issue(self, issue: str) -> str:
        """Categorize an issue into a general type."""
        
        issue_lower = issue.lower()
        
        if 'type hint' in issue_lower or 'annotation' in issue_lower:
            return 'missing_type_hints'
        elif 'docstring' in issue_lower:
            return 'missing_documentation'
        elif 'complexity' in issue_lower:
            return 'high_complexity'
        elif 'security' in issue_lower:
            return 'security_concern'
        elif 'performance' in issue_lower:
            return 'performance_issue'
        else:
            return 'general_quality'
    
    def _generate_file_recommendations(self, file_path: str, review: Dict[str, Any]) -> List[str]:
        """Generate specific recommendations for a file."""
        
        recommendations = []
        
        # Quality recommendations
        quality_analysis = review.get('quality_analysis', {})
        if quality_analysis.get('success'):
            context = quality_analysis.get('context', {})
            suggestions = context.get('suggestions', [])
            
            for suggestion in suggestions[:3]:  # Limit to top 3 suggestions
                recommendations.append(f"{file_path}: {suggestion}")
        
        # Security recommendations
        security_analysis = review.get('security_analysis', {})
        security_issues = security_analysis.get('vulnerabilities', [])
        
        for issue in security_issues[:2]:  # Limit to top 2 security issues
            recommendations.append(f"{file_path}: Security - {issue}")
        
        return recommendations
    
    def _determine_approval_status(self, overall_score: float, blocking_issues: List[str]) -> str:
        """Determine the approval status based on score and blocking issues."""
        
        if blocking_issues:
            return 'changes_requested'
        elif overall_score >= 8.0:
            return 'approved'
        elif overall_score >= 7.0:
            return 'approved_with_suggestions'
        else:
            return 'changes_requested'

async def main():
    """Example usage of the Code Review Assistant."""
    
    assistant = CodeReviewAssistant()
    
    # Example: Review files in a pull request
    pr_files = [
        'src/user_service.py',
        'src/auth_handler.py',
        'tests/test_user_service.py'
    ]
    
    print("🚀 Starting comprehensive code review...")
    
    review_results = await assistant.review_pull_request(pr_files)
    
    print(f"\n📊 Review Results:")
    print(f"Overall Score: {review_results['overall_score']:.1f}/10")
    print(f"Approval Status: {review_results['approval_status']}")
    
    if review_results['blocking_issues']:
        print(f"\n🚨 Blocking Issues:")
        for issue in review_results['blocking_issues']:
            print(f"  • {issue}")
    
    if review_results['recommendations']:
        print(f"\n💡 Recommendations:")
        for rec in review_results['recommendations'][:5]:  # Show top 5
            print(f"  • {rec}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Automated Code Migration

### Python 2 to Python 3 Migration

```bash
#!/bin/bash
# migrate-py2-to-py3.sh - Automated Python 2 to 3 migration

PROJECT_DIR=$1
BACKUP_DIR="${PROJECT_DIR}_py2_backup"

echo "🔄 Starting Python 2 to 3 migration for $PROJECT_DIR"

# 1. Create backup
cp -r "$PROJECT_DIR" "$BACKUP_DIR"
echo "✅ Backup created at $BACKUP_DIR"

# 2. Find all Python files
find "$PROJECT_DIR" -name "*.py" > py_files.txt

# 3. Analyze current Python 2 code
echo "📊 Analyzing Python 2 compatibility..."
while IFS= read -r file; do
    python cli.py analyze --file "$file" \
        --type compatibility \
        --target-version python3 \
        --save-report "migration_reports/$(basename $file)_analysis.json"
done < py_files.txt

# 4. Automated migration with validation
echo "🔧 Performing automated migration..."
while IFS= read -r file; do
    echo "Migrating $file..."

    # Use the framework to migrate the file
    python cli.py enhance --file "$file" \
        --goals python3_compatibility \
        --comprehensive \
        --enable-validation \
        --backup-original \
        --output-file "${file}.migrated"

    # Validate the migrated code
    python3 -m py_compile "${file}.migrated"

    if [ $? -eq 0 ]; then
        mv "${file}.migrated" "$file"
        echo "✅ Successfully migrated $file"
    else
        echo "❌ Migration failed for $file, keeping original"
        rm "${file}.migrated"
    fi

done < py_files.txt

# 5. Update setup.py and requirements
echo "📦 Updating package configuration..."
python cli.py enhance --file "$PROJECT_DIR/setup.py" \
    --goals python3_compatibility package_modernization \
    --enable-validation

# 6. Generate migration report
python cli.py generate report \
    --type migration \
    --input-dir migration_reports/ \
    --output-file "python3_migration_report.html" \
    --format html

echo "✅ Migration complete! Report: python3_migration_report.html"
```

### Legacy Framework Migration

```python
#!/usr/bin/env python3
"""
Advanced framework migration assistant for modernizing legacy codebases.
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional

class FrameworkMigrationAssistant:
    """Assists in migrating from legacy frameworks to modern alternatives."""

    def __init__(self, source_framework: str, target_framework: str):
        self.source_framework = source_framework
        self.target_framework = target_framework
        self.migration_patterns = self._load_migration_patterns()
        self.orchestrator = AdvancedAgentOrchestrator()

    async def migrate_project(self, project_path: str) -> Dict[str, Any]:
        """Migrate an entire project from source to target framework."""

        migration_result = {
            'success': False,
            'migrated_files': [],
            'failed_files': [],
            'warnings': [],
            'migration_summary': {}
        }

        # 1. Analyze project structure
        project_analysis = await self._analyze_project_structure(project_path)

        # 2. Create migration plan
        migration_plan = await self._create_migration_plan(project_analysis)

        # 3. Execute migration in phases
        for phase in migration_plan['phases']:
            phase_result = await self._execute_migration_phase(phase)
            migration_result['migrated_files'].extend(phase_result['migrated_files'])
            migration_result['failed_files'].extend(phase_result['failed_files'])
            migration_result['warnings'].extend(phase_result['warnings'])

        # 4. Validate migrated code
        validation_result = await self._validate_migration(project_path)
        migration_result['validation'] = validation_result

        # 5. Generate migration documentation
        await self._generate_migration_docs(migration_result, project_path)

        migration_result['success'] = len(migration_result['failed_files']) == 0
        return migration_result

    async def _analyze_project_structure(self, project_path: str) -> Dict[str, Any]:
        """Analyze the project structure and identify migration candidates."""

        analysis_task = Task(
            name="project_structure_analysis",
            description=f"Analyze {project_path} for {self.source_framework} migration",
            task_type="project_analysis",
            priority=TaskPriority.HIGH,
            parameters={
                "project_path": project_path,
                "source_framework": self.source_framework,
                "target_framework": self.target_framework,
                "include_dependencies": True,
                "analyze_patterns": True
            }
        )

        result = await self.orchestrator.execute_task(analysis_task)
        return result.result if result.success else {}

    async def _create_migration_plan(self, project_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a phased migration plan based on project analysis."""

        plan = {
            'phases': [
                {
                    'name': 'Dependencies and Configuration',
                    'description': 'Update package dependencies and configuration files',
                    'files': project_analysis.get('config_files', []),
                    'priority': 1
                },
                {
                    'name': 'Core Framework Components',
                    'description': 'Migrate core framework-specific code',
                    'files': project_analysis.get('framework_files', []),
                    'priority': 2
                },
                {
                    'name': 'Business Logic',
                    'description': 'Migrate application business logic',
                    'files': project_analysis.get('business_logic_files', []),
                    'priority': 3
                },
                {
                    'name': 'Tests and Documentation',
                    'description': 'Update tests and documentation',
                    'files': project_analysis.get('test_files', []) + project_analysis.get('doc_files', []),
                    'priority': 4
                }
            ],
            'estimated_effort': self._estimate_migration_effort(project_analysis),
            'risk_assessment': self._assess_migration_risks(project_analysis)
        }

        return plan

    async def _execute_migration_phase(self, phase: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single migration phase."""

        phase_result = {
            'migrated_files': [],
            'failed_files': [],
            'warnings': []
        }

        for file_path in phase['files']:
            try:
                migration_result = await self._migrate_single_file(file_path)

                if migration_result['success']:
                    phase_result['migrated_files'].append(file_path)
                else:
                    phase_result['failed_files'].append({
                        'file': file_path,
                        'error': migration_result['error']
                    })

                phase_result['warnings'].extend(migration_result.get('warnings', []))

            except Exception as e:
                phase_result['failed_files'].append({
                    'file': file_path,
                    'error': str(e)
                })

        return phase_result

    async def _migrate_single_file(self, file_path: str) -> Dict[str, Any]:
        """Migrate a single file from source to target framework."""

        with open(file_path, 'r') as f:
            original_content = f.read()

        # Use the framework to perform the migration
        migration_requirements = {
            'type': 'framework_migration',
            'source_framework': self.source_framework,
            'target_framework': self.target_framework,
            'migration_patterns': self.migration_patterns,
            'preserve_functionality': True,
            'add_compatibility_layer': True
        }

        result = await self.orchestrator.advanced_code_implementation(
            requirements=migration_requirements,
            file_path=file_path,
            existing_code=original_content
        )

        if result['success']:
            # Write migrated code to file
            migrated_code = result['generated_code']
            with open(file_path, 'w') as f:
                f.write(migrated_code.code if hasattr(migrated_code, 'code') else str(migrated_code))

        return result

    def _load_migration_patterns(self) -> Dict[str, Any]:
        """Load migration patterns for the specific framework combination."""

        # This would typically load from a configuration file or database
        patterns = {
            'django_to_fastapi': {
                'url_patterns': {
                    'from': r'url\(r\'^(.+)\$\', (.+), name=\'(.+)\'\)',
                    'to': '@app.get("/{}")\nasync def {}():'
                },
                'model_fields': {
                    'CharField': 'str',
                    'IntegerField': 'int',
                    'BooleanField': 'bool'
                },
                'imports': {
                    'from django.http import HttpResponse': 'from fastapi import FastAPI',
                    'from django.shortcuts import render': 'from fastapi.templating import Jinja2Templates'
                }
            },
            'flask_to_fastapi': {
                'route_decorators': {
                    'from': r'@app\.route\([\'"](.+)[\'"]\)',
                    'to': '@app.get("{}")'
                },
                'request_handling': {
                    'request.form': 'form_data: Form',
                    'request.json': 'json_data: dict'
                }
            }
        }

        migration_key = f"{self.source_framework}_to_{self.target_framework}"
        return patterns.get(migration_key, {})

    def _estimate_migration_effort(self, project_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate the effort required for migration."""

        total_files = len(project_analysis.get('all_files', []))
        framework_specific_files = len(project_analysis.get('framework_files', []))

        # Simple effort estimation based on file count and complexity
        effort_hours = {
            'configuration': framework_specific_files * 0.5,
            'core_migration': framework_specific_files * 2,
            'testing': total_files * 0.5,
            'documentation': total_files * 0.25
        }

        return {
            'total_hours': sum(effort_hours.values()),
            'breakdown': effort_hours,
            'complexity': 'high' if framework_specific_files > 20 else 'medium' if framework_specific_files > 5 else 'low'
        }

    def _assess_migration_risks(self, project_analysis: Dict[str, Any]) -> List[str]:
        """Assess potential risks in the migration."""

        risks = []

        if project_analysis.get('custom_framework_extensions'):
            risks.append("Custom framework extensions may require manual migration")

        if project_analysis.get('deprecated_features'):
            risks.append("Deprecated features detected that may not have direct equivalents")

        if project_analysis.get('complex_database_queries'):
            risks.append("Complex database queries may need restructuring")

        if project_analysis.get('third_party_integrations'):
            risks.append("Third-party integrations may need compatibility updates")

        return risks

    async def _validate_migration(self, project_path: str) -> Dict[str, Any]:
        """Validate the migrated code for correctness and functionality."""

        validation_result = {
            'syntax_valid': True,
            'tests_passing': False,
            'performance_impact': {},
            'compatibility_issues': []
        }

        # Run syntax validation
        syntax_check = await self._check_syntax(project_path)
        validation_result['syntax_valid'] = syntax_check['valid']

        # Run tests if available
        test_result = await self._run_tests(project_path)
        validation_result['tests_passing'] = test_result['success']

        # Performance comparison
        performance_result = await self._compare_performance(project_path)
        validation_result['performance_impact'] = performance_result

        return validation_result

    async def _generate_migration_docs(self, migration_result: Dict[str, Any], project_path: str):
        """Generate comprehensive migration documentation."""

        doc_content = f"""
# {self.source_framework.title()} to {self.target_framework.title()} Migration Report

## Migration Summary
- **Total Files Processed**: {len(migration_result['migrated_files']) + len(migration_result['failed_files'])}
- **Successfully Migrated**: {len(migration_result['migrated_files'])}
- **Failed Migrations**: {len(migration_result['failed_files'])}
- **Warnings**: {len(migration_result['warnings'])}

## Migrated Files
{chr(10).join(f"- {file}" for file in migration_result['migrated_files'])}

## Failed Migrations
{chr(10).join(f"- {item['file']}: {item['error']}" for item in migration_result['failed_files'])}

## Post-Migration Steps
1. Review and test all migrated functionality
2. Update deployment configurations
3. Train team on new framework patterns
4. Monitor performance in production

## Framework-Specific Changes
- Updated routing patterns to {self.target_framework} syntax
- Migrated database models and queries
- Updated template rendering logic
- Converted middleware to {self.target_framework} equivalents
"""

        with open(f"{project_path}/MIGRATION_REPORT.md", 'w') as f:
            f.write(doc_content)

# Example usage
async def migrate_django_to_fastapi():
    """Example: Migrate a Django project to FastAPI."""

    assistant = FrameworkMigrationAssistant('django', 'fastapi')

    result = await assistant.migrate_project('/path/to/django/project')

    if result['success']:
        print("✅ Migration completed successfully!")
    else:
        print(f"❌ Migration completed with {len(result['failed_files'])} failures")

    print(f"📊 Migration Summary:")
    print(f"  - Migrated: {len(result['migrated_files'])} files")
    print(f"  - Failed: {len(result['failed_files'])} files")
    print(f"  - Warnings: {len(result['warnings'])}")
```
