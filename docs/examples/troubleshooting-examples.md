# Troubleshooting Examples

This guide provides practical examples for diagnosing and resolving common issues when using the Agent Framework.

## Table of Contents

- [Troubleshooting Examples](#troubleshooting-examples)
  - [Table of Contents](#table-of-contents)
  - [Common Setup Issues](#common-setup-issues)
    - [Issue 1: Framework Not Found](#issue-1-framework-not-found)
    - [Issue 2: Configuration Problems](#issue-2-configuration-problems)
  - [API Connection Problems](#api-connection-problems)
    - [Issue 3: API Rate Limiting](#issue-3-api-rate-limiting)
    - [Issue 4: Model Access Denied](#issue-4-model-access-denied)
  - [Performance Issues](#performance-issues)
    - [Issue 5: Slow Analysis Performance](#issue-5-slow-analysis-performance)
    - [Issue 6: Memory Issues](#issue-6-memory-issues)
  - [Multi-Agent Problems](#multi-agent-problems)
    - [Issue 7: Agent Communication Failures](#issue-7-agent-communication-failures)

## Common Setup Issues

### Issue 1: Framework Not Found

**Problem**: `ModuleNotFoundError: No module named 'agent_framework'`

**Diagnosis**:

```bash
# Check if the framework is installed
pip list | grep agent-framework
```

**Solutions**:

```bash
# Solution 1: Install the framework
pip install agent-framework

# Solution 2: Install in development mode
git clone https://github.com/your-org/agent-framework.git
cd agent-framework
pip install -e .

# Solution 3: Check Python environment
which python
python --version
pip --version

# Solution 4: Virtual environment issues
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install agent-framework
```

**Verification**:

```bash
python -c "import agent_framework; print('✅ Framework imported successfully')"
```

### Issue 2: Configuration Problems

**Problem**: `ConfigurationError: API key not found`

**Diagnosis**:

```bash
# Check environment variables
echo $AGENT_API_KEY
env | grep AGENT

# Check configuration file
cat ~/.agent_framework/config.yaml
```

**Solutions**:

```bash
# Solution 1: Set environment variable
export AGENT_API_KEY="your-api-key-here"
export AGENT_MODEL="gpt-4o"

# Solution 2: Create configuration file
mkdir -p ~/.agent_framework
cat > ~/.agent_framework/config.yaml << 'EOF'
model:
  api_key: "your-api-key-here"
  model: "gpt-4o"
  base_url: "https://api.openai.com/v1"
EOF

# Solution 3: Use command-line configuration
python cli.py configure --api-key "your-api-key" --model "gpt-4o"

# Solution 4: Verify configuration
python cli.py status
```

**Expected Output**:

```
🤖 Agent Framework Status
========================
✅ API Connection: Active
✅ Model: gpt-4o (OpenAI)
✅ Configuration: Valid
```

## API Connection Problems

### Issue 3: API Rate Limiting

**Problem**: `RateLimitError: Too many requests`

**Diagnosis**:

```bash
# Check current usage
python cli.py debug --check-api-limits

# Monitor API calls
python cli.py analyze --file test.py --verbose --debug
```

**Solutions**:

```python
#!/usr/bin/env python3
"""
Rate limiting solution with exponential backoff.
"""

import asyncio
import time
from typing import Optional

class RateLimitHandler:
    def __init__(self, max_requests_per_minute: int = 60):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self.retry_delays = [1, 2, 4, 8, 16]  # Exponential backoff
    
    async def wait_if_needed(self):
        """Wait if we're approaching rate limits."""
        now = time.time()
        
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]
        
        # If we're at the limit, wait
        if len(self.requests) >= self.max_requests:
            wait_time = 60 - (now - self.requests[0])
            if wait_time > 0:
                print(f"⏳ Rate limit reached, waiting {wait_time:.1f}s...")
                await asyncio.sleep(wait_time)
        
        self.requests.append(now)
    
    async def execute_with_retry(self, func, *args, **kwargs):
        """Execute function with exponential backoff retry."""
        for attempt, delay in enumerate(self.retry_delays):
            try:
                await self.wait_if_needed()
                return await func(*args, **kwargs)
            except Exception as e:
                if "rate limit" in str(e).lower() and attempt < len(self.retry_delays) - 1:
                    print(f"⚠️  Rate limited, retrying in {delay}s... (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                else:
                    raise

# Usage example
async def analyze_with_rate_limiting():
    handler = RateLimitHandler(max_requests_per_minute=30)  # Conservative limit
    
    files = ["file1.py", "file2.py", "file3.py"]
    
    for file in files:
        try:
            result = await handler.execute_with_retry(
                analyze_file_async, file
            )
            print(f"✅ Analyzed {file}")
        except Exception as e:
            print(f"❌ Failed to analyze {file}: {e}")
```

**Configuration Solution**:

```yaml
# config.yaml - Rate limiting configuration
model:
  api_key: "your-key"
  model: "gpt-4o"
  rate_limiting:
    enabled: true
    max_requests_per_minute: 30
    retry_attempts: 5
    exponential_backoff: true
```

### Issue 4: Model Access Denied

**Problem**: `AuthenticationError: Invalid API key` or `PermissionError: Model not accessible`

**Diagnosis**:

```bash
# Test API key directly
curl -H "Authorization: Bearer $AGENT_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model":"gpt-4o","messages":[{"role":"user","content":"test"}],"max_tokens":5}' \
     https://api.openai.com/v1/chat/completions

# Check available models
python cli.py debug --list-available-models
```

**Solutions**:

```bash
# Solution 1: Verify API key format
echo "API Key length: ${#AGENT_API_KEY}"
echo "API Key prefix: ${AGENT_API_KEY:0:7}"

# Solution 2: Test with different model
export AGENT_MODEL="gpt-3.5-turbo"
python cli.py analyze --code "print('test')" --type basic

# Solution 3: Use alternative provider
export AGENT_BASE_URL="https://openrouter.ai/api/v1"
export AGENT_MODEL="qwen/qwen3-coder:free"

# Solution 4: Check account status
python cli.py debug --check-account-status
```

## Performance Issues

### Issue 5: Slow Analysis Performance

**Problem**: Analysis takes too long or times out

**Diagnosis**:

```bash
# Profile analysis performance
python cli.py analyze --file large_file.py --profile --verbose

# Check system resources
python cli.py debug --system-info
```

**Solutions**:

```python
#!/usr/bin/env python3
"""
Performance optimization strategies.
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

class PerformanceOptimizer:
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def analyze_files_parallel(self, files: List[str]) -> Dict[str, Any]:
        """Analyze multiple files in parallel."""
        
        # Split files into chunks for parallel processing
        chunk_size = max(1, len(files) // self.max_workers)
        file_chunks = [files[i:i + chunk_size] for i in range(0, len(files), chunk_size)]
        
        # Process chunks in parallel
        tasks = [
            self.analyze_chunk(chunk) for chunk in file_chunks
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Combine results
        combined_results = {}
        for chunk_result in results:
            combined_results.update(chunk_result)
        
        return combined_results
    
    async def analyze_chunk(self, files: List[str]) -> Dict[str, Any]:
        """Analyze a chunk of files."""
        results = {}
        
        for file in files:
            try:
                # Use lightweight analysis for large batches
                result = await self.quick_analyze(file)
                results[file] = result
            except Exception as e:
                results[file] = {"error": str(e)}
        
        return results
    
    async def quick_analyze(self, file: str) -> Dict[str, Any]:
        """Perform quick analysis optimized for speed."""
        
        # Read file efficiently
        with open(file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Skip very large files
        if len(content) > 100000:  # 100KB limit
            return {
                "status": "skipped",
                "reason": "file_too_large",
                "size": len(content)
            }
        
        # Use basic analysis for speed
        return await self.orchestrator.analyze_basic(content, file)

# Configuration for performance
performance_config = {
    "analysis": {
        "max_file_size": 100000,  # 100KB
        "timeout": 30,  # 30 seconds
        "parallel_workers": 4,
        "cache_enabled": True,
        "lightweight_mode": True
    },
    "model": {
        "max_tokens": 2048,  # Reduce for faster responses
        "temperature": 0.1,  # Lower for consistent, faster results
    }
}
```

**Command-line Solutions**:

```bash
# Solution 1: Use lightweight analysis
python cli.py analyze --file large_file.py --type basic --fast

# Solution 2: Analyze in chunks
find src/ -name "*.py" | head -10 | xargs -P 4 -I {} python cli.py analyze --file {} --type basic

# Solution 3: Use caching
python cli.py analyze --file file.py --cache --cache-ttl 3600

# Solution 4: Reduce scope
python cli.py analyze --file file.py --type quality --no-security --no-performance
```

### Issue 6: Memory Issues

**Problem**: `MemoryError` or high memory usage

**Diagnosis**:

```bash
# Monitor memory usage
python cli.py debug --memory-profile --file large_file.py

# Check system memory
free -h
ps aux | grep python | head -5
```

**Solutions**:

```python
#!/usr/bin/env python3
"""
Memory-efficient processing strategies.
"""

import gc
from typing import Iterator, List
import psutil
import os

class MemoryManager:
    def __init__(self, max_memory_mb: int = 1024):
        self.max_memory_mb = max_memory_mb
        self.process = psutil.Process(os.getpid())
    
    def check_memory_usage(self) -> float:
        """Check current memory usage in MB."""
        memory_info = self.process.memory_info()
        return memory_info.rss / 1024 / 1024  # Convert to MB
    
    def cleanup_if_needed(self):
        """Force garbage collection if memory usage is high."""
        current_memory = self.check_memory_usage()
        
        if current_memory > self.max_memory_mb * 0.8:  # 80% threshold
            print(f"⚠️  High memory usage: {current_memory:.1f}MB, cleaning up...")
            gc.collect()
            
            new_memory = self.check_memory_usage()
            print(f"✅ Memory after cleanup: {new_memory:.1f}MB")
    
    def process_files_streaming(self, files: List[str]) -> Iterator[Dict[str, Any]]:
        """Process files one at a time to minimize memory usage."""
        
        for i, file in enumerate(files):
            try:
                # Check memory before processing each file
                self.cleanup_if_needed()
                
                # Process single file
                result = self.process_single_file(file)
                yield {file: result}
                
                # Cleanup after each file
                if i % 10 == 0:  # Every 10 files
                    gc.collect()
                    
            except MemoryError:
                print(f"❌ Memory error processing {file}, skipping...")
                yield {file: {"error": "memory_error"}}
    
    def process_single_file(self, file: str) -> Dict[str, Any]:
        """Process a single file with memory constraints."""
        
        # Read file in chunks if it's large
        file_size = os.path.getsize(file)
        
        if file_size > 10 * 1024 * 1024:  # 10MB
            return {"status": "skipped", "reason": "file_too_large"}
        
        with open(file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Process with memory monitoring
        initial_memory = self.check_memory_usage()
        
        try:
            result = self.analyze_content(content)
            
            final_memory = self.check_memory_usage()
            result['memory_used'] = final_memory - initial_memory
            
            return result
            
        finally:
            # Ensure cleanup
            del content
            gc.collect()

# Usage example
def analyze_large_project_efficiently():
    """Analyze a large project with memory constraints."""
    
    memory_manager = MemoryManager(max_memory_mb=512)  # 512MB limit
    
    # Get all Python files
    import glob
    files = glob.glob("**/*.py", recursive=True)
    
    print(f"📁 Found {len(files)} Python files")
    
    results = {}
    processed = 0
    
    # Process files with memory management
    for file_result in memory_manager.process_files_streaming(files):
        results.update(file_result)
        processed += 1
        
        if processed % 50 == 0:
            current_memory = memory_manager.check_memory_usage()
            print(f"📊 Processed {processed}/{len(files)} files, Memory: {current_memory:.1f}MB")
    
    return results
```

**Command-line Solutions**:

```bash
# Solution 1: Process files individually
for file in $(find src/ -name "*.py"); do
    python cli.py analyze --file "$file" --type basic --memory-limit 512
done

# Solution 2: Use streaming mode
python cli.py analyze --directory src/ --streaming --memory-limit 1024

# Solution 3: Reduce analysis scope
python cli.py analyze --file large_file.py --type basic --no-detailed-analysis

# Solution 4: Split large files
split -l 1000 large_file.py large_file_part_
for part in large_file_part_*; do
    python cli.py analyze --file "$part" --type basic
done
```

## Multi-Agent Problems

### Issue 7: Agent Communication Failures

**Problem**: Agents not communicating or coordinating properly

**Diagnosis**:

```bash
# Check multi-agent status
python cli.py debug --multi-agent-status

# Monitor agent communication
python cli.py debug --trace-agent-communication --verbose
```

**Solutions**:

```python
#!/usr/bin/env python3
"""
Multi-agent debugging and recovery strategies.
"""

import asyncio
from typing import Dict, List, Any
import logging

class MultiAgentDebugger:
    def __init__(self, orchestrator):
        self.orchestrator = orchestrator
        self.logger = logging.getLogger(__name__)
    
    async def diagnose_agent_issues(self) -> Dict[str, Any]:
        """Comprehensive diagnosis of multi-agent system."""
        
        diagnosis = {
            "agent_registry": await self.check_agent_registry(),
            "communication": await self.test_agent_communication(),
            "load_balancing": await self.check_load_balancing(),
            "task_distribution": await self.test_task_distribution(),
            "performance": await self.measure_agent_performance()
        }
        
        return diagnosis
    
    async def check_agent_registry(self) -> Dict[str, Any]:
        """Check agent registry status."""
        try:
            status = await self.orchestrator.get_multi_agent_status()
            
            return {
                "status": "healthy",
                "total_agents": status.get("registry_stats", {}).get("total_agents", 0),
                "active_agents": status.get("registry_stats", {}).get("active_agents", 0),
                "capabilities": status.get("available_capabilities", [])
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def test_agent_communication(self) -> Dict[str, Any]:
        """Test communication between agents."""
        
        test_results = {}
        
        try:
            # Get list of active agents
            agents = await self.orchestrator.get_active_agents()
            
            for agent_id in agents:
                try:
                    # Send ping to agent
                    response = await self.orchestrator.ping_agent(agent_id)
                    test_results[agent_id] = {
                        "status": "responsive",
                        "response_time": response.get("response_time", 0)
                    }
                except Exception as e:
                    test_results[agent_id] = {
                        "status": "unresponsive",
                        "error": str(e)
                    }
            
            return test_results
            
        except Exception as e:
            return {"error": str(e)}
    
    async def recover_failed_agents(self) -> Dict[str, Any]:
        """Attempt to recover failed agents."""
        
        recovery_results = {}
        
        # Get agent status
        diagnosis = await self.diagnose_agent_issues()
        
        # Identify failed agents
        failed_agents = []
        communication_results = diagnosis.get("communication", {})
        
        for agent_id, status in communication_results.items():
            if status.get("status") == "unresponsive":
                failed_agents.append(agent_id)
        
        # Attempt recovery
        for agent_id in failed_agents:
            try:
                self.logger.info(f"Attempting to recover agent {agent_id}")
                
                # Restart agent
                await self.orchestrator.restart_agent(agent_id)
                
                # Wait for agent to come online
                await asyncio.sleep(2)
                
                # Test communication
                response = await self.orchestrator.ping_agent(agent_id)
                
                recovery_results[agent_id] = {
                    "status": "recovered",
                    "response_time": response.get("response_time", 0)
                }
                
            except Exception as e:
                recovery_results[agent_id] = {
                    "status": "recovery_failed",
                    "error": str(e)
                }
        
        return recovery_results

# Usage example
async def debug_multi_agent_system():
    """Debug and recover multi-agent system."""
    
    debugger = MultiAgentDebugger(orchestrator)
    
    print("🔍 Diagnosing multi-agent system...")
    diagnosis = await debugger.diagnose_agent_issues()
    
    print("📊 Diagnosis Results:")
    for component, status in diagnosis.items():
        print(f"  {component}: {status}")
    
    # Check if recovery is needed
    communication = diagnosis.get("communication", {})
    failed_agents = [
        agent_id for agent_id, status in communication.items()
        if status.get("status") == "unresponsive"
    ]
    
    if failed_agents:
        print(f"🔧 Attempting to recover {len(failed_agents)} failed agents...")
        recovery_results = await debugger.recover_failed_agents()
        
        for agent_id, result in recovery_results.items():
            if result.get("status") == "recovered":
                print(f"✅ Agent {agent_id} recovered successfully")
            else:
                print(f"❌ Failed to recover agent {agent_id}: {result.get('error')}")
    else:
        print("✅ All agents are responsive")
```

**Command-line Solutions**:

```bash
# Solution 1: Restart multi-agent system
python cli.py multi-agent restart --force

# Solution 2: Check agent health
python cli.py debug --check-agents --verbose

# Solution 3: Reset agent registry
python cli.py multi-agent reset-registry

# Solution 4: Increase communication timeout
python cli.py configure --multi-agent-timeout 60
```
