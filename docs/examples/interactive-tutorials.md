# Interactive Tutorial Examples

This guide provides step-by-step interactive tutorials with expected outputs, explanations, and hands-on exercises to help you master the Agent Framework.

## Table of Contents

1. [Getting Started Tutorial](#getting-started-tutorial)
2. [Code Analysis Walkthrough](#code-analysis-walkthrough)
3. [Code Generation Workshop](#code-generation-workshop)
4. [Debugging Session](#debugging-session)
5. [Multi-Agent Coordination Tutorial](#multi-agent-coordination-tutorial)
6. [Advanced Workflow Tutorial](#advanced-workflow-tutorial)
7. [Troubleshooting Guide](#troubleshooting-guide)

## Getting Started Tutorial

### Tutorial 1: Your First AI Assistant Session

**Objective**: Learn the basics of using the Agent Framework for code analysis and improvement.

**Prerequisites**:

- Agent Framework installed
- API key configured
- Basic Python knowledge

#### Step 1: Verify Your Setup

```bash
# Check if the framework is properly installed
python cli.py --version
```

**Expected Output**:

```
Agent Framework v2.1.0
✅ Framework installed successfully
```

**If you see an error**: Check the [installation guide](../getting-started/installation.md)

#### Step 2: Test Basic Functionality

```bash
# Simple test to verify API connection
python cli.py status
```

**Expected Output**:

```
🤖 Agent Framework Status
========================
✅ API Connection: Active
✅ Model: gpt-4o (OpenAI)
✅ Plugins: 5 loaded
✅ Multi-Agent: Disabled
📊 System Ready
```

**What this tells you**:

- ✅ Your API key is working
- ✅ The model is accessible
- ✅ Core plugins are loaded
- ✅ System is ready for use

#### Step 3: Analyze Your First Code

Create a simple Python file to analyze:

```bash
# Create a sample file
cat > sample.py << 'EOF'
def calculate_total(items):
    total = 0
    for i in range(len(items)):
        total = total + items[i]
    return total

def get_user_by_id(user_id):
    users = [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]
    for user in users:
        if user["id"] == user_id:
            return user
    return None
EOF
```

Now analyze it:

```bash
python cli.py analyze --file sample.py --type basic
```

**Expected Output**:

```
🔍 Code Analysis Results for sample.py
=====================================

📊 Overall Quality Score: 6.5/10

🔍 Issues Found:
├── calculate_total(): Inefficient loop pattern
├── calculate_total(): Missing type hints
├── get_user_by_id(): Linear search inefficiency
└── Both functions: Missing docstrings

💡 Suggestions:
├── Use sum() function instead of manual loop
├── Add type annotations for better code clarity
├── Consider using dictionary for O(1) user lookup
└── Add docstrings to document function behavior

⏱️  Analysis completed in 1.2s
```

**What this means**:

- **Quality Score 6.5/10**: Room for improvement
- **Issues**: Specific problems identified
- **Suggestions**: Actionable recommendations

#### Step 4: Apply AI Suggestions

Let's improve the code using AI suggestions:

```bash
python cli.py enhance --file sample.py --goals quality efficiency
```

**Expected Output**:

```
🔧 Enhancing sample.py...
✅ Applied 4 improvements
📊 Quality Score: 6.5 → 8.7 (+2.2)

🔄 Changes Applied:
├── Replaced manual loop with sum() function
├── Added comprehensive type hints
├── Optimized user lookup with dictionary
└── Added detailed docstrings

💾 Enhanced code saved to sample.py
```

**View the improved code**:

```bash
cat sample.py
```

**Expected Enhanced Code**:

```python
from typing import List, Dict, Optional, Union

def calculate_total(items: List[Union[int, float]]) -> Union[int, float]:
    """Calculate the total sum of numeric items.
    
    Args:
        items: List of numeric values to sum
        
    Returns:
        The total sum of all items
        
    Example:
        >>> calculate_total([1, 2, 3, 4])
        10
    """
    return sum(items)

def get_user_by_id(user_id: int) -> Optional[Dict[str, Union[int, str]]]:
    """Retrieve user information by ID.
    
    Args:
        user_id: The unique identifier for the user
        
    Returns:
        User dictionary if found, None otherwise
        
    Example:
        >>> get_user_by_id(1)
        {'id': 1, 'name': 'Alice'}
    """
    users_dict = {1: {"id": 1, "name": "Alice"}, 2: {"id": 2, "name": "Bob"}}
    return users_dict.get(user_id)
```

**Key Improvements**:

- ✅ Type hints added for better code clarity
- ✅ Efficient `sum()` function replaces manual loop
- ✅ Dictionary lookup replaces linear search
- ✅ Comprehensive docstrings with examples

#### Step 5: Verify Improvements

```bash
python cli.py analyze --file sample.py --type basic
```

**Expected Output**:

```
🔍 Code Analysis Results for sample.py
=====================================

📊 Overall Quality Score: 8.7/10 ⬆️ (+2.2)

✅ Improvements Detected:
├── Efficient algorithms implemented
├── Complete type annotation coverage
├── Comprehensive documentation
└── Clean, readable code structure

🎯 Remaining Suggestions:
├── Consider adding input validation
└── Add unit tests for better reliability

⏱️  Analysis completed in 0.8s
```

**Congratulations!** You've successfully:

- ✅ Analyzed code quality
- ✅ Applied AI-powered improvements
- ✅ Increased code quality by 2.2 points
- ✅ Learned the basic workflow

---

## Code Analysis Walkthrough

### Tutorial 2: Deep Code Analysis

**Objective**: Learn to perform comprehensive code analysis and understand different analysis types.

#### Step 1: Create a Complex Example

```bash
cat > complex_example.py << 'EOF'
import requests
import json

class UserService:
    def __init__(self):
        self.base_url = "https://api.example.com"
        self.users = []
    
    def fetch_user(self, user_id):
        url = f"{self.base_url}/users/{user_id}"
        response = requests.get(url)
        if response.status_code == 200:
            user_data = json.loads(response.text)
            self.users.append(user_data)
            return user_data
        else:
            return None
    
    def process_users(self, user_ids):
        results = []
        for uid in user_ids:
            try:
                user = self.fetch_user(uid)
                if user:
                    if user['age'] > 18:
                        user['category'] = 'adult'
                    else:
                        user['category'] = 'minor'
                    results.append(user)
            except:
                pass
        return results
    
    def calculate_stats(self, users):
        if len(users) == 0:
            return None
        
        total_age = 0
        for user in users:
            total_age += user['age']
        
        average_age = total_age / len(users)
        
        adults = 0
        for user in users:
            if user['age'] > 18:
                adults += 1
        
        return {
            'total_users': len(users),
            'average_age': average_age,
            'adult_percentage': (adults / len(users)) * 100
        }
EOF
```

#### Step 2: Comprehensive Analysis

```bash
python cli.py analyze --file complex_example.py --type comprehensive --detailed
```

**Expected Output**:

```
🔍 Comprehensive Analysis: complex_example.py
===========================================

📊 Quality Metrics:
├── Overall Score: 4.2/10 ⚠️
├── Complexity Score: 6.8/10
├── Security Score: 3.1/10 🚨
├── Performance Score: 5.5/10
└── Maintainability: 4.0/10

🚨 Critical Issues (5):
├── Security: Unvalidated API requests (Line 12)
├── Security: Bare except clause (Line 25)
├── Performance: Inefficient loops (Lines 35, 41)
├── Error Handling: Missing exception handling (Line 13)
└── Code Quality: Magic numbers (Line 19, 42)

⚠️  Major Issues (8):
├── Missing type hints throughout
├── No input validation
├── Hardcoded URLs
├── Inefficient data structures
├── Missing docstrings
├── Poor error messages
├── No logging
└── Potential memory leaks

💡 Improvement Suggestions:
├── Add comprehensive error handling
├── Implement input validation
├── Use type hints for better code clarity
├── Add proper logging
├── Optimize loops and data access
├── Extract configuration to constants
├── Add comprehensive docstrings
└── Implement proper exception handling

🔧 Complexity Analysis:
├── Cyclomatic Complexity: 12 (High)
├── Cognitive Complexity: 15 (High)
├── Lines of Code: 45
└── Functions: 4 (avg complexity: 3.0)

🔒 Security Analysis:
├── Potential SQL Injection: Not detected
├── XSS Vulnerabilities: Not applicable
├── Insecure API Calls: 1 found
├── Hardcoded Secrets: None detected
└── Input Validation: Missing

⚡ Performance Analysis:
├── Algorithmic Complexity: O(n²) in places
├── Memory Usage: Moderate concern
├── I/O Operations: Synchronous (blocking)
└── Optimization Potential: High

📈 Maintainability:
├── Code Duplication: 15% (moderate)
├── Function Length: Acceptable
├── Class Cohesion: Low
└── Coupling: Moderate

⏱️  Analysis completed in 3.4s
```

#### Step 3: Security-Focused Analysis

```bash
python cli.py analyze --file complex_example.py --type security --detailed
```

**Expected Output**:

```
🔒 Security Analysis: complex_example.py
======================================

🚨 Security Score: 3.1/10 (Critical)

🚨 Critical Vulnerabilities (2):
├── Unvalidated HTTP Requests (Line 12)
│   ├── Risk: Server-Side Request Forgery (SSRF)
│   ├── Impact: High
│   └── Fix: Validate URLs and use allowlists
│
└── Bare Exception Handling (Line 25)
    ├── Risk: Hidden security exceptions
    ├── Impact: Medium
    └── Fix: Catch specific exceptions

⚠️  High Risk Issues (3):
├── No Input Validation (Lines 11, 18)
│   ├── Risk: Injection attacks
│   └── Fix: Validate all user inputs
│
├── Hardcoded API Endpoint (Line 7)
│   ├── Risk: Information disclosure
│   └── Fix: Use environment variables
│
└── No Rate Limiting (Line 12)
    ├── Risk: DoS vulnerability
    └── Fix: Implement request throttling

🛡️  Security Recommendations:
├── Implement input validation for all user data
├── Use parameterized queries for database operations
├── Add proper authentication and authorization
├── Implement rate limiting for API calls
├── Use HTTPS for all external communications
├── Add comprehensive logging for security events
├── Implement proper error handling without information leakage
└── Regular security audits and dependency updates

🔍 OWASP Top 10 Assessment:
├── A01 Broken Access Control: Not assessed
├── A02 Cryptographic Failures: Low risk
├── A03 Injection: Medium risk ⚠️
├── A04 Insecure Design: High risk 🚨
├── A05 Security Misconfiguration: Medium risk ⚠️
├── A06 Vulnerable Components: Not assessed
├── A07 Identity/Auth Failures: Not assessed
├── A08 Software Integrity: Low risk
├── A09 Logging/Monitoring: High risk 🚨
└── A10 Server-Side Request Forgery: High risk 🚨

⏱️  Security analysis completed in 2.1s
```

#### Step 4: Performance Analysis

```bash
python cli.py analyze --file complex_example.py --type performance
```

**Expected Output**:

```
⚡ Performance Analysis: complex_example.py
=========================================

📊 Performance Score: 5.5/10

🐌 Performance Issues (4):
├── Inefficient Loop Pattern (Lines 35-36)
│   ├── Current: O(n) manual summation
│   ├── Suggested: Use sum() function
│   └── Impact: 2x faster for large datasets
│
├── Redundant Loop (Lines 41-43)
│   ├── Current: Separate loop for counting
│   ├── Suggested: Combine with previous loop
│   └── Impact: 50% fewer iterations
│
├── Synchronous HTTP Requests (Line 12)
│   ├── Current: Blocking I/O operations
│   ├── Suggested: Use async/await pattern
│   └── Impact: 10x faster for multiple requests
│
└── Inefficient Data Structure (Line 8)
    ├── Current: List for user storage
    ├── Suggested: Dictionary for O(1) lookup
    └── Impact: Better scalability

🚀 Optimization Opportunities:
├── Replace manual loops with built-in functions
├── Implement async/await for I/O operations
├── Use list comprehensions where appropriate
├── Cache frequently accessed data
├── Implement connection pooling for HTTP requests
└── Use generators for memory efficiency

📈 Performance Projections:
├── Current throughput: ~100 users/second
├── With optimizations: ~1000 users/second
├── Memory usage: 50% reduction possible
└── Response time: 80% improvement expected

🔧 Specific Recommendations:
├── Line 35-36: Replace with sum(user['age'] for user in users)
├── Line 41-43: Combine counting with age calculation
├── Line 12: Use aiohttp for async requests
├── Line 8: Use dict for user storage with ID as key
└── Add caching layer for frequently requested users

⏱️  Performance analysis completed in 1.8s
```

#### Step 5: Apply Comprehensive Improvements

```bash
python cli.py enhance --file complex_example.py --comprehensive --goals security performance quality
```

**Expected Output**:

```
🔧 Comprehensive Enhancement: complex_example.py
==============================================

🚀 Enhancement Progress:
├── Security improvements... ✅ (8 applied)
├── Performance optimizations... ✅ (6 applied)
├── Code quality enhancements... ✅ (12 applied)
├── Documentation generation... ✅ (4 docstrings added)
└── Type annotations... ✅ (Complete coverage)

📊 Quality Improvement:
├── Overall Score: 4.2 → 8.9 (+4.7) 🎉
├── Security Score: 3.1 → 8.5 (+5.4) 🛡️
├── Performance Score: 5.5 → 9.1 (+3.6) ⚡
└── Maintainability: 4.0 → 8.7 (+4.7) 🔧

🔄 Major Changes Applied:
├── Added comprehensive input validation
├── Implemented async/await pattern
├── Added proper exception handling
├── Optimized algorithms and data structures
├── Added security headers and validation
├── Implemented logging and monitoring
├── Added complete type annotations
├── Generated comprehensive docstrings
├── Extracted configuration constants
└── Added unit test suggestions

💾 Enhanced code saved to complex_example.py
📋 Enhancement report saved to enhancement_report.md

⏱️  Enhancement completed in 12.3s
```

**View the transformation**:

```bash
# See the improved code
head -20 complex_example.py
```

**Sample Enhanced Code** (first 20 lines):

```python
import asyncio
import logging
from typing import List, Dict, Optional, Union, Any
import aiohttp
import json
from urllib.parse import urlparse

# Configuration constants
API_BASE_URL = "https://api.example.com"
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3

# Set up logging
logger = logging.getLogger(__name__)

class UserService:
    """Service for managing user data with async operations and comprehensive error handling."""
    
    def __init__(self, base_url: str = API_BASE_URL):
        """Initialize UserService with configurable base URL.
        
        Args:
            base_url: Base URL for the API endpoint
            
        Raises:
            ValueError: If base_url is invalid
        """
        if not self._validate_url(base_url):
            raise ValueError(f"Invalid base URL: {base_url}")
            
        self.base_url = base_url
        self.users: Dict[int, Dict[str, Any]] = {}  # Use dict for O(1) lookup
        self._session: Optional[aiohttp.ClientSession] = None
```

**Key Transformations**:

- ✅ Added async/await for non-blocking I/O
- ✅ Comprehensive type hints throughout
- ✅ Input validation and error handling
- ✅ Security improvements (URL validation, proper exceptions)
- ✅ Performance optimizations (dict instead of list, efficient algorithms)
- ✅ Proper logging and monitoring
- ✅ Configuration externalization
- ✅ Comprehensive docstrings

This tutorial demonstrates how the Agent Framework can transform low-quality code into production-ready, secure, and efficient code through comprehensive analysis and enhancement.
