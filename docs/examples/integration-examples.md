# Integration Examples

This guide demonstrates how to integrate the Agent Framework with various development tools, CI/CD pipelines, and external systems.

## Table of Contents

- [Integration Examples](#integration-examples)
  - [Table of Contents](#table-of-contents)
  - [CI/CD Pipeline Integration](#cicd-pipeline-integration)
    - [GitHub Actions Workflow](#github-actions-workflow)
    - [GitLab CI Integration](#gitlab-ci-integration)
  - [Pre-commit Hooks](#pre-commit-hooks)
    - [Advanced Pre-commit Configuration](#advanced-pre-commit-configuration)
    - [Pre-commit Scripts](#pre-commit-scripts)

## CI/CD Pipeline Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/ai-code-quality.yml
name: AI-Powered Code Quality Check

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main ]

jobs:
  ai-code-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for better analysis
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install Agent Framework
      run: |
        pip install -r requirements.txt
        pip install agent-framework
    
    - name: Configure Agent Framework
      env:
        AGENT_API_KEY: ${{ secrets.AGENT_API_KEY }}
        AGENT_MODEL: "gpt-4o"
      run: |
        echo "AGENT_API_KEY=${AGENT_API_KEY}" >> $GITHUB_ENV
        echo "AGENT_MODEL=${AGENT_MODEL}" >> $GITHUB_ENV
    
    - name: Get changed files
      id: changed-files
      uses: tj-actions/changed-files@v40
      with:
        files: |
          **/*.py
          **/*.js
          **/*.ts
    
    - name: AI Code Analysis
      if: steps.changed-files.outputs.any_changed == 'true'
      run: |
        mkdir -p reports
        
        # Analyze each changed file
        for file in ${{ steps.changed-files.outputs.all_changed_files }}; do
          echo "Analyzing $file..."
          
          python cli.py analyze \
            --file "$file" \
            --type comprehensive \
            --output-format json \
            --save-report "reports/$(basename $file)_analysis.json" \
            --fail-on-critical
          
          # Security analysis
          python cli.py analyze \
            --file "$file" \
            --type security \
            --detailed \
            --save-report "reports/$(basename $file)_security.json"
        done
    
    - name: Generate Quality Report
      if: steps.changed-files.outputs.any_changed == 'true'
      run: |
        python cli.py generate report \
          --type ci-analysis \
          --input-dir reports/ \
          --output-file quality-report.html \
          --format html \
          --include-metrics
    
    - name: Quality Gate Check
      if: steps.changed-files.outputs.any_changed == 'true'
      run: |
        python scripts/quality_gate.py \
          --reports-dir reports/ \
          --min-quality-score 7.0 \
          --max-complexity 15 \
          --security-threshold medium \
          --fail-on-violation
    
    - name: Upload Quality Report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: quality-report.html
    
    - name: Comment PR with Results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          // Read quality report summary
          let summary = "## 🤖 AI Code Quality Analysis\n\n";
          
          try {
            const reportFiles = fs.readdirSync('reports/');
            let totalScore = 0;
            let fileCount = 0;
            
            for (const file of reportFiles) {
              if (file.endsWith('_analysis.json')) {
                const data = JSON.parse(fs.readFileSync(`reports/${file}`, 'utf8'));
                totalScore += data.quality_score || 0;
                fileCount++;
              }
            }
            
            const avgScore = fileCount > 0 ? (totalScore / fileCount).toFixed(1) : 'N/A';
            
            summary += `📊 **Average Quality Score**: ${avgScore}/10\n`;
            summary += `📁 **Files Analyzed**: ${fileCount}\n\n`;
            
            if (avgScore >= 8.0) {
              summary += "✅ **Status**: Excellent code quality!\n";
            } else if (avgScore >= 7.0) {
              summary += "✅ **Status**: Good code quality with minor suggestions\n";
            } else if (avgScore >= 6.0) {
              summary += "⚠️ **Status**: Code quality needs improvement\n";
            } else {
              summary += "❌ **Status**: Significant code quality issues detected\n";
            }
            
            summary += "\n📋 **Full Report**: Check the uploaded artifacts for detailed analysis.";
            
          } catch (error) {
            summary += "❌ Error generating summary: " + error.message;
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });

  ai-enhancement:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: ai-code-analysis
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install agent-framework
    
    - name: AI Code Enhancement
      env:
        AGENT_API_KEY: ${{ secrets.AGENT_API_KEY }}
      run: |
        # Get changed Python files
        git diff --name-only origin/main...HEAD | grep '\.py$' > changed_files.txt
        
        # Enhance each file
        while IFS= read -r file; do
          if [ -f "$file" ]; then
            echo "Enhancing $file..."
            
            python cli.py enhance \
              --file "$file" \
              --goals quality maintainability \
              --enable-evaluation \
              --output-file "${file}.enhanced"
            
            # Only replace if enhancement was successful
            if [ $? -eq 0 ] && [ -f "${file}.enhanced" ]; then
              mv "${file}.enhanced" "$file"
              echo "✅ Enhanced $file"
            else
              echo "⚠️ Enhancement failed for $file, keeping original"
              rm -f "${file}.enhanced"
            fi
          fi
        done < changed_files.txt
    
    - name: Create Enhancement PR
      if: success()
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "🤖 AI-powered code enhancements"
        title: "🤖 AI Code Enhancements for PR #${{ github.event.number }}"
        body: |
          ## 🤖 Automated Code Enhancements
          
          This PR contains AI-powered enhancements to the code in PR #${{ github.event.number }}.
          
          ### Enhancements Applied:
          - Code quality improvements
          - Maintainability enhancements
          - Performance optimizations
          - Documentation improvements
          
          ### Review Notes:
          - All enhancements have been validated
          - Original functionality is preserved
          - Please review changes before merging
        branch: ai-enhancements-${{ github.event.number }}
        delete-branch: true
```

### GitLab CI Integration

```yaml
# .gitlab-ci.yml
stages:
  - analysis
  - enhancement
  - quality-gate

variables:
  AGENT_MODEL: "gpt-4o"
  PYTHON_VERSION: "3.11"

.agent_setup: &agent_setup
  before_script:
    - python -m pip install --upgrade pip
    - pip install -r requirements.txt
    - pip install agent-framework
    - export AGENT_API_KEY=$AGENT_API_KEY

ai_code_analysis:
  stage: analysis
  image: python:$PYTHON_VERSION
  <<: *agent_setup
  script:
    - mkdir -p reports
    - |
      # Get changed files
      git diff --name-only $CI_MERGE_REQUEST_TARGET_BRANCH_NAME...HEAD | grep -E '\.(py|js|ts)$' > changed_files.txt || true
      
      if [ -s changed_files.txt ]; then
        while IFS= read -r file; do
          if [ -f "$file" ]; then
            echo "Analyzing $file..."
            
            python cli.py analyze \
              --file "$file" \
              --type comprehensive \
              --output-format json \
              --save-report "reports/$(basename $file .py)_analysis.json"
            
            python cli.py analyze \
              --file "$file" \
              --type security \
              --save-report "reports/$(basename $file .py)_security.json"
          fi
        done < changed_files.txt
        
        # Generate consolidated report
        python cli.py generate report \
          --type gitlab-ci \
          --input-dir reports/ \
          --output-file quality-report.html \
          --format html
      else
        echo "No relevant files changed"
      fi
  artifacts:
    reports:
      junit: reports/junit.xml
    paths:
      - reports/
      - quality-report.html
    expire_in: 1 week
  only:
    - merge_requests

quality_gate:
  stage: quality-gate
  image: python:$PYTHON_VERSION
  <<: *agent_setup
  script:
    - |
      if [ -d "reports" ] && [ "$(ls -A reports/)" ]; then
        python scripts/quality_gate.py \
          --reports-dir reports/ \
          --min-quality-score 7.0 \
          --max-complexity 15 \
          --security-threshold medium \
          --output-format gitlab
      else
        echo "No reports found, skipping quality gate"
      fi
  dependencies:
    - ai_code_analysis
  only:
    - merge_requests

ai_enhancement:
  stage: enhancement
  image: python:$PYTHON_VERSION
  <<: *agent_setup
  script:
    - |
      git config --global user.email "<EMAIL>"
      git config --global user.name "AI Enhancement Bot"
      
      # Get changed files
      git diff --name-only $CI_MERGE_REQUEST_TARGET_BRANCH_NAME...HEAD | grep '\.py$' > changed_files.txt || true
      
      if [ -s changed_files.txt ]; then
        enhanced_files=""
        
        while IFS= read -r file; do
          if [ -f "$file" ]; then
            echo "Enhancing $file..."
            
            python cli.py enhance \
              --file "$file" \
              --goals quality performance \
              --enable-evaluation \
              --output-file "${file}.enhanced"
            
            if [ $? -eq 0 ] && [ -f "${file}.enhanced" ]; then
              mv "${file}.enhanced" "$file"
              enhanced_files="$enhanced_files $file"
            fi
          fi
        done < changed_files.txt
        
        if [ -n "$enhanced_files" ]; then
          git add $enhanced_files
          git commit -m "🤖 AI-powered code enhancements"
          git push origin HEAD:ai-enhancements-$CI_MERGE_REQUEST_IID
          
          # Create merge request for enhancements
          curl --request POST \
            --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
            --header "Content-Type: application/json" \
            --data '{
              "source_branch": "ai-enhancements-'$CI_MERGE_REQUEST_IID'",
              "target_branch": "'$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME'",
              "title": "🤖 AI Code Enhancements for MR !'$CI_MERGE_REQUEST_IID'",
              "description": "Automated code enhancements generated by AI analysis"
            }' \
            "$CI_API_V4_URL/projects/$CI_PROJECT_ID/merge_requests"
        fi
      fi
  dependencies:
    - ai_code_analysis
  only:
    - merge_requests
  when: manual
```

## Pre-commit Hooks

### Advanced Pre-commit Configuration

```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: ai-code-analysis
        name: AI Code Analysis
        entry: scripts/pre-commit-ai-analysis.sh
        language: script
        files: \.(py|js|ts)$
        stages: [commit]
        
      - id: ai-security-check
        name: AI Security Check
        entry: scripts/pre-commit-security.sh
        language: script
        files: \.(py|js|ts)$
        stages: [commit]
        
      - id: ai-complexity-check
        name: AI Complexity Check
        entry: scripts/pre-commit-complexity.sh
        language: script
        files: \.py$
        stages: [commit]
        
      - id: ai-enhancement-suggestion
        name: AI Enhancement Suggestions
        entry: scripts/pre-commit-suggestions.sh
        language: script
        files: \.(py|js|ts)$
        stages: [commit]
        always_run: false
        verbose: true
```

### Pre-commit Scripts

```bash
#!/bin/bash
# scripts/pre-commit-ai-analysis.sh

set -e

echo "🤖 Running AI code analysis..."

# Get staged files
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(py|js|ts)$' || true)

if [ -z "$staged_files" ]; then
    echo "No relevant files to analyze"
    exit 0
fi

# Create temporary directory for reports
temp_dir=$(mktemp -d)
trap "rm -rf $temp_dir" EXIT

failed_files=()
total_score=0
file_count=0

for file in $staged_files; do
    echo "Analyzing $file..."
    
    # Run analysis
    if python cli.py analyze \
        --file "$file" \
        --type quality \
        --output-format json \
        --save-report "$temp_dir/$(basename $file)_analysis.json" \
        --quiet; then
        
        # Extract score
        score=$(jq -r '.quality_score // 0' "$temp_dir/$(basename $file)_analysis.json")
        total_score=$(echo "$total_score + $score" | bc -l)
        file_count=$((file_count + 1))
        
        # Check if score is below threshold
        if (( $(echo "$score < 6.0" | bc -l) )); then
            echo "⚠️  $file: Quality score $score below threshold (6.0)"
            failed_files+=("$file")
        else
            echo "✅ $file: Quality score $score"
        fi
    else
        echo "❌ Failed to analyze $file"
        failed_files+=("$file")
    fi
done

# Calculate average score
if [ $file_count -gt 0 ]; then
    avg_score=$(echo "scale=1; $total_score / $file_count" | bc -l)
    echo "📊 Average quality score: $avg_score"
else
    avg_score=0
fi

# Check if any files failed
if [ ${#failed_files[@]} -gt 0 ]; then
    echo "❌ The following files have quality issues:"
    printf '  %s\n' "${failed_files[@]}"
    echo ""
    echo "💡 Run 'python cli.py enhance --file <filename> --goals quality' to improve code quality"
    echo "   Or use 'git commit --no-verify' to bypass this check"
    exit 1
fi

echo "✅ All files passed quality analysis!"
```

```bash
#!/bin/bash
# scripts/pre-commit-security.sh

set -e

echo "🔒 Running AI security analysis..."

staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(py|js|ts)$' || true)

if [ -z "$staged_files" ]; then
    echo "No relevant files to analyze"
    exit 0
fi

security_issues=()

for file in $staged_files; do
    echo "Security check: $file..."
    
    # Run security analysis
    if python cli.py analyze \
        --file "$file" \
        --type security \
        --output-format json \
        --save-report "/tmp/$(basename $file)_security.json" \
        --quiet; then
        
        # Check for critical vulnerabilities
        critical_count=$(jq -r '.critical_vulnerabilities // 0' "/tmp/$(basename $file)_security.json")
        high_count=$(jq -r '.high_vulnerabilities // 0' "/tmp/$(basename $file)_security.json")
        
        if [ "$critical_count" -gt 0 ] || [ "$high_count" -gt 0 ]; then
            echo "🚨 $file: $critical_count critical, $high_count high severity issues"
            security_issues+=("$file")
        else
            echo "✅ $file: No critical security issues"
        fi
        
        rm -f "/tmp/$(basename $file)_security.json"
    else
        echo "❌ Failed to analyze $file"
        security_issues+=("$file")
    fi
done

if [ ${#security_issues[@]} -gt 0 ]; then
    echo "🚨 Security issues found in the following files:"
    printf '  %s\n' "${security_issues[@]}"
    echo ""
    echo "💡 Run 'python cli.py analyze --file <filename> --type security --detailed' for more information"
    echo "   Or use 'git commit --no-verify' to bypass this check"
    exit 1
fi

echo "✅ No critical security issues found!"
```

```bash
#!/bin/bash
# scripts/pre-commit-complexity.sh

set -e

echo "🧮 Running AI complexity analysis..."

staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.py$' || true)

if [ -z "$staged_files" ]; then
    echo "No Python files to analyze"
    exit 0
fi

complex_files=()
max_complexity=15

for file in $staged_files; do
    echo "Complexity check: $file..."
    
    if python cli.py analyze \
        --file "$file" \
        --type complexity \
        --threshold $max_complexity \
        --output-format json \
        --save-report "/tmp/$(basename $file)_complexity.json" \
        --quiet; then
        
        # Check complexity
        max_file_complexity=$(jq -r '.max_complexity // 0' "/tmp/$(basename $file)_complexity.json")
        
        if [ "$max_file_complexity" -gt $max_complexity ]; then
            echo "⚠️  $file: Max complexity $max_file_complexity exceeds threshold ($max_complexity)"
            complex_files+=("$file")
        else
            echo "✅ $file: Max complexity $max_file_complexity"
        fi
        
        rm -f "/tmp/$(basename $file)_complexity.json"
    else
        echo "❌ Failed to analyze $file"
    fi
done

if [ ${#complex_files[@]} -gt 0 ]; then
    echo "⚠️  High complexity detected in the following files:"
    printf '  %s\n' "${complex_files[@]}"
    echo ""
    echo "💡 Consider refactoring complex functions to improve maintainability"
    echo "   Run 'python cli.py enhance --file <filename> --goals complexity' for suggestions"
    echo "   Or use 'git commit --no-verify' to bypass this check"
    
    # Don't fail the commit for complexity, just warn
    echo "⚠️  Proceeding with commit (complexity is a warning, not an error)"
fi

echo "✅ Complexity analysis complete!"
```
