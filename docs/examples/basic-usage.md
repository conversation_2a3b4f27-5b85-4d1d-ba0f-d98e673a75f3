# Basic Usage Examples

This comprehensive guide provides practical examples of using the Agent Framework for common programming tasks, from simple code analysis to complex multi-step workflows.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start Examples](#quick-start-examples)
3. [Code Analysis Examples](#code-analysis-examples)
4. [Code Generation Examples](#code-generation-examples)
5. [Code Optimization Examples](#code-optimization-examples)
6. [Debugging Examples](#debugging-examples)
7. [Documentation Generation Examples](#documentation-generation-examples)
8. [Enhancement Examples](#enhancement-examples)
9. [Interactive Mode Examples](#interactive-mode-examples)
10. [Batch Processing Examples](#batch-processing-examples)
11. [Integration Examples](#integration-examples)
12. [Common Patterns and Best Practices](#common-patterns-and-best-practices)

## Prerequisites

Before running these examples, make sure you have:

- Installed the Agent Framework ([Installation Guide](../getting-started/installation.md))
- Set up your API key ([Configuration Guide](../user-guides/configuration.md))
- Basic familiarity with the CLI ([CLI Reference](../user-guides/cli-reference.md))

## Quick Start Examples

### Your First Command

```bash
# Simple code analysis - perfect for getting started
python cli.py analyze --code "def hello(): print('Hello, World!')" --type basic
```

**Expected Output:**

```
✅ Code Analysis Complete
📊 Quality Score: 7.5/10
📝 Suggestions:
  • Add type hints for better code clarity
  • Consider adding a docstring
  • Function could return a value instead of printing
```

### Environment Setup Verification

```bash
# Verify your setup is working correctly
python cli.py --version
python cli.py status
```

## Example Workflow

```mermaid
graph LR
    A[Write Code] --> B[Analyze Quality]
    B --> C{Issues Found?}
    C -->|Yes| D[Fix Issues]
    C -->|No| E[Generate Tests]
    D --> B
    E --> F[Generate Docs]
    F --> G[Optimize Performance]
    G --> H[Final Review]

    subgraph "Agent Framework Commands"
        B1[agent-framework analyze]
        D1[agent-framework debug --auto-fix]
        E1[agent-framework generate tests]
        F1[agent-framework document]
        G1[agent-framework optimize]
    end

    B -.-> B1
    D -.-> D1
    E -.-> E1
    F -.-> F1
    G -.-> G1

    style A fill:#e1f5fe
    style H fill:#e8f5e8
    style C fill:#fff3e0
```

## Code Analysis Examples

### Basic Code Quality Analysis

```bash
# Analyze a Python file for quality issues
python cli.py analyze --file calculator.py --type quality --detailed
```

**Sample Output:**

```text
Quality Analysis Results:
├── Overall Quality Score: 8.1/10
├── Code Smells Found: 2
├── Documentation Coverage: 75%
└── Type Annotation Coverage: 90%

Issues Found:
├── Long Method: calculate_complex() (35 lines)
└── Missing Docstring: helper_function()

Recommendations:
├── Break down calculate_complex() into smaller functions
└── Add docstring to helper_function()
```

### Analyzing Code from String Input

```bash
# Analyze code directly from command line
python cli.py analyze --code "
def calculate_sum(numbers):
    total = 0
    for i in range(len(numbers)):
        total = total + numbers[i]
    return total
" --type all
```

**Sample Output:**

```text
📊 Analysis Results:
├── Quality Score: 6.5/10
├── Complexity: Low (2)
├── Performance Issues: 1 found

🔍 Issues Detected:
├── Inefficient loop pattern (use sum() or enumerate)
├── Missing type hints
└── No input validation

💡 Suggestions:
├── Use built-in sum() function: return sum(numbers)
├── Add type hints: def calculate_sum(numbers: List[float]) -> float
└── Add input validation for empty lists
```

### Advanced Analysis with Multiple Types

```bash
# Comprehensive analysis covering all aspects
python cli.py analyze --file complex_module.py \
  --type complexity,security,performance,maintainability \
  --output-format json \
  --save-report analysis_report.json
```

### Check Code Complexity

```bash
# Check if code complexity is within acceptable limits
python cli.py analyze --file complex_algorithm.py --type complexity --threshold 10
```

**Sample Output:**

```text
Complexity Analysis Results:
├── Cyclomatic Complexity: 12 (High - exceeds threshold)
├── Cognitive Complexity: 15 (High)
└── Functions over threshold: 1

High Complexity Functions:
└── process_data() - Complexity: 12

Suggestions:
└── Consider refactoring process_data() to reduce complexity
```

### Security Analysis

```bash
# Analyze code for security vulnerabilities
python cli.py analyze --file web_app.py --type security --detailed
```

**Sample Output:**

```text
🔒 Security Analysis Results:
├── Security Score: 7.2/10
├── Vulnerabilities Found: 3
├── Risk Level: Medium

🚨 Security Issues:
├── SQL Injection Risk: Line 45 (user_query concatenation)
├── XSS Vulnerability: Line 78 (unescaped user input)
└── Hardcoded Secret: Line 12 (API key in source)

🛡️ Recommendations:
├── Use parameterized queries for database operations
├── Sanitize and escape user input before rendering
└── Move secrets to environment variables
```

### Analyze Code Patterns and Architecture

```bash
# Detect design patterns and anti-patterns
python cli.py analyze --file design_patterns.py --type patterns --architectural
```

**Sample Output:**

```text
🏗️ Pattern Analysis Results:
├── Design Patterns Found: 3
├── Anti-patterns Detected: 2
├── Architecture Score: 8.5/10

✅ Detected Patterns:
├── Singleton Pattern: DatabaseConnection class
├── Factory Pattern: ShapeFactory class
└── Observer Pattern: EventManager class

⚠️ Anti-patterns Found:
├── God Object: MainController (too many responsibilities)
└── Magic Numbers: Multiple hardcoded values

💡 Architectural Suggestions:
├── Split MainController into smaller, focused classes
├── Extract constants to configuration file
└── Consider implementing Dependency Injection
```

## Code Generation Examples

### Generate a Simple Function

```bash
# Generate a function with type hints and docstring
python cli.py generate function \
  --name calculate_average \
  --description "Calculate the average of a list of numbers" \
  --parameters "numbers:List[float]" "exclude_zeros:bool:False" \
  --return-type "float"
```

**Generated Code:**

```python
from typing import List

def calculate_average(numbers: List[float], exclude_zeros: bool = False) -> float:
    """Calculate the average of a list of numbers.

    Args:
        numbers: List of numbers to calculate average for
        exclude_zeros: Whether to exclude zero values from calculation

    Returns:
        The average of the numbers

    Raises:
        ValueError: If the list is empty or contains no valid numbers
    """
    if exclude_zeros:
        numbers = [n for n in numbers if n != 0]

    if not numbers:
        raise ValueError("Cannot calculate average of empty list")

    return sum(numbers) / len(numbers)
```

### Generate an Async Function

```bash
# Generate an asynchronous function with error handling
python cli.py generate function \
  --name fetch_user_data \
  --description "Fetch user data from API with retry logic" \
  --parameters "user_id:str" "max_retries:int:3" \
  --return-type "Dict[str, Any]" \
  --async \
  --include-error-handling
```

**Generated Code:**

```python
import asyncio
import aiohttp
from typing import Dict, Any

async def fetch_user_data(user_id: str, max_retries: int = 3) -> Dict[str, Any]:
    """Fetch user data from API with retry logic.

    Args:
        user_id: Unique identifier for the user
        max_retries: Maximum number of retry attempts

    Returns:
        Dictionary containing user data

    Raises:
        ValueError: If user_id is invalid
        aiohttp.ClientError: If API request fails after all retries
    """
    if not user_id or not user_id.strip():
        raise ValueError("user_id cannot be empty")

    url = f"https://api.example.com/users/{user_id}"

    for attempt in range(max_retries + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response.raise_for_status()
                    return await response.json()
        except aiohttp.ClientError as e:
            if attempt == max_retries:
                raise
            await asyncio.sleep(2 ** attempt)  # Exponential backoff

    # This should never be reached, but included for completeness
    raise aiohttp.ClientError("Failed to fetch user data after all retries")
```

### Generate a Class with Full Features

```bash
# Generate a class with methods and attributes
python cli.py generate class \
  --name UserManager \
  --description "Manage user accounts and authentication" \
  --attributes "users:Dict[str, User]" "active_sessions:Set[str]" \
  --methods "add_user:Add a new user to the system" "authenticate:Authenticate user credentials" \
  --include-validation \
  --include-logging
```

**Generated Code:**

```python
from typing import Dict, Set, Optional
from dataclasses import dataclass
import hashlib
import logging
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class User:
    username: str
    email: str
    password_hash: str
    created_at: datetime
    last_login: Optional[datetime] = None

class UserManager:
    """Manage user accounts and authentication with validation and logging."""

    def __init__(self):
        self.users: Dict[str, User] = {}
        self.active_sessions: Set[str] = set()
        logger.info("UserManager initialized")

    def add_user(self, username: str, email: str, password: str) -> bool:
        """Add a new user to the system.

        Args:
            username: Unique username for the user (3-50 characters)
            email: User's email address (must be valid format)
            password: Plain text password (minimum 8 characters)

        Returns:
            True if user was added successfully, False if username exists

        Raises:
            ValueError: If input validation fails
        """
        # Input validation
        if not username or len(username) < 3 or len(username) > 50:
            raise ValueError("Username must be 3-50 characters long")

        if not email or '@' not in email:
            raise ValueError("Invalid email format")

        if not password or len(password) < 8:
            raise ValueError("Password must be at least 8 characters long")

        if username in self.users:
            logger.warning(f"Attempt to add existing user: {username}")
            return False

        # Hash password securely
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        # Create user
        user = User(
            username=username,
            email=email,
            password_hash=password_hash,
            created_at=datetime.now()
        )

        self.users[username] = user
        logger.info(f"User added successfully: {username}")
        return True

    def authenticate(self, username: str, password: str) -> Optional[str]:
        """Authenticate user credentials and create session.

        Args:
            username: Username to authenticate
            password: Password to verify

        Returns:
            Session ID if authentication successful, None otherwise
        """
        if not username or not password:
            logger.warning("Authentication attempt with empty credentials")
            return None

        if username not in self.users:
            logger.warning(f"Authentication attempt for non-existent user: {username}")
            return None

        user = self.users[username]
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        if user.password_hash == password_hash:
            # Create session
            session_id = str(uuid.uuid4())
            self.active_sessions.add(session_id)

            # Update last login
            user.last_login = datetime.now()

            logger.info(f"User authenticated successfully: {username}")
            return session_id
        else:
            logger.warning(f"Failed authentication attempt for user: {username}")
            return None

    def logout(self, session_id: str) -> bool:
        """Logout user by removing session.

        Args:
            session_id: Session ID to remove

        Returns:
            True if session was removed, False if session didn't exist
        """
        if session_id in self.active_sessions:
            self.active_sessions.remove(session_id)
            logger.info(f"User logged out, session removed: {session_id}")
            return True
        return False
```

### Generate Comprehensive Test Suite

```bash
# Generate test cases for existing code
python cli.py generate tests \
  --file calculator.py \
  --framework pytest \
  --coverage \
  --include-edge-cases \
  --include-performance-tests
```

**Generated Tests:**

```python
import pytest
import time
from unittest.mock import patch, MagicMock
from calculator import Calculator

class TestCalculator:
    """Comprehensive test cases for Calculator class."""

    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.calc = Calculator()

    def teardown_method(self):
        """Clean up after each test method."""
        # Reset any global state if needed
        pass

    # Basic functionality tests
    def test_add_positive_numbers(self):
        """Test addition of positive numbers."""
        result = self.calc.add(2, 3)
        assert result == 5

    def test_add_negative_numbers(self):
        """Test addition of negative numbers."""
        result = self.calc.add(-2, -3)
        assert result == -5

    def test_add_mixed_numbers(self):
        """Test addition of positive and negative numbers."""
        result = self.calc.add(5, -3)
        assert result == 2

    def test_add_zero(self):
        """Test addition with zero."""
        assert self.calc.add(5, 0) == 5
        assert self.calc.add(0, 5) == 5
        assert self.calc.add(0, 0) == 0

    # Edge cases
    def test_add_large_numbers(self):
        """Test addition with very large numbers."""
        large_num = 10**15
        result = self.calc.add(large_num, large_num)
        assert result == 2 * large_num

    def test_add_floating_point_precision(self):
        """Test floating point precision in addition."""
        result = self.calc.add(0.1, 0.2)
        assert abs(result - 0.3) < 1e-10

    # Division tests
    def test_divide_positive_numbers(self):
        """Test division of positive numbers."""
        result = self.calc.divide(10, 2)
        assert result == 5

    def test_divide_by_zero(self):
        """Test division by zero raises appropriate exception."""
        with pytest.raises(ZeroDivisionError, match="Cannot divide by zero"):
            self.calc.divide(10, 0)

    def test_divide_zero_by_number(self):
        """Test dividing zero by a number."""
        result = self.calc.divide(0, 5)
        assert result == 0

    # Parametrized tests
    @pytest.mark.parametrize("a,b,expected", [
        (10, 2, 5),
        (15, 3, 5),
        (7, 2, 3.5),
        (-10, 2, -5),
        (10, -2, -5),
        (-10, -2, 5),
    ])
    def test_divide_parametrized(self, a, b, expected):
        """Test division with multiple parameter sets."""
        result = self.calc.divide(a, b)
        assert result == expected

    # Performance tests
    def test_add_performance(self):
        """Test addition performance with large datasets."""
        start_time = time.time()

        for i in range(10000):
            self.calc.add(i, i + 1)

        end_time = time.time()
        execution_time = end_time - start_time

        # Should complete within reasonable time (adjust threshold as needed)
        assert execution_time < 1.0, f"Addition took too long: {execution_time}s"

    # Mock tests (if calculator has external dependencies)
    @patch('calculator.external_api_call')
    def test_complex_calculation_with_mock(self, mock_api):
        """Test complex calculation that depends on external API."""
        mock_api.return_value = 42

        result = self.calc.complex_calculation(10)

        mock_api.assert_called_once_with(10)
        assert result == 52  # 10 + 42

    # Property-based testing (requires hypothesis)
    @pytest.mark.skipif(not pytest.importorskip("hypothesis"),
                       reason="hypothesis not available")
    def test_add_commutative_property(self):
        """Test that addition is commutative using property-based testing."""
        from hypothesis import given, strategies as st

        @given(st.floats(allow_nan=False, allow_infinity=False),
               st.floats(allow_nan=False, allow_infinity=False))
        def test_commutative(a, b):
            assert self.calc.add(a, b) == self.calc.add(b, a)

        test_commutative()

    # Integration tests
    def test_calculator_workflow(self):
        """Test a complete calculator workflow."""
        # Start with initial value
        result = self.calc.add(10, 5)  # 15

        # Perform multiple operations
        result = self.calc.multiply(result, 2)  # 30
        result = self.calc.divide(result, 3)    # 10
        result = self.calc.subtract(result, 5)  # 5

        assert result == 5

    # Error handling tests
    def test_invalid_input_types(self):
        """Test calculator behavior with invalid input types."""
        with pytest.raises(TypeError):
            self.calc.add("string", 5)

        with pytest.raises(TypeError):
            self.calc.add(5, None)
```

## Code Optimization Examples

### Performance Optimization

```bash
# Optimize code for better performance
agent-framework optimize --file slow_algorithm.py --type performance --show-diff
```

**Before:**

```python
def find_duplicates(items):
    duplicates = []
    for i in range(len(items)):
        for j in range(i + 1, len(items)):
            if items[i] == items[j] and items[i] not in duplicates:
                duplicates.append(items[i])
    return duplicates
```

**After:**

```python
def find_duplicates(items):
    seen = set()
    duplicates = set()
    for item in items:
        if item in seen:
            duplicates.add(item)
        else:
            seen.add(item)
    return list(duplicates)
```

### Memory Optimization

```bash
# Optimize memory usage
agent-framework optimize --file memory_intensive.py --type memory
```

**Optimization Suggestions:**

```
Memory Optimization Results:
├── Large list comprehensions found: 2
├── Unnecessary object creation: 3 instances
└── Memory leaks potential: 1

Recommendations:
├── Use generators instead of list comprehensions for large datasets
├── Reuse objects where possible
└── Explicitly close file handles and database connections
```

## Debugging Examples

### Analyze Error Traceback

```bash
# Analyze an error traceback for debugging insights
agent-framework debug --traceback error.txt --suggest-fixes
```

**Sample error.txt:**

```
Traceback (most recent call last):
  File "main.py", line 15, in <module>
    result = process_data(data)
  File "main.py", line 8, in process_data
    return data[index]
IndexError: list index out of range
```

**Analysis Output:**

```
Error Analysis Results:
├── Error Type: IndexError
├── Root Cause: Array index out of bounds
├── Location: main.py, line 8, in process_data()
└── Confidence: High (95%)

Suggested Fixes:
├── Add bounds checking before accessing array elements
├── Validate input data length before processing
└── Use try-catch block for graceful error handling

Code Suggestions:
```python
def process_data(data):
    if not data or index >= len(data):
        raise ValueError("Invalid data or index")
    return data[index]
```

### Automatic Bug Fixing

```bash
# Attempt to automatically fix detected issues
agent-framework debug --auto-fix --file buggy_code.py --max-iterations 3
```

**Process Output:**

```
Automatic Bug Fixing Session:
├── Iteration 1: Fixed syntax error on line 12
├── Iteration 2: Added missing import statement
├── Iteration 3: Fixed variable scope issue
└── Result: All issues resolved successfully

Fixed Issues:
├── Missing colon in function definition
├── Undefined variable 'json' (added import)
└── Variable 'result' used before assignment
```

## Documentation Generation Examples

### Generate Docstrings

```bash
# Add docstrings to functions and classes
agent-framework document docstrings \
  --file undocumented.py \
  --style google \
  --include-examples
```

**Before:**

```python
def calculate_tax(income, rate, deductions):
    taxable_income = income - deductions
    return taxable_income * rate
```

**After:**

```python
def calculate_tax(income: float, rate: float, deductions: float) -> float:
    """Calculate tax based on income, rate, and deductions.
    
    Args:
        income: Total income amount
        rate: Tax rate as a decimal (e.g., 0.25 for 25%)
        deductions: Total deductible amount
        
    Returns:
        The calculated tax amount
        
    Example:
        >>> calculate_tax(50000, 0.25, 5000)
        11250.0
    """
    taxable_income = income - deductions
    return taxable_income * rate
```

### Generate API Documentation

```bash
# Generate API documentation for a module
agent-framework document api \
  --file mymodule.py \
  --format markdown \
  --with-toc
```

## Enhancement Examples

### Comprehensive Code Enhancement

```bash
# Enhance code with multiple improvement goals
agent-framework enhance \
  --file legacy_code.py \
  --goals quality performance maintainability \
  --comprehensive
```

**Enhancement Process:**

```
Enhancement Session Results:
├── Quality improvements: 8 applied
├── Performance optimizations: 3 applied
├── Maintainability enhancements: 5 applied
└── Overall quality score: 6.2 → 8.7

Applied Changes:
├── Added type hints to all functions
├── Extracted magic numbers to constants
├── Improved variable naming
├── Optimized loop performance
├── Added comprehensive docstrings
└── Refactored complex functions
```

## Interactive Mode Examples

### Natural Language Queries

```bash
# Start interactive mode
agent-framework interactive
```

**Interactive Session:**

```
agent> How can I optimize this function for better performance?
def slow_function(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result

🤖 I can help optimize this function! Here are several improvements:

1. Use list comprehension for better performance:
   result = [item * 2 for item in data if item > 0]

2. For very large datasets, consider using a generator:
   return (item * 2 for item in data if item > 0)

3. If you need to process this frequently, consider using NumPy:
   import numpy as np
   data_array = np.array(data)
   return (data_array[data_array > 0] * 2).tolist()

Would you like me to analyze the performance difference between these approaches?

agent> generate a test for this function

🤖 Here's a comprehensive test for your function:

```python
import pytest

def test_slow_function():
    # Test with positive numbers
    assert slow_function([1, 2, 3]) == [2, 4, 6]
    
    # Test with mixed numbers
    assert slow_function([-1, 0, 1, 2]) == [2, 4]
    
    # Test with empty list
    assert slow_function([]) == []
    
    # Test with all negative numbers
    assert slow_function([-1, -2, -3]) == []
```

agent> exit

```

## Batch Processing Examples

### Process Multiple Files

```bash
# Analyze all Python files in a directory
find src/ -name "*.py" -exec agent-framework analyze --file {} --type quality \;

# Generate tests for all modules
for file in src/*.py; do
    agent-framework generate tests --file "$file" --framework pytest
done

# Enhance all files in a project
agent-framework enhance --file src/ --goals quality --comprehensive
```

## Integration Examples

### CI/CD Integration

```bash
#!/bin/bash
# ci-quality-check.sh

# Run quality analysis
agent-framework analyze --file src/ --type all --format json > quality_report.json

# Check if quality meets threshold
quality_score=$(jq '.overall_quality_score' quality_report.json)
if (( $(echo "$quality_score < 7.0" | bc -l) )); then
    echo "Quality score $quality_score below threshold 7.0"
    exit 1
fi

echo "Quality check passed with score $quality_score"
```

### Pre-commit Hook

```bash
#!/bin/bash
# .git/hooks/pre-commit

# Analyze staged Python files
for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.py$'); do
    echo "Analyzing $file..."
    agent-framework analyze --file "$file" --type complexity --threshold 15
    if [ $? -ne 0 ]; then
        echo "Complexity check failed for $file"
        exit 1
    fi
done

echo "All files passed complexity check"
```

## Next Steps

After trying these basic examples:

1. Explore [Advanced Scenarios](advanced-scenarios.md) for complex use cases
2. Learn about [Multi-Agent Setup](../user-guides/multi-agent-setup.md) for coordinated workflows
3. Check [Enhanced Capabilities](../features/enhanced-capabilities.md) for advanced features
4. Review [Configuration Guide](../user-guides/configuration.md) for customization options

## See Also

- [CLI Reference](../user-guides/cli-reference.md) - Complete command documentation
- [Quick Start Guide](../getting-started/quick-start.md) - Getting started tutorial
- [Troubleshooting](../troubleshooting/common-issues.md) - Common issues and solutions
