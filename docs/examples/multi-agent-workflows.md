# Multi-Agent Workflow Examples

This guide demonstrates advanced multi-agent coordination patterns and collaborative workflows using the Agent Framework's multi-agent capabilities.

## Table of Contents

- [Multi-Agent Workflow Examples](#multi-agent-workflow-examples)
  - [Table of Contents](#table-of-contents)
  - [Basic Multi-Agent Setup](#basic-multi-agent-setup)
    - [Simple Multi-Agent Configuration](#simple-multi-agent-configuration)
  - [Collaborative Code Review](#collaborative-code-review)
    - [Multi-Agent Code Review Workflow](#multi-agent-code-review-workflow)

## Basic Multi-Agent Setup

### Simple Multi-Agent Configuration

```python
#!/usr/bin/env python3
"""
Basic multi-agent setup with specialized roles.
"""

import asyncio
from agent_framework.core.config import FrameworkConfig, MultiAgentConfig, AgentRoleConfig, ModelConfig, ModelProvider

async def setup_basic_multi_agent_system():
    """Set up a basic multi-agent system with different specialized roles."""
    
    # Configure different models for different agents
    analyst_config = ModelConfig(
        provider=ModelProvider.OPENAI,
        model="gpt-4o",
        temperature=0.1,  # Low temperature for analytical tasks
        max_tokens=4096
    )
    
    creative_config = ModelConfig(
        provider=ModelProvider.ANTHROPIC,
        model="claude-3-5-sonnet-20241022",
        temperature=0.8,  # Higher temperature for creative tasks
        max_tokens=4096
    )
    
    efficient_config = ModelConfig(
        provider=ModelProvider.OPENROUTER,
        model="qwen/qwen3-coder:free",
        temperature=0.3,  # Balanced for general tasks
        max_tokens=2048
    )
    
    # Define agent roles with specific capabilities
    agent_roles = {
        "code_analyst": AgentRoleConfig(
            name="code_analyst",
            capabilities=["code_analysis", "complexity_analysis", "security_analysis"],
            model_config=analyst_config,
            system_message="""You are a senior code analyst specializing in code quality, 
            security, and performance analysis. You provide detailed, technical assessments 
            with specific recommendations for improvement.""",
            max_concurrent_tasks=3
        ),
        
        "code_generator": AgentRoleConfig(
            name="code_generator",
            capabilities=["code_generation", "refactoring", "optimization"],
            model_config=creative_config,
            system_message="""You are an expert code generator who creates clean, 
            efficient, and well-documented code. You follow best practices and 
            modern programming patterns.""",
            max_concurrent_tasks=2
        ),
        
        "tester": AgentRoleConfig(
            name="tester",
            capabilities=["test_generation", "test_analysis", "quality_assurance"],
            model_config=efficient_config,
            system_message="""You are a testing specialist who creates comprehensive 
            test suites, identifies edge cases, and ensures code reliability.""",
            max_concurrent_tasks=4
        ),
        
        "documenter": AgentRoleConfig(
            name="documenter",
            capabilities=["documentation", "api_docs", "user_guides"],
            model_config=creative_config,
            system_message="""You are a technical writer who creates clear, 
            comprehensive documentation that helps developers understand and use code effectively.""",
            max_concurrent_tasks=2
        )
    }
    
    # Configure multi-agent system
    multi_agent_config = MultiAgentConfig(
        enabled=True,
        max_agents=8,
        coordination_strategy="capability_based",
        load_balancing_strategy="round_robin",
        agent_roles=agent_roles,
        communication_timeout=30.0,
        task_distribution_algorithm="priority_based"
    )
    
    # Create framework configuration
    config = FrameworkConfig(
        multi_agent=multi_agent_config,
        debug=True
    )
    
    # Initialize orchestrator
    from agent_framework.core.agent_orchestrator import AgentOrchestrator
    orchestrator = AgentOrchestrator(config)
    await orchestrator.initialize()
    
    print("✅ Multi-agent system initialized successfully!")
    print(f"📊 System Status:")
    
    status = await orchestrator.get_multi_agent_status()
    print(f"  - Total Agents: {status['registry_stats']['total_agents']}")
    print(f"  - Active Agents: {status['registry_stats']['active_agents']}")
    print(f"  - Available Capabilities: {len(status['available_capabilities'])}")
    
    return orchestrator

# Example usage
async def main():
    orchestrator = await setup_basic_multi_agent_system()
    
    # Example task that will be distributed among agents
    from agent_framework.core.types import Task, TaskPriority
    
    complex_task = Task(
        name="comprehensive_code_review",
        description="Perform complete code analysis, generate tests, and create documentation",
        task_type="workflow",
        priority=TaskPriority.HIGH,
        parameters={
            "file_path": "src/user_service.py",
            "include_security_analysis": True,
            "generate_tests": True,
            "create_documentation": True,
            "optimize_performance": True
        }
    )
    
    print("\n🚀 Executing complex multi-agent task...")
    result = await orchestrator.execute_multi_agent_task(complex_task)
    
    print(f"✅ Task completed successfully: {result.success}")
    print(f"📊 Execution time: {result.execution_time:.2f}s")
    
    await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
```

## Collaborative Code Review

### Multi-Agent Code Review Workflow

```python
#!/usr/bin/env python3
"""
Advanced multi-agent code review system with specialized review agents.
"""

import asyncio
from typing import Dict, List, Any
from dataclasses import dataclass
from agent_framework.core.types import Task, TaskPriority

@dataclass
class ReviewResult:
    agent_name: str
    review_type: str
    score: float
    issues: List[str]
    suggestions: List[str]
    approval: bool

class MultiAgentCodeReviewer:
    """Orchestrates multiple specialized agents for comprehensive code review."""
    
    def __init__(self, orchestrator):
        self.orchestrator = orchestrator
        self.review_agents = {
            'security_reviewer': {
                'focus': 'security vulnerabilities and best practices',
                'weight': 0.3
            },
            'performance_reviewer': {
                'focus': 'performance optimization and efficiency',
                'weight': 0.25
            },
            'quality_reviewer': {
                'focus': 'code quality and maintainability',
                'weight': 0.25
            },
            'style_reviewer': {
                'focus': 'coding standards and style consistency',
                'weight': 0.2
            }
        }
    
    async def comprehensive_review(self, file_path: str, code_content: str) -> Dict[str, Any]:
        """Perform comprehensive code review using multiple specialized agents."""
        
        print(f"🔍 Starting multi-agent review of {file_path}")
        
        # Create review tasks for each specialized agent
        review_tasks = []
        for agent_type, config in self.review_agents.items():
            task = Task(
                name=f"{agent_type}_review",
                description=f"Review {file_path} focusing on {config['focus']}",
                task_type="code_review",
                priority=TaskPriority.HIGH,
                parameters={
                    "file_path": file_path,
                    "code_content": code_content,
                    "review_focus": config['focus'],
                    "agent_type": agent_type,
                    "detailed_analysis": True
                }
            )
            review_tasks.append(task)
        
        # Execute all review tasks in parallel
        print("⚡ Running parallel reviews...")
        review_results = await asyncio.gather(*[
            self.orchestrator.execute_task(task) for task in review_tasks
        ])
        
        # Process and aggregate results
        aggregated_results = await self._aggregate_review_results(review_results)
        
        # Generate consensus recommendations
        consensus = await self._generate_consensus(aggregated_results)
        
        return {
            'file_path': file_path,
            'individual_reviews': aggregated_results,
            'consensus': consensus,
            'overall_score': consensus['weighted_score'],
            'recommendation': consensus['final_recommendation']
        }
    
    async def _aggregate_review_results(self, review_results: List[Any]) -> List[ReviewResult]:
        """Aggregate and structure review results from multiple agents."""
        
        aggregated = []
        
        for i, result in enumerate(review_results):
            if result.success:
                agent_name = list(self.review_agents.keys())[i]
                review_data = result.result
                
                review_result = ReviewResult(
                    agent_name=agent_name,
                    review_type=self.review_agents[agent_name]['focus'],
                    score=review_data.get('score', 0.0),
                    issues=review_data.get('issues', []),
                    suggestions=review_data.get('suggestions', []),
                    approval=review_data.get('score', 0.0) >= 7.0
                )
                
                aggregated.append(review_result)
                print(f"  ✅ {agent_name}: {review_result.score:.1f}/10")
            else:
                print(f"  ❌ Review failed for agent {i}")
        
        return aggregated
    
    async def _generate_consensus(self, review_results: List[ReviewResult]) -> Dict[str, Any]:
        """Generate consensus from multiple agent reviews."""
        
        # Calculate weighted score
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for review in review_results:
            weight = self.review_agents[review.agent_name]['weight']
            total_weighted_score += review.score * weight
            total_weight += weight
        
        weighted_score = total_weighted_score / total_weight if total_weight > 0 else 0.0
        
        # Aggregate issues and suggestions
        all_issues = []
        all_suggestions = []
        
        for review in review_results:
            all_issues.extend(review.issues)
            all_suggestions.extend(review.suggestions)
        
        # Remove duplicates while preserving order
        unique_issues = list(dict.fromkeys(all_issues))
        unique_suggestions = list(dict.fromkeys(all_suggestions))
        
        # Determine final recommendation
        approval_count = sum(1 for review in review_results if review.approval)
        total_reviews = len(review_results)
        
        if approval_count == total_reviews:
            final_recommendation = "APPROVED"
        elif approval_count >= total_reviews * 0.75:
            final_recommendation = "APPROVED_WITH_SUGGESTIONS"
        elif approval_count >= total_reviews * 0.5:
            final_recommendation = "CHANGES_REQUESTED"
        else:
            final_recommendation = "REJECTED"
        
        return {
            'weighted_score': weighted_score,
            'approval_rate': approval_count / total_reviews,
            'consolidated_issues': unique_issues,
            'consolidated_suggestions': unique_suggestions,
            'final_recommendation': final_recommendation,
            'reviewer_consensus': approval_count >= total_reviews * 0.75
        }
    
    async def review_pull_request(self, pr_files: List[str]) -> Dict[str, Any]:
        """Review an entire pull request with multiple files."""
        
        print(f"📋 Reviewing pull request with {len(pr_files)} files")
        
        file_reviews = {}
        overall_scores = []
        
        # Review each file
        for file_path in pr_files:
            try:
                with open(file_path, 'r') as f:
                    code_content = f.read()
                
                file_review = await self.comprehensive_review(file_path, code_content)
                file_reviews[file_path] = file_review
                overall_scores.append(file_review['overall_score'])
                
            except Exception as e:
                print(f"❌ Failed to review {file_path}: {e}")
                file_reviews[file_path] = {'error': str(e)}
        
        # Calculate PR-level metrics
        avg_score = sum(overall_scores) / len(overall_scores) if overall_scores else 0.0
        
        # Determine PR recommendation
        if avg_score >= 8.0:
            pr_recommendation = "APPROVED"
        elif avg_score >= 7.0:
            pr_recommendation = "APPROVED_WITH_SUGGESTIONS"
        elif avg_score >= 6.0:
            pr_recommendation = "CHANGES_REQUESTED"
        else:
            pr_recommendation = "REJECTED"
        
        return {
            'pr_summary': {
                'total_files': len(pr_files),
                'reviewed_files': len([r for r in file_reviews.values() if 'error' not in r]),
                'average_score': avg_score,
                'recommendation': pr_recommendation
            },
            'file_reviews': file_reviews,
            'consolidated_feedback': await self._consolidate_pr_feedback(file_reviews)
        }
    
    async def _consolidate_pr_feedback(self, file_reviews: Dict[str, Any]) -> Dict[str, Any]:
        """Consolidate feedback across all files in the PR."""
        
        all_issues = []
        all_suggestions = []
        pattern_issues = {}
        
        for file_path, review in file_reviews.items():
            if 'error' in review:
                continue
            
            consensus = review.get('consensus', {})
            issues = consensus.get('consolidated_issues', [])
            suggestions = consensus.get('consolidated_suggestions', [])
            
            # Track issues by pattern
            for issue in issues:
                issue_type = self._categorize_issue(issue)
                if issue_type not in pattern_issues:
                    pattern_issues[issue_type] = []
                pattern_issues[issue_type].append(file_path)
            
            all_issues.extend(issues)
            all_suggestions.extend(suggestions)
        
        # Identify cross-file patterns
        cross_file_patterns = {
            pattern: files for pattern, files in pattern_issues.items()
            if len(files) > 1
        }
        
        return {
            'total_issues': len(all_issues),
            'total_suggestions': len(all_suggestions),
            'cross_file_patterns': cross_file_patterns,
            'priority_issues': self._prioritize_issues(all_issues),
            'actionable_suggestions': self._prioritize_suggestions(all_suggestions)
        }
    
    def _categorize_issue(self, issue: str) -> str:
        """Categorize an issue for pattern detection."""
        issue_lower = issue.lower()
        
        if 'security' in issue_lower:
            return 'security'
        elif 'performance' in issue_lower:
            return 'performance'
        elif 'type' in issue_lower or 'annotation' in issue_lower:
            return 'typing'
        elif 'docstring' in issue_lower or 'documentation' in issue_lower:
            return 'documentation'
        elif 'complexity' in issue_lower:
            return 'complexity'
        else:
            return 'general'
    
    def _prioritize_issues(self, issues: List[str]) -> List[str]:
        """Prioritize issues by severity and impact."""
        
        priority_keywords = {
            'critical': ['security vulnerability', 'sql injection', 'xss'],
            'high': ['performance bottleneck', 'memory leak', 'deadlock'],
            'medium': ['code smell', 'complexity', 'maintainability'],
            'low': ['style', 'formatting', 'naming']
        }
        
        prioritized = {'critical': [], 'high': [], 'medium': [], 'low': []}
        
        for issue in issues:
            issue_lower = issue.lower()
            categorized = False
            
            for priority, keywords in priority_keywords.items():
                if any(keyword in issue_lower for keyword in keywords):
                    prioritized[priority].append(issue)
                    categorized = True
                    break
            
            if not categorized:
                prioritized['medium'].append(issue)
        
        # Return flattened list in priority order
        result = []
        for priority in ['critical', 'high', 'medium', 'low']:
            result.extend(prioritized[priority])
        
        return result
    
    def _prioritize_suggestions(self, suggestions: List[str]) -> List[str]:
        """Prioritize suggestions by potential impact."""
        
        # Simple prioritization based on keywords
        high_impact = []
        medium_impact = []
        low_impact = []
        
        for suggestion in suggestions:
            suggestion_lower = suggestion.lower()
            
            if any(keyword in suggestion_lower for keyword in ['refactor', 'optimize', 'security']):
                high_impact.append(suggestion)
            elif any(keyword in suggestion_lower for keyword in ['improve', 'enhance', 'add']):
                medium_impact.append(suggestion)
            else:
                low_impact.append(suggestion)
        
        return high_impact + medium_impact + low_impact

# Example usage
async def demo_multi_agent_review():
    """Demonstrate multi-agent code review workflow."""
    
    # Initialize orchestrator (assuming it's already set up)
    from agent_framework.core.agent_orchestrator import AgentOrchestrator
    from agent_framework.core.config import FrameworkConfig
    
    config = FrameworkConfig()
    orchestrator = AgentOrchestrator(config)
    await orchestrator.initialize()
    
    # Create multi-agent reviewer
    reviewer = MultiAgentCodeReviewer(orchestrator)
    
    # Example: Review a single file
    print("🔍 Single File Review Example")
    single_file_result = await reviewer.comprehensive_review(
        "src/user_service.py",
        "def get_user(id): return database.query(f'SELECT * FROM users WHERE id={id}')"
    )
    
    print(f"Overall Score: {single_file_result['overall_score']:.1f}/10")
    print(f"Recommendation: {single_file_result['recommendation']}")
    
    # Example: Review entire pull request
    print("\n📋 Pull Request Review Example")
    pr_files = ["src/user_service.py", "src/auth_handler.py", "tests/test_user_service.py"]
    pr_result = await reviewer.review_pull_request(pr_files)
    
    print(f"PR Average Score: {pr_result['pr_summary']['average_score']:.1f}/10")
    print(f"PR Recommendation: {pr_result['pr_summary']['recommendation']}")
    
    await orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(demo_multi_agent_review())
```
