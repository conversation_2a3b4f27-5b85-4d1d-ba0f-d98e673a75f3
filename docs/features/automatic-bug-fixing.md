# Automatic Bug Fix Loop

Automatic bug fix loop system for iterative debugging and error resolution.

## Workflow

The following diagram illustrates the workflow of the AutomaticBugFixLoop system:

```mermaid
graph TD
    A[初始化 AutomaticBugFixLoop] --> B[调用 start_fix_loop]
    B --> C[分析错误并执行根本原因分析]
    C --> D[生成调试建议]
    D --> E{启用高级分析?}
    E -->|是| F[使用 ErrorDetectionAgent 获取额外洞察]
    E -->|否| G[进入修复迭代循环]
    F --> G
    G --> H[检查是否还有未尝试的建议]
    H -->|否| I[结束修复会话]
    H -->|是| J[获取优先级最高的建议]
    J --> K[应用修复建议]
    K --> L[运行测试验证]
    L --> M{测试通过?}
    M -->|是| N[修复成功，结束循环]
    M -->|否| O[分析新错误]
    O --> P[生成更多修复建议]
    P --> Q[将新建议添加到建议列表]
    Q --> R[检查是否达到最大迭代次数]
    R -->|否| H
    R -->|是| I
    N --> I
    I --> S[更新性能指标和统计信息]
```

## Description

The AutomaticBugFixLoop class implements an automatic bug fixing system that iteratively debugs and resolves errors in code. The system works through the following steps:

1. **Initialization**: Sets up the necessary components including debugger, code editor, code generator, testing agent, and error detection agent.

2. **Error Analysis**: When an error occurs, the system analyzes the error and performs root cause analysis to understand the underlying issue.

3. **Suggestion Generation**: Based on the error analysis, the system generates debugging suggestions to fix the issue.

4. **Fix Iteration Loop**: The system enters a loop where it:
   - Applies the highest priority fix suggestion
   - Runs tests to verify the fix
   - If the fix is successful, the loop ends
   - If the fix fails, it analyzes the new error and generates more suggestions

5. **Advanced Analysis**: If enabled, the system uses an ErrorDetectionAgent to gain additional insights about potential issues in the code.

6. **Performance Tracking**: The system tracks performance metrics and statistics about the fix sessions to improve future performance.
