# CLI Reference Guide

Complete reference for the Agent Framework command-line interface.

## Overview

The Agent Framework provides a comprehensive CLI with the following main commands:

```mermaid
graph TD
    A[agent-framework] --> B[analyze]
    A --> C[generate]
    A --> D[optimize]
    A --> E[debug]
    A --> F[document]
    A --> G[enhance]
    A --> H[interactive]

    B --> B1[complexity]
    B --> B2[quality]
    B --> B3[patterns]
    B --> B4[dependencies]
    B --> B5[all]

    C --> C1[function]
    C --> C2[class]
    C --> C3[boilerplate]
    C --> C4[tests]

    D --> D1[performance]
    D --> D2[memory]
    D --> D3[algorithms]

    E --> E1[traceback]
    E --> E2[check-errors]
    E --> E3[auto-fix]
    E --> E4[code-smells]

    F --> F1[docstrings]
    F --> F2[api]
    F --> F3[readme]

    G --> G1[quality goals]
    G --> G2[performance goals]
    G --> G3[comprehensive]

    H --> H1[natural language]
    H --> H2[command help]
    H --> H3[status info]

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#f3e5f5
    style E fill:#ffebee
    style F fill:#e0f2f1
    style G fill:#fce4ec
    style H fill:#f1f8e9
```

**Main Commands:**

- **[analyze](#analyze)** - Code analysis and quality metrics
- **[generate](#generate)** - Code generation and creation
- **[optimize](#optimize)** - Performance and memory optimization
- **[debug](#debug)** - Error detection and debugging
- **[document](#document)** - Documentation generation
- **[enhance](#enhance)** - Comprehensive code enhancement
- **[interactive](#interactive)** - Interactive mode

## Global Options

Available for all commands:

```bash
agent-framework [GLOBAL_OPTIONS] COMMAND [COMMAND_OPTIONS]
```

### Global Options

| Option | Short | Description |
|--------|-------|-------------|
| `--config` | `-c` | Path to configuration file |
| `--verbose` | `-v` | Enable verbose output |
| `--quiet` | `-q` | Suppress non-essential output |
| `--no-color` | | Disable colored output |
| `--version` | | Show version information |
| `--help` | `-h` | Show help message |

## Commands

### analyze

Analyze code for quality, complexity, and potential issues.

```bash
agent-framework analyze [OPTIONS]
```

#### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--file FILE` | Analyze code from file | |
| `--code CODE` | Analyze code from string | |
| `--stdin` | Read code from stdin | |
| `--type TYPE` | Analysis type: `complexity`, `quality`, `patterns`, `dependencies`, `all` | `all` |
| `--threshold N` | Complexity threshold | 10 |
| `--detailed` | Show detailed analysis | |
| `--format FORMAT` | Output format: `text`, `json`, `yaml` | `text` |
| `--enable-evaluation-cycles` | Enable automatic evaluation | |

#### Examples

```bash
# Analyze a Python file
agent-framework analyze --file mycode.py --type all --detailed

# Analyze code complexity
agent-framework analyze --code "def complex_function(): pass" --type complexity

# Check for code smells
agent-framework analyze --file legacy_code.py --type patterns
```

### generate

Generate code from specifications.

```bash
agent-framework generate TYPE [OPTIONS]
```

#### Subcommands

##### function

Generate a function with docstrings and type hints.

```bash
agent-framework generate function [OPTIONS]
```

**Options:**

- `--name NAME` - Function name (required)
- `--description DESC` - Function description (required)
- `--parameters PARAMS` - Parameters in format "name:type:default"
- `--return-type TYPE` - Return type annotation
- `--async` - Generate async function
- `--docstring-style STYLE` - Docstring style: `google`, `numpy`, `sphinx`

**Example:**

```bash
agent-framework generate function \
  --name calculate_average \
  --description "Calculate average of numbers" \
  --parameters "numbers:List[float]" "exclude_zeros:bool:False" \
  --return-type "float"
```

##### class

Generate a class with methods and attributes.

```bash
agent-framework generate class [OPTIONS]
```

**Options:**

- `--name NAME` - Class name (required)
- `--description DESC` - Class description (required)
- `--attributes ATTRS` - Attributes in format "name:type"
- `--methods METHODS` - Methods in format "name:description"
- `--inherit-from CLASS` - Base class to inherit from

**Example:**

```bash
agent-framework generate class \
  --name UserManager \
  --description "Manage user accounts" \
  --attributes "users:List[User]" \
  --methods "add_user:Add a new user" "find_user:Find user by ID"
```

##### boilerplate

Generate project boilerplate code.

```bash
agent-framework generate boilerplate [OPTIONS]
```

**Options:**

- `--type TYPE` - Project type: `flask_app`, `fastapi_app`, `cli_app`
- `--name NAME` - Project name (required)
- `--features FEATURES` - Features to include: `database`, `auth`, `api`
- `--directory DIR` - Output directory

**Example:**

```bash
agent-framework generate boilerplate \
  --type flask_app \
  --name myapp \
  --features database auth
```

##### tests

Generate test cases for existing code.

```bash
agent-framework generate tests [OPTIONS]
```

**Options:**

- `--file FILE` - File to generate tests for (required)
- `--framework FRAMEWORK` - Test framework: `pytest`, `unittest`
- `--coverage` - Include coverage annotations
- `--mocking` - Include mock examples

**Example:**

```bash
agent-framework generate tests \
  --file src/calculator.py \
  --framework pytest \
  --coverage
```

### optimize

Optimize code for performance and efficiency.

```bash
agent-framework optimize [OPTIONS]
```

#### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--file FILE` | File to optimize | |
| `--code CODE` | Code string to optimize | |
| `--type TYPE` | Optimization type: `performance`, `memory`, `algorithms`, `all` | `all` |
| `--target TARGET` | Target metric: `speed`, `memory`, `readability` | `speed` |
| `--aggressive` | Use aggressive optimizations | |
| `--safe-only` | Only safe optimizations | |
| `--show-diff` | Show before/after diff | |

#### Examples

```bash
# Optimize for performance
agent-framework optimize --file slow_code.py --type performance --show-diff

# Memory optimization
agent-framework optimize --code "data = [x for x in range(1000000)]" --type memory

# Algorithm improvements
agent-framework optimize --file algorithms.py --type algorithms --aggressive
```

### debug

Debug code and analyze errors.

```bash
agent-framework debug [OPTIONS]
```

#### Options

| Option | Description |
|--------|-------------|
| `--traceback FILE` | Analyze error traceback from file |
| `--check-errors` | Check code for potential errors |
| `--syntax-check` | Check code syntax |
| `--auto-fix` | Automatically attempt to fix issues |
| `--code-smells` | Detect code smells |
| `--severity LEVEL` | Minimum severity: `low`, `medium`, `high` |
| `--suggest-fixes` | Suggest fixes for issues |
| `--max-iterations N` | Maximum fix iterations |

#### Examples

```bash
# Analyze error traceback
agent-framework debug --traceback error.txt --suggest-fixes

# Check for potential errors
agent-framework debug --check-errors --file buggy_code.py

# Automatic fixing
agent-framework debug --auto-fix --file buggy_code.py --max-iterations 3
```

### document

Generate documentation for code.

```bash
agent-framework document TYPE [OPTIONS]
```

#### Subcommands

##### docstrings

Generate docstrings for functions and classes.

```bash
agent-framework document docstrings [OPTIONS]
```

**Options:**

- `--file FILE` - File to document (required)
- `--style STYLE` - Docstring style: `google`, `numpy`, `sphinx`
- `--include-examples` - Include usage examples

##### api

Generate API documentation.

```bash
agent-framework document api [OPTIONS]
```

**Options:**

- `--file FILE` - File to document (required)
- `--format FORMAT` - Output format: `markdown`, `rst`, `html`
- `--include-private` - Include private methods
- `--with-toc` - Generate table of contents

##### readme

Generate README file for project.

```bash
agent-framework document readme [OPTIONS]
```

**Options:**

- `--project-name NAME` - Project name (required)
- `--description DESC` - Project description (required)
- `--features FEATURES` - List of features
- `--installation TEXT` - Installation instructions

#### Examples

```bash
# Generate docstrings
agent-framework document docstrings \
  --file mymodule.py \
  --style google \
  --include-examples

# Generate API documentation
agent-framework document api \
  --file mypackage.py \
  --format markdown \
  --with-toc
```

### enhance

Comprehensively enhance code using advanced capabilities.

```bash
agent-framework enhance [OPTIONS]
```

#### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--file FILE` | File to enhance | |
| `--code CODE` | Code string to enhance | |
| `--goals GOALS` | Enhancement goals: `quality`, `performance`, `security`, `maintainability`, `testing`, `documentation` | `quality` |
| `--enable-bug-fixing` | Enable automatic bug fixing | |
| `--enable-evaluation` | Enable evaluation cycles | |
| `--max-iterations N` | Maximum iterations | 3 |
| `--comprehensive` | Run all enhancements | |
| `--output-file FILE` | Output file for enhanced code | |

#### Examples

```bash
# Enhance code quality and performance
agent-framework enhance --file code.py --goals quality performance

# Comprehensive enhancement
agent-framework enhance --file app.py --comprehensive --enable-bug-fixing
```

### interactive

Start interactive mode for natural language queries.

```bash
agent-framework interactive
```

In interactive mode, you can:

- Ask natural language questions
- Use framework commands
- Get help and status information

#### Interactive Commands

- `help` - Show available commands
- `status` - Show system status
- `history` - Show command history
- `clear` - Clear screen
- `exit` - Exit interactive mode

## Configuration

### Environment Variables

```bash
export AGENT_API_KEY="your-api-key"
export AGENT_MODEL="gpt-4o"
export AGENT_BASE_URL="https://api.openai.com/v1"
export AGENT_DEBUG="false"
```

### Configuration File

Create `config.yaml`:

```yaml
model:
  model: "gpt-4o"
  api_key: "${AGENT_API_KEY}"
  temperature: 0.7

plugins:
  auto_load_plugins: true

logging:
  level: "INFO"
```

## Exit Codes

| Code | Meaning |
|------|---------|
| 0 | Success |
| 1 | General error |
| 2 | Invalid arguments |
| 130 | Interrupted by user (Ctrl+C) |

## See Also

- [Interactive Mode Guide](interactive-mode.md)
- [Configuration Guide](configuration.md)
- [Examples](../examples/basic-usage.md)
- [Troubleshooting](../troubleshooting/common-issues.md)
