# Multi-Agent Collaboration Guide

This guide covers the enhanced multi-agent capabilities of the agent framework, including setup, configuration, and usage patterns.

## Overview

The multi-agent system enables multiple specialized agents to collaborate on complex tasks through:

- **Agent Specialization**: Different agents with specific capabilities (code analysis, testing, documentation, etc.)
- **Task Coordination**: Intelligent delegation and coordination of tasks across agents
- **MCP Integration**: Enhanced Model Context Protocol support for tool access
- **Communication**: Inter-agent messaging and result sharing
- **Monitoring**: Comprehensive logging and performance tracking

## Architecture

```mermaid
graph TB
    subgraph "User Interface"
        User[User Request]
    end

    subgraph "Orchestration Layer"
        Orchestrator[Agent Orchestrator]
        TaskDecomposer[Task Decomposer]
        ResultAggregator[Result Aggregator]
    end

    subgraph "Specialized Agents"
        CodeAnalyst[Code Analysis Agent]
        Tester[Testing Agent]
        Writer[Documentation Agent]
        Refactorer[Refactoring Agent]
        ErrorDetector[Error Detection Agent]
    end

    subgraph "Coordination Mechanisms"
        MessageBroker[Message Broker]
        SharedContext[Shared Context]
        TaskQueue[Task Queue]
    end

    subgraph "Model Providers"
        OpenAI[OpenAI GPT-4]
        Anthropic[Claude 3.5]
        Local[Local Models]
    end

    User --> Orchestrator
    Orchestrator --> TaskDecomposer
    TaskDecomposer --> TaskQueue

    TaskQueue --> CodeAnalyst
    TaskQueue --> Tester
    TaskQueue --> Writer
    TaskQueue --> Refactorer
    TaskQueue --> ErrorDetector

    CodeAnalyst --> MessageBroker
    Tester --> MessageBroker
    Writer --> MessageBroker
    Refactorer --> MessageBroker
    ErrorDetector --> MessageBroker

    MessageBroker --> SharedContext
    SharedContext --> ResultAggregator
    ResultAggregator --> Orchestrator
    Orchestrator --> User

    CodeAnalyst --> OpenAI
    Tester --> Anthropic
    Writer --> Local
    Refactorer --> OpenAI
    ErrorDetector --> Anthropic

    style Orchestrator fill:#e3f2fd
    style TaskDecomposer fill:#fff3e0
    style ResultAggregator fill:#e8f5e8
    style MessageBroker fill:#f3e5f5
```

### Core Components

1. **Agent Registry**: Manages agent lifecycle and capability discovery
2. **Agent Manager**: Coordinates task delegation and execution
3. **Agent Coordinator**: Orchestrates complex multi-agent workflows
4. **MCP Connection Manager**: Manages connections to MCP servers
5. **Multi-Agent Logger**: Specialized logging for agent interactions

### Specialized Agents

- **CodeAnalysisAgent**: Code quality, complexity, and security analysis
- **TestingAgent**: Test generation, execution, and coverage analysis
- **DocumentationAgent**: API docs, README generation, and user guides
- **RefactoringAgent**: Code improvement and optimization
- **ErrorDetectionAgent**: Bug detection and issue identification
- **OptimizationAgent**: Performance optimization suggestions

## Configuration

### Basic Multi-Agent Setup

```python
from agent_framework.core.config import (
    FrameworkConfig, MultiAgentConfig, AgentRoleConfig, 
    ModelConfig, MCPConfig, MCPServerConfig
)

# Configure model for agents
model_config = ModelConfig(
    provider="openrouter",
    model="qwen/qwen3-coder:free",
    api_key="your-api-key",
    base_url="https://openrouter.ai/api/v1"
)

# Configure MCP servers
mcp_config = MCPConfig(
    servers={
        "filesystem": MCPServerConfig(
            command="npx",
            args=["-y", "@modelcontextprotocol/server-filesystem", "/path/to/project"],
            description="File system access"
        )
    }
)

# Configure multi-agent system
multi_agent_config = MultiAgentConfig(
    enabled=True,
    max_agents=5,
    coordination_strategy="capability_based",
    agent_roles={
        "code_analyst": AgentRoleConfig(
            name="code_analyst",
            description="Code analysis specialist",
            system_message="You are a code analysis expert...",
            capabilities=["code_analysis", "error_detection"],
            model_config=model_config,
            mcp_servers=["filesystem"]
        )
    }
)

config = FrameworkConfig(
    multi_agent=multi_agent_config,
    mcp=mcp_config
)
```

### Agent Role Configuration

Each agent role can be configured with:

- **Capabilities**: What the agent can do (code_analysis, testing, documentation, etc.)
- **Model Configuration**: Specific model settings for the agent
- **MCP Servers**: Which MCP servers the agent can access
- **System Message**: Specialized instructions for the agent
- **Priority**: Agent priority for task assignment
- **Concurrency**: Maximum concurrent tasks

## Usage Patterns

### 1. Automatic Multi-Agent Coordination

```python
from agent_framework import AgentOrchestrator
from agent_framework.core.types import Task

orchestrator = AgentOrchestrator(config)
await orchestrator.initialize()

# Create a complex task
task = Task(
    name="comprehensive_code_review",
    description="Analyze code quality, generate tests, and create documentation",
    task_type="workflow",
    parameters={
        "file_path": "src/main.py",
        "include_tests": True,
        "include_docs": True
    }
)

# Execute with automatic agent coordination
result = await orchestrator.execute_multi_agent_task(task)
```

### 2. Direct Agent Delegation

```python
# Delegate to a specific agent type
result = await orchestrator.delegate_task_to_agent(task, "code_analyst")
```

### 3. Custom Workflows

```python
from agent_framework.coordination.coordinator import AgentCoordinator

# Define a custom workflow
workflow_definition = {
    "name": "code_quality_pipeline",
    "steps": [
        {
            "name": "analyze",
            "agent_role": "code_analyst",
            "task_type": "code_analysis"
        },
        {
            "name": "test",
            "agent_role": "tester", 
            "task_type": "test_generation",
            "depends_on": ["analyze"]
        },
        {
            "name": "document",
            "agent_role": "documenter",
            "task_type": "documentation",
            "depends_on": ["analyze", "test"]
        }
    ]
}

# Execute the workflow
result = await coordinator.coordinate_workflow(workflow_definition)
```

## MCP Integration

### Supported MCP Features

The framework implements the MCP 2025-06-18 specification with support for:

- **Tools**: Function calling capabilities
- **Resources**: File and data access
- **Prompts**: Reusable prompt templates
- **Server Discovery**: Automatic detection of available servers
- **Connection Management**: Robust connection handling with retry logic

### MCP Server Examples

```python
# Filesystem server for file operations
"filesystem": MCPServerConfig(
    command="npx",
    args=["-y", "@modelcontextprotocol/server-filesystem", "/project/path"],
    description="File system access"
)

# Web fetch server for internet access
"fetch": MCPServerConfig(
    command="uvx", 
    args=["mcp-server-fetch"],
    description="Web content fetching"
)

# Database server for data access
"database": MCPServerConfig(
    command="python",
    args=["-m", "mcp_server_database", "--connection-string", "sqlite:///data.db"],
    description="Database operations"
)
```

## Coordination Strategies

### 1. Capability-Based (Default)

Tasks are assigned to agents based on required capabilities:

```python
coordination_strategy="capability_based"
```

### 2. Round-Robin

Tasks are distributed evenly across available agents:

```python
coordination_strategy="round_robin"
```

### 3. Priority-Based

Higher priority agents receive tasks first:

```python
coordination_strategy="priority"
```

### 4. Load-Balanced

Tasks are assigned to agents with the lowest current load:

```python
coordination_strategy="load_balanced"
```

## Monitoring and Logging

### Multi-Agent Logging

The framework provides specialized logging for multi-agent interactions:

```python
# Get system status
status = await orchestrator.get_multi_agent_status()

# Access logs
from agent_framework.monitoring import MultiAgentLogger

logger = MultiAgentLogger(log_dir="logs")
# Logs are automatically generated for:
# - Agent lifecycle events
# - Task delegation and execution
# - Inter-agent communication
# - Performance metrics
# - Errors and debugging
```

### Log Structure

Logs are structured JSON for easy parsing:

```json
{
  "timestamp": "2025-01-31T10:30:00Z",
  "session_id": "1738234200",
  "sequence": 42,
  "event_type": "task_delegation",
  "task_id": "uuid-here",
  "task_name": "analyze_code",
  "assigned_agent": "code_analyst",
  "delegation_success": true
}
```

## Best Practices

### 1. Agent Design

- **Single Responsibility**: Each agent should have a clear, focused role
- **Capability Mapping**: Ensure capabilities accurately reflect agent abilities
- **System Messages**: Provide clear, specific instructions for each agent

### 2. Task Design

- **Clear Requirements**: Specify required capabilities and expected outputs
- **Appropriate Granularity**: Break complex tasks into manageable subtasks
- **Context Sharing**: Provide necessary context for agent coordination

### 3. Performance Optimization

- **Concurrent Execution**: Use parallel coordination for independent tasks
- **Resource Management**: Monitor agent load and adjust concurrency limits
- **Caching**: Leverage context caching for repeated operations

### 4. Error Handling

- **Graceful Degradation**: Implement fallback strategies for agent failures
- **Retry Logic**: Configure appropriate retry policies for transient failures
- **Monitoring**: Set up alerts for critical failures

## Troubleshooting

### Common Issues

1. **Agent Not Found**: Check agent role configuration and registration
2. **MCP Connection Failed**: Verify server command and arguments
3. **Task Delegation Failed**: Ensure required capabilities are available
4. **Performance Issues**: Monitor agent load and adjust concurrency

### Debug Mode

Enable debug logging for detailed information:

```python
config = FrameworkConfig(
    debug=True,
    logging=LoggingConfig(level="DEBUG")
)
```

### Health Checks

Monitor system health:

```python
# Check agent registry status
registry_stats = await agent_registry.get_registry_stats()

# Check MCP connections
mcp_stats = await mcp_manager.get_connection_stats()

# Check coordination status
coordination_status = await agent_manager.get_coordination_status()
```

## Examples

See the `examples/` directory for complete working examples:

- `multi_agent_example.py`: Basic multi-agent setup and usage
- `workflow_example.py`: Complex workflow coordination
- `mcp_integration_example.py`: MCP server integration
- `monitoring_example.py`: System monitoring and logging

## API Reference

For detailed API documentation, see:

- [Agent Framework API](api/agent_framework.md)
- [Multi-Agent Types](api/multi_agent_types.md)
- [MCP Integration](api/mcp.md)
- [Coordination System](api/coordination.md)
