"""
Default Test Generation Plugin for the Agent Framework.

Provides test generation capabilities including:
- Unit test generation
- Integration test generation
- Test coverage analysis
- Mock generation
"""

import ast
import logging
from typing import Any, Dict, List, Optional

from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability


class TestGenerationPlugin(PluginInterface):
    """Default test generation plugin implementation."""
    
    PLUGIN_NAME = "test_generation"
    PLUGIN_VERSION = "1.0.0"
    
    def __init__(self):
        """Initialize the test generation plugin."""
        self.logger = logging.getLogger(f"{__name__}.{self.PLUGIN_NAME}")
        self._is_initialized = False
        self._capabilities = [
            PluginCapability(
                name="generate_unit_tests",
                description="Generate unit tests for functions and classes",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to test"},
                        "framework": {"type": "string", "enum": ["pytest", "unittest"], "default": "pytest"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "test_code": {"type": "string"},
                        "tests_generated": {"type": "number"}
                    }
                }
            ),
            PluginCapability(
                name="generate_integration_tests",
                description="Generate integration tests",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"test_code": {"type": "string"}}}
            ),
            PluginCapability(
                name="calculate_coverage",
                description="Calculate test coverage requirements",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"coverage_report": {"type": "object"}}}
            ),
            PluginCapability(
                name="generate_mocks",
                description="Generate mock objects for testing",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"mock_code": {"type": "string"}}}
            )
        ]
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True
        self.logger.info(f"Test generation plugin {self.version} initialized")
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )
        
        try:
            capability = request.capability
            parameters = request.parameters
            
            if capability == "generate_unit_tests":
                result = await self._generate_unit_tests(parameters)
            elif capability == "generate_integration_tests":
                result = await self._generate_integration_tests(parameters)
            elif capability == "calculate_coverage":
                result = await self._calculate_coverage(parameters)
            elif capability == "generate_mocks":
                result = await self._generate_mocks(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )
            
            return PluginResponse(
                success=True,
                data=result
            )
            
        except Exception as e:
            self.logger.error(f"Plugin execution failed: {e}")
            return PluginResponse(
                success=False,
                error=str(e)
            )
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return self._capabilities
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self.logger.info("Test generation plugin cleaned up")
    
    async def _generate_unit_tests(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate unit tests for functions and classes."""
        code = parameters.get("code", "")
        framework = parameters.get("framework", "pytest")
        
        if not code:
            raise ValueError("Code parameter is required")
        
        try:
            tree = ast.parse(code)
            test_cases = []
            tests_generated = 0
            
            # Generate tests for functions
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    if not node.name.startswith('_'):  # Skip private methods
                        test_case = self._generate_function_test(node, framework)
                        if test_case:
                            test_cases.append(test_case)
                            tests_generated += 1
                
                elif isinstance(node, ast.ClassDef):
                    class_tests = self._generate_class_tests(node, framework)
                    test_cases.extend(class_tests)
                    tests_generated += len(class_tests)
            
            # Generate test file structure
            if framework == "pytest":
                test_code = self._generate_pytest_file(test_cases)
            else:
                test_code = self._generate_unittest_file(test_cases)
            
            return {
                "test_code": test_code,
                "tests_generated": tests_generated,
                "framework": framework
            }
            
        except Exception as e:
            self.logger.error(f"Error generating unit tests: {e}")
            return {
                "test_code": f"# Error generating tests: {e}",
                "tests_generated": 0,
                "error": str(e)
            }
    
    async def _generate_integration_tests(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate integration tests."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        integration_test = f'''"""
Integration tests for the module.
"""

import pytest
from unittest.mock import Mock, patch


class TestIntegration:
    """Integration test suite."""
    
    def test_module_integration(self):
        """Test module integration."""
        # TODO: Implement integration test
        assert True
    
    def test_external_dependencies(self):
        """Test external dependencies."""
        # TODO: Test external API calls, database connections, etc.
        assert True
    
    def test_end_to_end_workflow(self):
        """Test complete workflow."""
        # TODO: Test complete user workflow
        assert True
'''
        
        return {
            "test_code": integration_test,
            "test_types": ["integration", "dependencies", "end_to_end"]
        }
    
    async def _calculate_coverage(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate test coverage requirements."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        try:
            tree = ast.parse(code)
            
            coverage_report = {
                "total_functions": 0,
                "total_classes": 0,
                "total_lines": len(code.splitlines()),
                "testable_functions": 0,
                "coverage_targets": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    coverage_report["total_functions"] += 1
                    if not node.name.startswith('_'):
                        coverage_report["testable_functions"] += 1
                        coverage_report["coverage_targets"].append({
                            "type": "function",
                            "name": node.name,
                            "line": node.lineno,
                            "complexity": self._estimate_complexity(node)
                        })
                
                elif isinstance(node, ast.ClassDef):
                    coverage_report["total_classes"] += 1
                    coverage_report["coverage_targets"].append({
                        "type": "class",
                        "name": node.name,
                        "line": node.lineno,
                        "methods": len([n for n in node.body if isinstance(n, ast.FunctionDef)])
                    })
            
            # Calculate recommended coverage percentage
            complexity_score = sum(target.get("complexity", 1) for target in coverage_report["coverage_targets"])
            recommended_coverage = min(95, max(80, 70 + complexity_score * 2))
            coverage_report["recommended_coverage"] = recommended_coverage
            
            return {"coverage_report": coverage_report}
            
        except Exception as e:
            self.logger.error(f"Error calculating coverage: {e}")
            return {"coverage_report": {"error": str(e)}}
    
    async def _generate_mocks(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock objects for testing."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        mock_code = '''"""
Mock objects for testing.
"""

from unittest.mock import Mock, MagicMock, patch


class MockFactory:
    """Factory for creating mock objects."""
    
    @staticmethod
    def create_mock_database():
        """Create a mock database connection."""
        mock_db = Mock()
        mock_db.connect.return_value = True
        mock_db.execute.return_value = []
        mock_db.close.return_value = None
        return mock_db
    
    @staticmethod
    def create_mock_api_client():
        """Create a mock API client."""
        mock_client = Mock()
        mock_client.get.return_value = {"status": "success", "data": {}}
        mock_client.post.return_value = {"status": "created", "id": 123}
        return mock_client
    
    @staticmethod
    def create_mock_file_system():
        """Create a mock file system."""
        mock_fs = Mock()
        mock_fs.read.return_value = "mock file content"
        mock_fs.write.return_value = True
        mock_fs.exists.return_value = True
        return mock_fs


# Common mock decorators
def mock_database(func):
    """Decorator to mock database operations."""
    return patch('your_module.database', MockFactory.create_mock_database())(func)


def mock_api_calls(func):
    """Decorator to mock API calls."""
    return patch('your_module.api_client', MockFactory.create_mock_api_client())(func)
'''
        
        return {
            "mock_code": mock_code,
            "mock_types": ["database", "api_client", "file_system"]
        }
    
    def _generate_function_test(self, node: ast.FunctionDef, framework: str) -> Optional[str]:
        """Generate test for a single function."""
        func_name = node.name
        args = [arg.arg for arg in node.args.args if arg.arg != 'self']
        
        if framework == "pytest":
            test_template = f'''
def test_{func_name}():
    """Test {func_name} function."""
    # Arrange
    {self._generate_test_data(args)}
    
    # Act
    result = {func_name}({", ".join(args) if args else ""})
    
    # Assert
    assert result is not None
    # TODO: Add specific assertions
'''
        else:  # unittest
            test_template = f'''
def test_{func_name}(self):
    """Test {func_name} function."""
    # Arrange
    {self._generate_test_data(args)}
    
    # Act
    result = {func_name}({", ".join(args) if args else ""})
    
    # Assert
    self.assertIsNotNone(result)
    # TODO: Add specific assertions
'''
        
        return test_template
    
    def _generate_class_tests(self, node: ast.ClassDef, framework: str) -> List[str]:
        """Generate tests for a class."""
        class_name = node.name
        methods = [n for n in node.body if isinstance(n, ast.FunctionDef) and not n.name.startswith('_')]
        
        tests = []
        
        # Constructor test
        if framework == "pytest":
            constructor_test = f'''
def test_{class_name.lower()}_creation():
    """Test {class_name} creation."""
    instance = {class_name}()
    assert instance is not None
'''
        else:
            constructor_test = f'''
def test_{class_name.lower()}_creation(self):
    """Test {class_name} creation."""
    instance = {class_name}()
    self.assertIsNotNone(instance)
'''
        tests.append(constructor_test)
        
        # Method tests
        for method in methods:
            if framework == "pytest":
                method_test = f'''
def test_{class_name.lower()}_{method.name}():
    """Test {class_name}.{method.name} method."""
    instance = {class_name}()
    result = instance.{method.name}()
    assert result is not None
    # TODO: Add specific assertions
'''
            else:
                method_test = f'''
def test_{class_name.lower()}_{method.name}(self):
    """Test {class_name}.{method.name} method."""
    instance = {class_name}()
    result = instance.{method.name}()
    self.assertIsNotNone(result)
    # TODO: Add specific assertions
'''
            tests.append(method_test)
        
        return tests
    
    def _generate_pytest_file(self, test_cases: List[str]) -> str:
        """Generate complete pytest file."""
        header = '''"""
Unit tests generated by Agent Framework.
"""

import pytest
from unittest.mock import Mock, patch

# Import the module under test
# from your_module import *

'''
        
        return header + "\n".join(test_cases)
    
    def _generate_unittest_file(self, test_cases: List[str]) -> str:
        """Generate complete unittest file."""
        header = '''"""
Unit tests generated by Agent Framework.
"""

import unittest
from unittest.mock import Mock, patch

# Import the module under test
# from your_module import *


class TestModule(unittest.TestCase):
    """Test suite for the module."""
    
    def setUp(self):
        """Set up test fixtures."""
        pass
    
    def tearDown(self):
        """Clean up after tests."""
        pass
'''
        
        # Indent test cases for class
        indented_tests = []
        for test in test_cases:
            indented_test = "\n".join(f"    {line}" for line in test.split("\n"))
            indented_tests.append(indented_test)
        
        footer = '''

if __name__ == '__main__':
    unittest.main()
'''
        
        return header + "\n".join(indented_tests) + footer
    
    def _generate_test_data(self, args: List[str]) -> str:
        """Generate test data for function arguments."""
        if not args:
            return "# No arguments needed"
        
        test_data = []
        for arg in args:
            test_data.append(f"{arg} = None  # TODO: Provide test data")
        
        return "\n    ".join(test_data)
    
    def _estimate_complexity(self, node: ast.FunctionDef) -> int:
        """Estimate function complexity for coverage planning."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.For, ast.While, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
        
        return complexity
