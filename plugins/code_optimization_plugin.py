"""
Default Code Optimization Plugin for the Agent Framework.

Provides code optimization capabilities including:
- Performance optimization
- Memory optimization
- Algorithm improvements
- Refactoring suggestions
"""

import ast
import logging
import re
from typing import Any, Dict, List, Optional

from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability


class CodeOptimizationPlugin(PluginInterface):
    """Default code optimization plugin implementation."""
    
    PLUGIN_NAME = "code_optimization"
    PLUGIN_VERSION = "1.0.0"
    
    def __init__(self):
        """Initialize the code optimization plugin."""
        self.logger = logging.getLogger(f"{__name__}.{self.PLUGIN_NAME}")
        self._is_initialized = False
        self._capabilities = [
            PluginCapability(
                name="optimize_performance",
                description="Optimize code for better performance",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to optimize"},
                        "target_metric": {"type": "string", "enum": ["speed", "memory", "both"]}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "optimized_code": {"type": "string"},
                        "optimizations": {"type": "array"},
                        "performance_gain": {"type": "number"}
                    }
                }
            ),
            PluginCapability(
                name="optimize_memory",
                description="Optimize code for memory efficiency",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"optimized_code": {"type": "string"}}}
            ),
            PluginCapability(
                name="suggest_algorithms",
                description="Suggest better algorithms",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"suggestions": {"type": "array"}}}
            ),
            PluginCapability(
                name="identify_bottlenecks",
                description="Identify performance bottlenecks",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"bottlenecks": {"type": "array"}}}
            ),
            PluginCapability(
                name="refactor_for_efficiency",
                description="Refactor code for better efficiency",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"refactored_code": {"type": "string"}}}
            )
        ]
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True
        self.logger.info(f"Code optimization plugin {self.version} initialized")
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )
        
        try:
            capability = request.capability
            parameters = request.parameters
            
            if capability == "optimize_performance":
                result = await self._optimize_performance(parameters)
            elif capability == "optimize_memory":
                result = await self._optimize_memory(parameters)
            elif capability == "suggest_algorithms":
                result = await self._suggest_algorithms(parameters)
            elif capability == "identify_bottlenecks":
                result = await self._identify_bottlenecks(parameters)
            elif capability == "refactor_for_efficiency":
                result = await self._refactor_for_efficiency(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )
            
            return PluginResponse(
                success=True,
                result=result
            )
            
        except Exception as e:
            self.logger.error(f"Plugin execution failed: {e}")
            return PluginResponse(
                success=False,
                error=str(e)
            )
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return self._capabilities
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self.logger.info("Code optimization plugin cleaned up")
    
    async def _optimize_performance(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize code for performance."""
        code = parameters.get("code", "")
        target_metric = parameters.get("target_metric", "speed")
        
        if not code:
            raise ValueError("Code parameter is required")
        
        optimizations = []
        optimized_code = code
        
        # Apply various optimization techniques
        if target_metric in ["speed", "both"]:
            optimized_code, speed_opts = self._apply_speed_optimizations(optimized_code)
            optimizations.extend(speed_opts)
        
        if target_metric in ["memory", "both"]:
            optimized_code, memory_opts = self._apply_memory_optimizations(optimized_code)
            optimizations.extend(memory_opts)
        
        # Calculate estimated performance gain
        performance_gain = len(optimizations) * 0.1  # Rough estimate
        
        return {
            "optimized_code": optimized_code,
            "optimizations": optimizations,
            "performance_gain": min(performance_gain, 0.5),  # Cap at 50%
            "original_lines": len(code.splitlines()),
            "optimized_lines": len(optimized_code.splitlines())
        }
    
    async def _optimize_memory(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize code for memory efficiency."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        optimized_code, optimizations = self._apply_memory_optimizations(code)
        
        return {
            "optimized_code": optimized_code,
            "optimizations": optimizations,
            "memory_savings": len(optimizations) * 0.05  # Rough estimate
        }
    
    async def _suggest_algorithms(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest better algorithms."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        suggestions = []
        
        # Analyze code for algorithmic improvements
        try:
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.For):
                    # Nested loops detection
                    nested_loops = self._count_nested_loops(node)
                    if nested_loops > 1:
                        suggestions.append({
                            "type": "algorithm",
                            "line": node.lineno,
                            "issue": f"Nested loops detected (depth: {nested_loops})",
                            "suggestion": "Consider using more efficient algorithms like hash tables or sets",
                            "complexity": f"O(n^{nested_loops})"
                        })
                
                elif isinstance(node, ast.ListComp):
                    # List comprehension optimization
                    suggestions.append({
                        "type": "optimization",
                        "line": node.lineno,
                        "issue": "List comprehension found",
                        "suggestion": "Consider using generator expressions for memory efficiency",
                        "example": "Use (x for x in items) instead of [x for x in items]"
                    })
        
        except Exception as e:
            self.logger.error(f"Error analyzing algorithms: {e}")
        
        # Add general algorithmic suggestions
        if "sort" in code.lower():
            suggestions.append({
                "type": "algorithm",
                "issue": "Sorting operation detected",
                "suggestion": "Consider using appropriate sorting algorithm based on data size",
                "options": ["TimSort (Python default)", "QuickSort", "MergeSort", "HeapSort"]
            })
        
        return {"suggestions": suggestions}
    
    async def _identify_bottlenecks(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Identify performance bottlenecks."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        bottlenecks = []
        
        # Common bottleneck patterns
        bottleneck_patterns = [
            (r'\.append\(.*\)', "List append in loop - consider list comprehension"),
            (r'for.*in.*range\(len\(', "Range-len pattern - use enumerate or direct iteration"),
            (r'\.join\(.*\)', "String join - good practice, but check if needed in loop"),
            (r'open\(.*\)', "File operations - ensure proper closing and consider context managers"),
            (r'import.*\*', "Wildcard imports - can slow down startup and cause namespace pollution")
        ]
        
        lines = code.splitlines()
        for i, line in enumerate(lines, 1):
            for pattern, message in bottleneck_patterns:
                if re.search(pattern, line):
                    bottlenecks.append({
                        "type": "bottleneck",
                        "line": i,
                        "code": line.strip(),
                        "issue": message,
                        "severity": "medium"
                    })
        
        # Analyze AST for more complex bottlenecks
        try:
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                # Deep nesting
                if isinstance(node, (ast.For, ast.While, ast.If)):
                    depth = self._calculate_nesting_depth(node)
                    if depth > 4:
                        bottlenecks.append({
                            "type": "complexity",
                            "line": node.lineno,
                            "issue": f"Deep nesting (depth: {depth})",
                            "suggestion": "Consider extracting methods to reduce complexity",
                            "severity": "high"
                        })
        
        except Exception as e:
            self.logger.error(f"Error identifying bottlenecks: {e}")
        
        return {"bottlenecks": bottlenecks}
    
    async def _refactor_for_efficiency(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Refactor code for better efficiency."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        refactored_code = code
        refactoring_steps = []
        
        # Apply common refactoring patterns
        
        # 1. Replace range(len()) with enumerate
        if re.search(r'for\s+\w+\s+in\s+range\(len\(', refactored_code):
            refactored_code = re.sub(
                r'for\s+(\w+)\s+in\s+range\(len\((\w+)\)\):',
                r'for \1, item in enumerate(\2):',
                refactored_code
            )
            refactoring_steps.append("Replaced range(len()) with enumerate")
        
        # 2. Convert list comprehensions to generator expressions where appropriate
        # This is a simplified example - real implementation would be more sophisticated
        if '[' in refactored_code and 'for' in refactored_code and 'in' in refactored_code:
            refactoring_steps.append("Consider converting list comprehensions to generators for memory efficiency")
        
        # 3. Suggest using sets for membership testing
        if ' in ' in refactored_code and '[' in refactored_code:
            refactoring_steps.append("Consider using sets instead of lists for membership testing")
        
        return {
            "refactored_code": refactored_code,
            "refactoring_steps": refactoring_steps,
            "efficiency_improvements": len(refactoring_steps)
        }
    
    def _apply_speed_optimizations(self, code: str) -> tuple[str, List[Dict[str, Any]]]:
        """Apply speed optimizations to code."""
        optimized_code = code
        optimizations = []
        
        # Replace inefficient patterns
        
        # 1. String concatenation in loops
        if '+=' in code and 'for' in code:
            optimizations.append({
                "type": "speed",
                "description": "String concatenation in loop detected",
                "suggestion": "Use list.append() and ''.join() instead",
                "impact": "high"
            })
        
        # 2. Repeated function calls
        if code.count('len(') > 2:
            optimizations.append({
                "type": "speed", 
                "description": "Multiple len() calls detected",
                "suggestion": "Cache length in variable",
                "impact": "low"
            })
        
        return optimized_code, optimizations
    
    def _apply_memory_optimizations(self, code: str) -> tuple[str, List[Dict[str, Any]]]:
        """Apply memory optimizations to code."""
        optimized_code = code
        optimizations = []
        
        # 1. List comprehensions to generators
        if '[' in code and 'for' in code:
            optimizations.append({
                "type": "memory",
                "description": "List comprehension found",
                "suggestion": "Consider using generator expression",
                "impact": "medium"
            })
        
        # 2. Large data structures
        if 'range(' in code:
            optimizations.append({
                "type": "memory",
                "description": "Range usage detected",
                "suggestion": "Range is already memory efficient in Python 3",
                "impact": "info"
            })
        
        return optimized_code, optimizations
    
    def _count_nested_loops(self, node: ast.AST) -> int:
        """Count the depth of nested loops."""
        depth = 0
        for child in ast.walk(node):
            if isinstance(child, (ast.For, ast.While)) and child != node:
                depth = max(depth, 1 + self._count_nested_loops(child))
        return depth
    
    def _calculate_nesting_depth(self, node: ast.AST, current_depth: int = 0) -> int:
        """Calculate the maximum nesting depth of a node."""
        max_depth = current_depth
        
        for child in ast.iter_child_nodes(node):
            if isinstance(child, (ast.For, ast.While, ast.If, ast.With, ast.Try)):
                child_depth = self._calculate_nesting_depth(child, current_depth + 1)
                max_depth = max(max_depth, child_depth)
        
        return max_depth
