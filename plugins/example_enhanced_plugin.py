"""
Example Enhanced Plugin demonstrating hot reload and function exposure capabilities.

This plugin showcases the new features of the enhanced plugin system:
- Hot reload support with state persistence
- Function exposure with decorators
- Model integration capabilities
- Comprehensive error handling
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional
from pathlib import Path

from agent_framework.plugins.integrated_plugin import IntegratedPlugin
from agent_framework.plugins.decorators import (
    exposed_function, parameter, returns, example, 
    string_param, int_param, float_param, bool_param, array_param
)
from agent_framework.plugins.function_registry import ParameterType
from agent_framework.core.types import PluginRequest, PluginResponse, PluginCapability


class ExampleEnhancedPlugin(IntegratedPlugin):
    """
    Example plugin demonstrating enhanced capabilities.
    
    Provides text processing, mathematical operations, and data manipulation
    functions with full hot reload and model integration support.
    """
    
    PLUGIN_NAME = "example_enhanced"
    PLUGIN_VERSION = "2.0.0"
    PLUGIN_DESCRIPTION = "Enhanced example plugin with hot reload and function exposure"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []
    
    def __init__(self):
        """Initialize the example enhanced plugin."""
        super().__init__()
        
        # Plugin state
        self._operation_count = 0
        self._state_file = Path("plugin_state.json")
        self._cached_data: Dict[str, Any] = {}
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    # Plugin lifecycle methods
    
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        self.logger.info("Initializing example enhanced plugin")
        
        # Load any persistent state
        await self._load_state()
        
        # Initialize plugin-specific resources
        self._cached_data = {}
        
        self.logger.info("Example enhanced plugin initialized")
    
    async def _cleanup_plugin(self) -> None:
        """Clean up plugin resources."""
        self.logger.info("Cleaning up example enhanced plugin")
        
        # Save state before cleanup
        await self._save_state()
        
        # Clear cached data
        self._cached_data.clear()
        
        self.logger.info("Example enhanced plugin cleaned up")
    
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute a capability request."""
        # This plugin primarily uses exposed functions
        # but can still handle traditional capability requests
        
        if request.capability == "legacy_operation":
            return PluginResponse(
                success=True,
                result=f"Legacy operation executed with parameters: {request.parameters}",
                metadata={"plugin_name": self.name, "type": "legacy"}
            )
        
        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )
    
    # Hot reload state management
    
    async def _save_reload_state(self) -> None:
        """Save state before reload."""
        await self._save_state()
    
    async def _restore_reload_state(self) -> None:
        """Restore state after reload."""
        await self._load_state()
    
    async def _save_state(self) -> None:
        """Save plugin state to file."""
        try:
            state = {
                "operation_count": self._operation_count,
                "cached_data": self._cached_data,
                "last_save": time.time()
            }
            
            with open(self._state_file, 'w') as f:
                json.dump(state, f, indent=2)
                
            self.logger.debug("Plugin state saved")
            
        except Exception as e:
            self.logger.error(f"Failed to save plugin state: {e}")
    
    async def _load_state(self) -> None:
        """Load plugin state from file."""
        try:
            if self._state_file.exists():
                with open(self._state_file, 'r') as f:
                    state = json.load(f)
                
                self._operation_count = state.get("operation_count", 0)
                self._cached_data = state.get("cached_data", {})
                
                self.logger.debug("Plugin state loaded")
            
        except Exception as e:
            self.logger.error(f"Failed to load plugin state: {e}")
    
    # Exposed functions for AI model integration
    
    @exposed_function(
        name="process_text",
        description="Process text with various operations like uppercase, lowercase, reverse, etc.",
        tags=["text", "processing", "utility"],
        category="text_processing"
    )
    @string_param("text", "The text to process", required=True)
    @string_param("operation", "Operation to perform: uppercase, lowercase, reverse, word_count", required=True)
    @bool_param("strip_whitespace", "Whether to strip whitespace", required=False, default=True)
    @returns(ParameterType.OBJECT, "Processed text result with metadata")
    @example(
        description="Convert text to uppercase",
        input={"text": "hello world", "operation": "uppercase"},
        output={"result": "HELLO WORLD", "original_length": 11, "processed_length": 11}
    )
    @example(
        description="Count words in text",
        input={"text": "hello world example", "operation": "word_count"},
        output={"result": 3, "words": ["hello", "world", "example"]}
    )
    async def process_text(self, text: str, operation: str, strip_whitespace: bool = True) -> Dict[str, Any]:
        """Process text with the specified operation."""
        self._operation_count += 1
        
        if strip_whitespace:
            text = text.strip()
        
        original_length = len(text)
        
        if operation == "uppercase":
            result = text.upper()
            return {
                "result": result,
                "original_length": original_length,
                "processed_length": len(result),
                "operation": operation
            }
        
        elif operation == "lowercase":
            result = text.lower()
            return {
                "result": result,
                "original_length": original_length,
                "processed_length": len(result),
                "operation": operation
            }
        
        elif operation == "reverse":
            result = text[::-1]
            return {
                "result": result,
                "original_length": original_length,
                "processed_length": len(result),
                "operation": operation
            }
        
        elif operation == "word_count":
            words = text.split()
            return {
                "result": len(words),
                "words": words,
                "original_length": original_length,
                "operation": operation
            }
        
        else:
            raise ValueError(f"Unknown operation: {operation}")
    
    @exposed_function(
        name="calculate",
        description="Perform mathematical calculations with support for basic operations",
        tags=["math", "calculation", "utility"],
        category="mathematics"
    )
    @float_param("a", "First number", required=True)
    @float_param("b", "Second number", required=True)
    @string_param("operation", "Operation: add, subtract, multiply, divide, power", required=True)
    @returns(ParameterType.OBJECT, "Calculation result with metadata")
    @example(
        description="Add two numbers",
        input={"a": 5.5, "b": 3.2, "operation": "add"},
        output={"result": 8.7, "operation": "add", "operands": [5.5, 3.2]}
    )
    async def calculate(self, a: float, b: float, operation: str) -> Dict[str, Any]:
        """Perform mathematical calculation."""
        self._operation_count += 1
        
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                raise ValueError("Division by zero")
            result = a / b
        elif operation == "power":
            result = a ** b
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        return {
            "result": result,
            "operation": operation,
            "operands": [a, b],
            "timestamp": time.time()
        }
    
    @exposed_function(
        name="manage_cache",
        description="Manage plugin cache data with get, set, delete, and list operations",
        tags=["cache", "data", "storage"],
        category="data_management"
    )
    @string_param("action", "Action: get, set, delete, list, clear", required=True)
    @string_param("key", "Cache key", required=False)
    @parameter("value", ParameterType.ANY, "Value to store", required=False)
    @returns(ParameterType.OBJECT, "Cache operation result")
    async def manage_cache(self, action: str, key: Optional[str] = None, value: Any = None) -> Dict[str, Any]:
        """Manage plugin cache data."""
        self._operation_count += 1
        
        if action == "get":
            if not key:
                raise ValueError("Key required for get operation")
            return {
                "action": "get",
                "key": key,
                "value": self._cached_data.get(key),
                "exists": key in self._cached_data
            }
        
        elif action == "set":
            if not key:
                raise ValueError("Key required for set operation")
            if value is None:
                raise ValueError("Value required for set operation")
            
            self._cached_data[key] = value
            return {
                "action": "set",
                "key": key,
                "value": value,
                "success": True
            }
        
        elif action == "delete":
            if not key:
                raise ValueError("Key required for delete operation")
            
            existed = key in self._cached_data
            if existed:
                del self._cached_data[key]
            
            return {
                "action": "delete",
                "key": key,
                "existed": existed,
                "success": True
            }
        
        elif action == "list":
            return {
                "action": "list",
                "keys": list(self._cached_data.keys()),
                "count": len(self._cached_data)
            }
        
        elif action == "clear":
            count = len(self._cached_data)
            self._cached_data.clear()
            return {
                "action": "clear",
                "cleared_count": count,
                "success": True
            }
        
        else:
            raise ValueError(f"Unknown action: {action}")
    
    @exposed_function(
        name="get_plugin_stats",
        description="Get plugin statistics and status information",
        tags=["stats", "monitoring", "info"],
        category="monitoring"
    )
    @returns(ParameterType.OBJECT, "Plugin statistics and status")
    async def get_plugin_stats(self) -> Dict[str, Any]:
        """Get plugin statistics."""
        return {
            "plugin_name": self.name,
            "plugin_version": self.version,
            "operation_count": self._operation_count,
            "reload_count": self.reload_count,
            "last_reload_time": self._last_reload_time,
            "cached_items": len(self._cached_data),
            "is_initialized": self._is_initialized,
            "exposed_functions": list(self._exposed_functions.keys()),
            "uptime": time.time() - (self._last_reload_time or 0) if self._last_reload_time else None
        }
