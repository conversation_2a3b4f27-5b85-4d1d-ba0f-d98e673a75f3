"""
API Integration Plugin - Advanced example demonstrating HTTP API operations.

This plugin provides comprehensive HTTP API integration capabilities with
authentication, rate limiting, caching, and error handling.
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse
import hashlib

try:
    import aiohttp
    import aiofiles
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    aiohttp = None

from agent_framework.plugins.integrated_plugin import IntegratedPlugin
from agent_framework.plugins.decorators import (
    exposed_function, parameter, returns, example, 
    string_param, int_param, bool_param, object_param,
    validate_input, cached, rate_limited
)
from agent_framework.plugins.function_registry import ParameterType
from agent_framework.core.types import PluginRequest, PluginResponse


class APIIntegrationPlugin(IntegratedPlugin):
    """
    Advanced API integration plugin with authentication and caching.
    
    Provides HTTP API operations with rate limiting, authentication,
    response caching, and comprehensive error handling.
    """
    
    PLUGIN_NAME = "api_integration"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "HTTP API integration with authentication and caching"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    
    def __init__(self):
        """Initialize the API integration plugin."""
        super().__init__()
        
        # HTTP session and configuration
        self._session: Optional[aiohttp.ClientSession] = None
        self._base_urls: Dict[str, str] = {}
        self._auth_tokens: Dict[str, str] = {}
        self._rate_limits: Dict[str, Dict[str, Any]] = {}
        
        # Response cache
        self._response_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_ttl = 300  # 5 minutes default
        
        # Request history
        self._request_history: List[Dict[str, Any]] = []
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        if not AIOHTTP_AVAILABLE:
            raise ImportError("aiohttp is required for API integration plugin")
        
        self.logger.info("Initializing API integration plugin")
        
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(total=30)
        self._session = aiohttp.ClientSession(timeout=timeout)
        
        # Load configuration
        self._base_urls = config.get('base_urls', {})
        self._auth_tokens = config.get('auth_tokens', {})
        self._cache_ttl = config.get('cache_ttl', 300)
        
        # Initialize rate limits
        for service, limits in config.get('rate_limits', {}).items():
            self._rate_limits[service] = {
                'requests_per_minute': limits.get('requests_per_minute', 60),
                'requests': [],
                'last_reset': time.time()
            }
        
        self.logger.info("API integration plugin initialized")
    
    async def _cleanup_plugin(self) -> None:
        """Clean up plugin resources."""
        self.logger.info("Cleaning up API integration plugin")
        
        # Close HTTP session
        if self._session:
            await self._session.close()
            self._session = None
        
        # Clear caches
        self._response_cache.clear()
        
        self.logger.info("API integration plugin cleaned up")
    
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute traditional capability requests."""
        if request.capability == "health_check":
            return PluginResponse(
                success=True,
                result={"status": "healthy", "session_active": self._session is not None},
                metadata={"plugin_name": self.name, "capability": "health_check"}
            )
        
        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )
    
    # HTTP request methods
    
    @exposed_function(
        name="http_get",
        description="Perform HTTP GET request with caching and authentication",
        tags=["http", "api", "get"],
        category="api_operations"
    )
    @string_param("url", "URL to request", required=True)
    @object_param("headers", "Additional headers", required=False, default={})
    @object_param("params", "Query parameters", required=False, default={})
    @string_param("auth_service", "Authentication service name", required=False)
    @bool_param("use_cache", "Whether to use response cache", required=False, default=True)
    @int_param("timeout", "Request timeout in seconds", required=False, default=30)
    @returns(ParameterType.OBJECT, "HTTP response data")
    @example(
        description="Get data from API",
        input={"url": "https://api.example.com/data", "use_cache": True},
        output={"status": 200, "data": {"result": "success"}, "cached": False}
    )
    @rate_limited(calls_per_minute=120)
    async def http_get(self, url: str, headers: Dict[str, str] = None, 
                      params: Dict[str, Any] = None, auth_service: str = None,
                      use_cache: bool = True, timeout: int = 30) -> Dict[str, Any]:
        """Perform HTTP GET request with caching."""
        try:
            # Check rate limits
            await self._check_rate_limit(url)
            
            # Generate cache key
            cache_key = self._generate_cache_key("GET", url, params, headers)
            
            # Check cache
            if use_cache and cache_key in self._response_cache:
                cached_response = self._response_cache[cache_key]
                if time.time() - cached_response['timestamp'] < self._cache_ttl:
                    await self._record_request("GET", url, 200, True)
                    return {
                        **cached_response['data'],
                        'cached': True,
                        'cache_age': time.time() - cached_response['timestamp']
                    }
            
            # Prepare headers
            request_headers = headers or {}
            if auth_service and auth_service in self._auth_tokens:
                request_headers['Authorization'] = f"Bearer {self._auth_tokens[auth_service]}"
            
            # Make request
            start_time = time.time()
            async with self._session.get(
                url, 
                headers=request_headers, 
                params=params,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                response_time = time.time() - start_time
                
                # Read response
                if response.content_type.startswith('application/json'):
                    data = await response.json()
                else:
                    data = await response.text()
                
                result = {
                    'status': response.status,
                    'data': data,
                    'headers': dict(response.headers),
                    'response_time': response_time,
                    'cached': False,
                    'url': str(response.url)
                }
                
                # Cache successful responses
                if use_cache and 200 <= response.status < 300:
                    self._response_cache[cache_key] = {
                        'data': result,
                        'timestamp': time.time()
                    }
                
                # Record request
                await self._record_request("GET", url, response.status, False)
                
                return result
                
        except Exception as e:
            await self._record_request("GET", url, 0, False, str(e))
            self.logger.error(f"HTTP GET error for {url}: {e}")
            raise
    
    @exposed_function(
        name="http_post",
        description="Perform HTTP POST request with data and authentication",
        tags=["http", "api", "post"],
        category="api_operations"
    )
    @string_param("url", "URL to request", required=True)
    @parameter("data", ParameterType.ANY, "Data to send in request body", required=False)
    @object_param("headers", "Additional headers", required=False, default={})
    @string_param("auth_service", "Authentication service name", required=False)
    @string_param("content_type", "Content type", required=False, default="application/json")
    @int_param("timeout", "Request timeout in seconds", required=False, default=30)
    @returns(ParameterType.OBJECT, "HTTP response data")
    @example(
        description="Post data to API",
        input={"url": "https://api.example.com/submit", "data": {"name": "test"}},
        output={"status": 201, "data": {"id": 123}, "response_time": 0.5}
    )
    @rate_limited(calls_per_minute=60)
    async def http_post(self, url: str, data: Any = None, headers: Dict[str, str] = None,
                       auth_service: str = None, content_type: str = "application/json",
                       timeout: int = 30) -> Dict[str, Any]:
        """Perform HTTP POST request."""
        try:
            # Check rate limits
            await self._check_rate_limit(url)
            
            # Prepare headers
            request_headers = headers or {}
            request_headers['Content-Type'] = content_type
            
            if auth_service and auth_service in self._auth_tokens:
                request_headers['Authorization'] = f"Bearer {self._auth_tokens[auth_service]}"
            
            # Prepare data
            if content_type == "application/json" and data is not None:
                request_data = json.dumps(data)
            else:
                request_data = data
            
            # Make request
            start_time = time.time()
            async with self._session.post(
                url,
                data=request_data,
                headers=request_headers,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                response_time = time.time() - start_time
                
                # Read response
                if response.content_type.startswith('application/json'):
                    response_data = await response.json()
                else:
                    response_data = await response.text()
                
                result = {
                    'status': response.status,
                    'data': response_data,
                    'headers': dict(response.headers),
                    'response_time': response_time,
                    'url': str(response.url)
                }
                
                # Record request
                await self._record_request("POST", url, response.status, False)
                
                return result
                
        except Exception as e:
            await self._record_request("POST", url, 0, False, str(e))
            self.logger.error(f"HTTP POST error for {url}: {e}")
            raise
    
    @exposed_function(
        name="configure_service",
        description="Configure API service with base URL and authentication",
        tags=["config", "auth", "setup"],
        category="configuration"
    )
    @string_param("service_name", "Name of the service", required=True)
    @string_param("base_url", "Base URL for the service", required=True)
    @string_param("auth_token", "Authentication token", required=False)
    @int_param("rate_limit", "Requests per minute limit", required=False, default=60)
    @returns(ParameterType.OBJECT, "Configuration result")
    async def configure_service(self, service_name: str, base_url: str, 
                              auth_token: str = None, rate_limit: int = 60) -> Dict[str, Any]:
        """Configure API service settings."""
        try:
            # Validate URL
            parsed_url = urlparse(base_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid base URL: {base_url}")
            
            # Store configuration
            self._base_urls[service_name] = base_url
            
            if auth_token:
                self._auth_tokens[service_name] = auth_token
            
            # Set up rate limiting
            self._rate_limits[service_name] = {
                'requests_per_minute': rate_limit,
                'requests': [],
                'last_reset': time.time()
            }
            
            return {
                'service_name': service_name,
                'base_url': base_url,
                'auth_configured': bool(auth_token),
                'rate_limit': rate_limit,
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"Error configuring service {service_name}: {e}")
            raise
    
    @exposed_function(
        name="get_request_stats",
        description="Get statistics about API requests made by this plugin",
        tags=["stats", "monitoring", "analytics"],
        category="monitoring"
    )
    @int_param("limit", "Maximum number of requests to analyze", required=False, default=100)
    @string_param("service_filter", "Filter by service name", required=False)
    @returns(ParameterType.OBJECT, "Request statistics")
    async def get_request_stats(self, limit: int = 100, service_filter: str = None) -> Dict[str, Any]:
        """Get API request statistics."""
        history = self._request_history.copy()
        
        # Filter by service if specified
        if service_filter:
            history = [req for req in history if service_filter in req.get('url', '')]
        
        # Apply limit
        recent_history = history[-limit:] if limit > 0 else history
        
        # Calculate statistics
        total_requests = len(recent_history)
        successful_requests = len([req for req in recent_history if 200 <= req.get('status', 0) < 300])
        cached_requests = len([req for req in recent_history if req.get('cached', False)])
        
        # Calculate average response time
        response_times = [req.get('response_time', 0) for req in recent_history if req.get('response_time')]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # Group by status code
        status_codes = {}
        for req in recent_history:
            status = req.get('status', 0)
            status_codes[status] = status_codes.get(status, 0) + 1
        
        # Group by method
        methods = {}
        for req in recent_history:
            method = req.get('method', 'UNKNOWN')
            methods[method] = methods.get(method, 0) + 1
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'success_rate': successful_requests / total_requests if total_requests > 0 else 0,
            'cached_requests': cached_requests,
            'cache_hit_rate': cached_requests / total_requests if total_requests > 0 else 0,
            'average_response_time': avg_response_time,
            'status_codes': status_codes,
            'methods': methods,
            'configured_services': list(self._base_urls.keys()),
            'cache_size': len(self._response_cache)
        }
    
    @exposed_function(
        name="clear_cache",
        description="Clear the response cache for API requests",
        tags=["cache", "cleanup", "maintenance"],
        category="maintenance"
    )
    @string_param("service_filter", "Clear cache only for specific service", required=False)
    @returns(ParameterType.OBJECT, "Cache clear result")
    async def clear_cache(self, service_filter: str = None) -> Dict[str, Any]:
        """Clear response cache."""
        try:
            initial_size = len(self._response_cache)
            
            if service_filter:
                # Clear cache for specific service
                keys_to_remove = []
                for key in self._response_cache:
                    if service_filter in key:
                        keys_to_remove.append(key)
                
                for key in keys_to_remove:
                    del self._response_cache[key]
                
                cleared_count = len(keys_to_remove)
            else:
                # Clear all cache
                self._response_cache.clear()
                cleared_count = initial_size
            
            return {
                'success': True,
                'initial_cache_size': initial_size,
                'cleared_entries': cleared_count,
                'remaining_entries': len(self._response_cache),
                'service_filter': service_filter
            }
            
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
            raise
    
    # Helper methods
    
    async def _check_rate_limit(self, url: str) -> None:
        """Check and enforce rate limits."""
        # Find matching service
        service_name = None
        for name, base_url in self._base_urls.items():
            if url.startswith(base_url):
                service_name = name
                break
        
        if not service_name or service_name not in self._rate_limits:
            return  # No rate limit configured
        
        rate_limit = self._rate_limits[service_name]
        current_time = time.time()
        
        # Reset counter if a minute has passed
        if current_time - rate_limit['last_reset'] >= 60:
            rate_limit['requests'] = []
            rate_limit['last_reset'] = current_time
        
        # Remove old requests
        rate_limit['requests'] = [
            req_time for req_time in rate_limit['requests']
            if current_time - req_time < 60
        ]
        
        # Check limit
        if len(rate_limit['requests']) >= rate_limit['requests_per_minute']:
            raise Exception(f"Rate limit exceeded for {service_name}: {rate_limit['requests_per_minute']} requests per minute")
        
        # Record this request
        rate_limit['requests'].append(current_time)
    
    def _generate_cache_key(self, method: str, url: str, params: Dict = None, headers: Dict = None) -> str:
        """Generate cache key for request."""
        key_data = {
            'method': method,
            'url': url,
            'params': params or {},
            'headers': {k: v for k, v in (headers or {}).items() if k.lower() not in ['authorization']}
        }
        
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def _record_request(self, method: str, url: str, status: int, cached: bool, error: str = None) -> None:
        """Record request for statistics."""
        request_record = {
            'method': method,
            'url': url,
            'status': status,
            'cached': cached,
            'timestamp': time.time(),
            'error': error
        }
        
        self._request_history.append(request_record)
        
        # Keep only last 1000 requests
        if len(self._request_history) > 1000:
            self._request_history = self._request_history[-1000:]
