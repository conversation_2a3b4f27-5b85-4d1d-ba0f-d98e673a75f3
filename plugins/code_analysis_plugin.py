"""
Default Code Analysis Plugin for the Agent Framework.

Provides comprehensive code analysis capabilities including:
- Complexity analysis
- Quality metrics
- Pattern detection
- Dependency analysis
"""

import ast
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path

from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from agent_framework.utils.code_analysis_utils import analyze_code_quality, calculate_complexity
from agent_framework.utils.validation_utils import validate_python_syntax


class CodeAnalysisPlugin(PluginInterface):
    """Default code analysis plugin implementation."""
    
    PLUGIN_NAME = "code_analysis"
    PLUGIN_VERSION = "1.0.0"
    
    def __init__(self):
        """Initialize the code analysis plugin."""
        self.logger = logging.getLogger(f"{__name__}.{self.PLUGIN_NAME}")
        self._is_initialized = False
        self._capabilities = [
            PluginCapability(
                name="code_analysis",
                description="Analyze code for complexity, quality, and patterns",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to analyze"},
                        "file_path": {"type": "string", "description": "Optional file path"},
                        "analysis_type": {"type": "string", "enum": ["complexity", "quality", "patterns", "all"]}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "complexity_metrics": {"type": "object"},
                        "quality_metrics": {"type": "object"},
                        "patterns": {"type": "array"},
                        "issues": {"type": "array"}
                    }
                }
            ),
            PluginCapability(
                name="complexity",
                description="Calculate cyclomatic complexity",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"complexity": {"type": "number"}}}
            ),
            PluginCapability(
                name="quality",
                description="Analyze code quality metrics",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"quality_score": {"type": "number"}}}
            )
        ]
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True
        self.logger.info(f"Code analysis plugin {self.version} initialized")
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )
        
        try:
            capability = request.capability
            parameters = request.parameters
            
            if capability == "code_analysis":
                result = await self._analyze_code(parameters)
            elif capability == "complexity":
                result = await self._analyze_complexity(parameters)
            elif capability == "quality":
                result = await self._analyze_quality(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )
            
            return PluginResponse(
                success=True,
                result=result
            )
            
        except Exception as e:
            self.logger.error(f"Plugin execution failed: {e}")
            return PluginResponse(
                success=False,
                error=str(e)
            )
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return self._capabilities
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self.logger.info("Code analysis plugin cleaned up")
    
    async def _analyze_code(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive code analysis."""
        code = parameters.get("code", "")
        file_path = parameters.get("file_path", "")
        analysis_type = parameters.get("analysis_type", "all")
        
        if not code:
            raise ValueError("Code parameter is required")
        
        # Validate syntax first
        if not validate_python_syntax(code):
            return {
                "error": "Invalid Python syntax",
                "valid_syntax": False
            }
        
        result = {"valid_syntax": True}
        
        if analysis_type in ["complexity", "all"]:
            result["complexity_metrics"] = await self._get_complexity_metrics(code)
        
        if analysis_type in ["quality", "all"]:
            result["quality_metrics"] = await self._get_quality_metrics(code)
        
        if analysis_type in ["patterns", "all"]:
            result["patterns"] = await self._detect_patterns(code)
        
        if analysis_type in ["all"]:
            result["issues"] = await self._detect_issues(code)
            result["dependencies"] = await self._analyze_dependencies(code)
        
        return result
    
    async def _analyze_complexity(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code complexity."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        complexity = calculate_complexity(code)
        return {"complexity": complexity}
    
    async def _analyze_quality(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code quality."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        quality_metrics = analyze_code_quality(code)
        return {"quality_metrics": quality_metrics}
    
    async def _get_complexity_metrics(self, code: str) -> Dict[str, Any]:
        """Get detailed complexity metrics."""
        try:
            tree = ast.parse(code)
            
            metrics = {
                "cyclomatic_complexity": calculate_complexity(code),
                "lines_of_code": len(code.splitlines()),
                "functions": 0,
                "classes": 0,
                "max_nesting_depth": 0
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    metrics["functions"] += 1
                elif isinstance(node, ast.ClassDef):
                    metrics["classes"] += 1
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating complexity metrics: {e}")
            return {"error": str(e)}
    
    async def _get_quality_metrics(self, code: str) -> Dict[str, Any]:
        """Get code quality metrics."""
        try:
            quality_data = analyze_code_quality(code)
            
            # Add additional quality metrics
            lines = code.splitlines()
            non_empty_lines = [line for line in lines if line.strip()]
            
            quality_data.update({
                "total_lines": len(lines),
                "non_empty_lines": len(non_empty_lines),
                "comment_ratio": self._calculate_comment_ratio(lines),
                "average_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0
            })
            
            return quality_data
            
        except Exception as e:
            self.logger.error(f"Error calculating quality metrics: {e}")
            return {"error": str(e)}
    
    async def _detect_patterns(self, code: str) -> List[Dict[str, Any]]:
        """Detect design patterns and anti-patterns."""
        patterns = []
        
        try:
            tree = ast.parse(code)
            
            # Detect common patterns
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # Singleton pattern detection
                    if self._is_singleton_pattern(node):
                        patterns.append({
                            "type": "design_pattern",
                            "name": "Singleton",
                            "line": node.lineno,
                            "description": "Singleton pattern detected"
                        })
                    
                    # Factory pattern detection
                    if self._is_factory_pattern(node):
                        patterns.append({
                            "type": "design_pattern", 
                            "name": "Factory",
                            "line": node.lineno,
                            "description": "Factory pattern detected"
                        })
            
            # Detect anti-patterns
            anti_patterns = self._detect_anti_patterns(tree)
            patterns.extend(anti_patterns)
            
        except Exception as e:
            self.logger.error(f"Error detecting patterns: {e}")
            patterns.append({
                "type": "error",
                "description": f"Pattern detection failed: {e}"
            })
        
        return patterns
    
    async def _detect_issues(self, code: str) -> List[Dict[str, Any]]:
        """Detect potential code issues."""
        issues = []
        
        try:
            lines = code.splitlines()
            
            for i, line in enumerate(lines, 1):
                # Long lines
                if len(line) > 100:
                    issues.append({
                        "type": "style",
                        "severity": "warning",
                        "line": i,
                        "message": f"Line too long ({len(line)} characters)"
                    })
                
                # TODO comments
                if "TODO" in line or "FIXME" in line:
                    issues.append({
                        "type": "maintenance",
                        "severity": "info",
                        "line": i,
                        "message": "TODO/FIXME comment found"
                    })
            
        except Exception as e:
            self.logger.error(f"Error detecting issues: {e}")
        
        return issues
    
    async def _analyze_dependencies(self, code: str) -> Dict[str, Any]:
        """Analyze code dependencies."""
        dependencies = {
            "imports": [],
            "from_imports": [],
            "builtin_usage": []
        }
        
        try:
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies["imports"].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        dependencies["from_imports"].append(f"{module}.{alias.name}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing dependencies: {e}")
        
        return dependencies
    
    def _calculate_comment_ratio(self, lines: List[str]) -> float:
        """Calculate the ratio of comment lines to total lines."""
        comment_lines = sum(1 for line in lines if line.strip().startswith('#'))
        total_lines = len([line for line in lines if line.strip()])
        return comment_lines / total_lines if total_lines > 0 else 0.0
    
    def _is_singleton_pattern(self, node: ast.ClassDef) -> bool:
        """Check if a class implements singleton pattern."""
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and item.name == "__new__":
                return True
        return False
    
    def _is_factory_pattern(self, node: ast.ClassDef) -> bool:
        """Check if a class implements factory pattern."""
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and "create" in item.name.lower():
                return True
        return False
    
    def _detect_anti_patterns(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Detect anti-patterns in the code."""
        anti_patterns = []
        
        for node in ast.walk(tree):
            # God class detection (too many methods)
            if isinstance(node, ast.ClassDef):
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                if len(methods) > 20:
                    anti_patterns.append({
                        "type": "anti_pattern",
                        "name": "God Class",
                        "line": node.lineno,
                        "description": f"Class has {len(methods)} methods (too many)"
                    })
            
            # Long method detection
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    method_length = node.end_lineno - node.lineno
                    if method_length > 50:
                        anti_patterns.append({
                            "type": "anti_pattern",
                            "name": "Long Method",
                            "line": node.lineno,
                            "description": f"Method is {method_length} lines long"
                        })
        
        return anti_patterns
