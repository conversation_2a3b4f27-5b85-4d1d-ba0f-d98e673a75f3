"""
Default Documentation Plugin for the Agent Framework.

Provides documentation generation capabilities including:
- Docstring generation
- API documentation
- Inline comments
- README generation
"""

import ast
import logging
import re
from typing import Any, Dict, List, Optional

from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability


class DocumentationPlugin(PluginInterface):
    """Default documentation plugin implementation."""
    
    PLUGIN_NAME = "documentation"
    PLUGIN_VERSION = "1.0.0"
    
    def __init__(self):
        """Initialize the documentation plugin."""
        self.logger = logging.getLogger(f"{__name__}.{self.PLUGIN_NAME}")
        self._is_initialized = False
        self._capabilities = [
            PluginCapability(
                name="generate_docstrings",
                description="Generate docstrings for functions and classes",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to document"},
                        "style": {"type": "string", "enum": ["google", "numpy", "sphinx"], "default": "google"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "documented_code": {"type": "string"},
                        "docstrings_added": {"type": "number"}
                    }
                }
            ),
            PluginCapability(
                name="generate_api_docs",
                description="Generate API documentation",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"documentation": {"type": "string"}}}
            ),
            PluginCapability(
                name="generate_comments",
                description="Generate inline comments",
                input_schema={"type": "object", "properties": {"code": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"commented_code": {"type": "string"}}}
            ),
            PluginCapability(
                name="generate_readme",
                description="Generate README documentation",
                input_schema={"type": "object", "properties": {"project_name": {"type": "string"}}},
                output_schema={"type": "object", "properties": {"readme_content": {"type": "string"}}}
            )
        ]
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True
        self.logger.info(f"Documentation plugin {self.version} initialized")
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )
        
        try:
            capability = request.capability
            parameters = request.parameters
            
            if capability == "generate_docstrings":
                result = await self._generate_docstrings(parameters)
            elif capability == "generate_api_docs":
                result = await self._generate_api_docs(parameters)
            elif capability == "generate_comments":
                result = await self._generate_comments(parameters)
            elif capability == "generate_readme":
                result = await self._generate_readme(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )
            
            return PluginResponse(
                success=True,
                data=result
            )
            
        except Exception as e:
            self.logger.error(f"Plugin execution failed: {e}")
            return PluginResponse(
                success=False,
                error=str(e)
            )
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return self._capabilities
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self.logger.info("Documentation plugin cleaned up")
    
    async def _generate_docstrings(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate docstrings for functions and classes."""
        code = parameters.get("code", "")
        style = parameters.get("style", "google")
        
        if not code:
            raise ValueError("Code parameter is required")
        
        try:
            tree = ast.parse(code)
            lines = code.splitlines()
            documented_lines = lines.copy()
            docstrings_added = 0
            
            # Find functions and classes that need docstrings
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                    if not self._has_docstring(node):
                        docstring = self._generate_docstring_for_node(node, style)
                        if docstring:
                            # Insert docstring after the function/class definition
                            insert_line = node.lineno  # 0-based index
                            indent = self._get_indentation(lines[node.lineno - 1]) + "    "
                            docstring_lines = [f'{indent}"""', f'{indent}{docstring}', f'{indent}"""']
                            
                            # Insert in reverse order to maintain line numbers
                            for i, doc_line in enumerate(docstring_lines):
                                documented_lines.insert(insert_line + i, doc_line)
                            
                            docstrings_added += 1
            
            return {
                "documented_code": "\n".join(documented_lines),
                "docstrings_added": docstrings_added,
                "style": style
            }
            
        except Exception as e:
            self.logger.error(f"Error generating docstrings: {e}")
            return {
                "documented_code": code,
                "docstrings_added": 0,
                "error": str(e)
            }
    
    async def _generate_api_docs(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate API documentation."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        try:
            tree = ast.parse(code)
            documentation = []
            
            # Extract module-level docstring
            if (tree.body and isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, ast.Constant)):
                module_doc = tree.body[0].value.value
                documentation.append(f"# Module Documentation\n\n{module_doc}\n")
            
            # Document classes
            classes = [node for node in tree.body if isinstance(node, ast.ClassDef)]
            if classes:
                documentation.append("## Classes\n")
                for cls in classes:
                    doc = self._extract_docstring(cls)
                    documentation.append(f"### {cls.name}\n")
                    if doc:
                        documentation.append(f"{doc}\n")
                    
                    # Document methods
                    methods = [node for node in cls.body if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))]
                    if methods:
                        documentation.append("#### Methods\n")
                        for method in methods:
                            method_doc = self._extract_docstring(method)
                            args = self._get_function_signature(method)
                            documentation.append(f"- **{method.name}**({args})")
                            if method_doc:
                                documentation.append(f"  - {method_doc}")
                            documentation.append("")
            
            # Document functions
            functions = [node for node in tree.body if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))]
            if functions:
                documentation.append("## Functions\n")
                for func in functions:
                    doc = self._extract_docstring(func)
                    args = self._get_function_signature(func)
                    documentation.append(f"### {func.name}({args})\n")
                    if doc:
                        documentation.append(f"{doc}\n")
            
            return {
                "documentation": "\n".join(documentation),
                "sections": ["classes", "functions"] if classes or functions else []
            }
            
        except Exception as e:
            self.logger.error(f"Error generating API docs: {e}")
            return {
                "documentation": f"Error generating documentation: {e}",
                "error": str(e)
            }
    
    async def _generate_comments(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate inline comments for code."""
        code = parameters.get("code", "")
        if not code:
            raise ValueError("Code parameter is required")
        
        lines = code.splitlines()
        commented_lines = []
        comments_added = 0
        
        for i, line in enumerate(lines):
            commented_lines.append(line)
            
            # Add comments for complex patterns
            stripped = line.strip()
            
            # Comment list comprehensions
            if '[' in stripped and 'for' in stripped and 'in' in stripped:
                indent = self._get_indentation(line)
                commented_lines.append(f"{indent}# List comprehension for efficient iteration")
                comments_added += 1
            
            # Comment regex patterns
            elif 're.' in stripped or 'regex' in stripped.lower():
                indent = self._get_indentation(line)
                commented_lines.append(f"{indent}# Regular expression pattern matching")
                comments_added += 1
            
            # Comment exception handling
            elif stripped.startswith('except'):
                indent = self._get_indentation(line)
                commented_lines.append(f"{indent}# Handle specific exception case")
                comments_added += 1
        
        return {
            "commented_code": "\n".join(commented_lines),
            "comments_added": comments_added
        }
    
    async def _generate_readme(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate README documentation."""
        project_name = parameters.get("project_name", "My Project")
        description = parameters.get("description", "A Python project")
        
        readme_template = f"""# {project_name}

{description}

## Installation

```bash
pip install {project_name.lower().replace(' ', '-')}
```

## Usage

```python
import {project_name.lower().replace(' ', '_')}

# Basic usage example
# TODO: Add specific usage examples
```

## Features

- Feature 1
- Feature 2
- Feature 3

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
"""
        
        return {
            "readme_content": readme_template,
            "sections": ["installation", "usage", "features", "contributing", "license"]
        }
    
    def _has_docstring(self, node: ast.AST) -> bool:
        """Check if a function or class has a docstring."""
        if not hasattr(node, 'body') or not node.body:
            return False
        
        first_stmt = node.body[0]
        return (isinstance(first_stmt, ast.Expr) and 
                isinstance(first_stmt.value, ast.Constant) and
                isinstance(first_stmt.value.value, str))
    
    def _extract_docstring(self, node: ast.AST) -> Optional[str]:
        """Extract docstring from a function or class."""
        if self._has_docstring(node):
            return node.body[0].value.value
        return None
    
    def _generate_docstring_for_node(self, node: ast.AST, style: str) -> str:
        """Generate a docstring for a function or class."""
        if isinstance(node, ast.ClassDef):
            return self._generate_class_docstring(node, style)
        elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            return self._generate_function_docstring(node, style)
        return ""
    
    def _generate_class_docstring(self, node: ast.ClassDef, style: str) -> str:
        """Generate docstring for a class."""
        if style == "google":
            return f"{node.name} class.\n\nProvides functionality for {node.name.lower()}."
        elif style == "numpy":
            return f"{node.name}\n{'-' * len(node.name)}\n\nA class for {node.name.lower()}."
        else:  # sphinx
            return f"{node.name} class.\n\n:param: Class parameters\n:type: Parameter types"
    
    def _generate_function_docstring(self, node: ast.FunctionDef, style: str) -> str:
        """Generate docstring for a function."""
        args = [arg.arg for arg in node.args.args if arg.arg != 'self']
        
        if style == "google":
            docstring = f"{node.name.replace('_', ' ').title()}.\n\n"
            if args:
                docstring += "Args:\n"
                for arg in args:
                    docstring += f"    {arg}: Description of {arg}\n"
            docstring += "\nReturns:\n    Return value description"
            return docstring
        
        elif style == "numpy":
            docstring = f"{node.name.replace('_', ' ').title()}.\n\n"
            if args:
                docstring += "Parameters\n----------\n"
                for arg in args:
                    docstring += f"{arg} : type\n    Description of {arg}\n"
            docstring += "\nReturns\n-------\ntype\n    Return value description"
            return docstring
        
        else:  # sphinx
            docstring = f"{node.name.replace('_', ' ').title()}.\n\n"
            for arg in args:
                docstring += f":param {arg}: Description of {arg}\n:type {arg}: type\n"
            docstring += ":returns: Return value description\n:rtype: type"
            return docstring
    
    def _get_function_signature(self, node: ast.FunctionDef) -> str:
        """Get function signature as string."""
        args = []
        for arg in node.args.args:
            if arg.arg != 'self':
                args.append(arg.arg)
        return ", ".join(args)
    
    def _get_indentation(self, line: str) -> str:
        """Get the indentation of a line."""
        return line[:len(line) - len(line.lstrip())]
