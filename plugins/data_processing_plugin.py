"""
Data Processing Plugin - Advanced example demonstrating data analysis operations.

This plugin provides comprehensive data processing capabilities including
statistical analysis, data transformation, and machine learning operations.
"""

import asyncio
import json
import logging
import statistics
import time
from typing import Any, Dict, List, Optional, Union, Tuple
import math
import re
from collections import Counter, defaultdict

try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
    np = None

from agent_framework.plugins.integrated_plugin import IntegratedPlugin
from agent_framework.plugins.decorators import (
    exposed_function, parameter, returns, example, 
    string_param, int_param, float_param, bool_param, array_param, object_param,
    validate_input, cached, rate_limited
)
from agent_framework.plugins.function_registry import ParameterType
from agent_framework.core.types import PluginRequest, PluginResponse


class DataProcessingPlugin(IntegratedPlugin):
    """
    Advanced data processing plugin with statistical analysis and ML operations.
    
    Provides data transformation, statistical analysis, and basic machine
    learning operations with comprehensive error handling and validation.
    """
    
    PLUGIN_NAME = "data_processing"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Data processing with statistical analysis and ML operations"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    
    def __init__(self):
        """Initialize the data processing plugin."""
        super().__init__()
        
        # Data storage
        self._datasets: Dict[str, Dict[str, Any]] = {}
        self._processing_history: List[Dict[str, Any]] = []
        
        # Configuration
        self._max_dataset_size = 10000  # Maximum rows per dataset
        self._max_memory_usage = 100 * 1024 * 1024  # 100MB
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        self.logger.info("Initializing data processing plugin")
        
        # Load configuration
        self._max_dataset_size = config.get('max_dataset_size', 10000)
        self._max_memory_usage = config.get('max_memory_usage', 100 * 1024 * 1024)
        
        # Check optional dependencies
        if not PANDAS_AVAILABLE:
            self.logger.warning("Pandas not available - some advanced features will be limited")
        
        self.logger.info("Data processing plugin initialized")
    
    async def _cleanup_plugin(self) -> None:
        """Clean up plugin resources."""
        self.logger.info("Cleaning up data processing plugin")
        
        # Clear datasets
        self._datasets.clear()
        
        self.logger.info("Data processing plugin cleaned up")
    
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute traditional capability requests."""
        if request.capability == "list_datasets":
            return PluginResponse(
                success=True,
                result=list(self._datasets.keys()),
                metadata={"plugin_name": self.name, "capability": "list_datasets"}
            )
        
        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )
    
    # Dataset management
    
    @exposed_function(
        name="create_dataset",
        description="Create a new dataset from provided data",
        tags=["data", "dataset", "create"],
        category="data_management"
    )
    @string_param("dataset_name", "Name for the dataset", required=True)
    @array_param("data", "Array of data records", required=True)
    @array_param("columns", "Column names", required=False, default=[])
    @bool_param("overwrite", "Whether to overwrite existing dataset", required=False, default=False)
    @returns(ParameterType.OBJECT, "Dataset creation result")
    @example(
        description="Create a dataset from array data",
        input={"dataset_name": "sales", "data": [[1, 100], [2, 150]], "columns": ["id", "amount"]},
        output={"success": True, "rows": 2, "columns": 2, "dataset_name": "sales"}
    )
    async def create_dataset(self, dataset_name: str, data: List[Any], 
                           columns: List[str] = None, overwrite: bool = False) -> Dict[str, Any]:
        """Create a new dataset from data."""
        try:
            # Check if dataset exists
            if dataset_name in self._datasets and not overwrite:
                raise ValueError(f"Dataset '{dataset_name}' already exists. Use overwrite=True to replace.")
            
            # Validate data size
            if len(data) > self._max_dataset_size:
                raise ValueError(f"Dataset too large: {len(data)} rows (max: {self._max_dataset_size})")
            
            # Process data
            if not data:
                processed_data = []
                column_names = columns or []
            else:
                # Determine if data is list of lists or list of dicts
                if isinstance(data[0], dict):
                    # Data is list of dictionaries
                    column_names = columns or list(data[0].keys())
                    processed_data = data
                elif isinstance(data[0], (list, tuple)):
                    # Data is list of lists/tuples
                    if not columns:
                        column_names = [f"col_{i}" for i in range(len(data[0]))]
                    else:
                        column_names = columns
                    
                    processed_data = [
                        {column_names[i]: row[i] for i in range(min(len(row), len(column_names)))}
                        for row in data
                    ]
                else:
                    # Data is list of scalars
                    column_names = columns or ["value"]
                    processed_data = [{column_names[0]: value} for value in data]
            
            # Store dataset
            dataset_info = {
                'data': processed_data,
                'columns': column_names,
                'created_at': time.time(),
                'row_count': len(processed_data),
                'column_count': len(column_names)
            }
            
            self._datasets[dataset_name] = dataset_info
            
            # Record operation
            await self._record_operation("create_dataset", {
                "dataset_name": dataset_name,
                "rows": len(processed_data),
                "columns": len(column_names)
            })
            
            return {
                'success': True,
                'dataset_name': dataset_name,
                'rows': len(processed_data),
                'columns': len(column_names),
                'column_names': column_names
            }
            
        except Exception as e:
            self.logger.error(f"Error creating dataset {dataset_name}: {e}")
            raise
    
    @exposed_function(
        name="analyze_dataset",
        description="Perform statistical analysis on a dataset",
        tags=["analysis", "statistics", "data"],
        category="data_analysis"
    )
    @string_param("dataset_name", "Name of the dataset to analyze", required=True)
    @array_param("columns", "Specific columns to analyze", required=False, default=[])
    @bool_param("include_correlations", "Whether to include correlation analysis", required=False, default=True)
    @returns(ParameterType.OBJECT, "Statistical analysis results")
    @example(
        description="Analyze dataset statistics",
        input={"dataset_name": "sales", "include_correlations": True},
        output={"summary": {"rows": 100, "columns": 3}, "statistics": {"mean": 150.5}}
    )
    @cached(ttl=300)  # Cache for 5 minutes
    async def analyze_dataset(self, dataset_name: str, columns: List[str] = None, 
                            include_correlations: bool = True) -> Dict[str, Any]:
        """Perform statistical analysis on dataset."""
        try:
            # Get dataset
            if dataset_name not in self._datasets:
                raise ValueError(f"Dataset '{dataset_name}' not found")
            
            dataset = self._datasets[dataset_name]
            data = dataset['data']
            
            if not data:
                return {'summary': {'rows': 0, 'columns': 0}, 'statistics': {}}
            
            # Determine columns to analyze
            analyze_columns = columns or dataset['columns']
            
            # Extract numeric data for analysis
            numeric_data = {}
            for col in analyze_columns:
                values = []
                for row in data:
                    value = row.get(col)
                    if isinstance(value, (int, float)) and not math.isnan(value):
                        values.append(value)
                
                if values:
                    numeric_data[col] = values
            
            # Calculate statistics
            statistics_result = {}
            for col, values in numeric_data.items():
                if len(values) > 0:
                    col_stats = {
                        'count': len(values),
                        'mean': statistics.mean(values),
                        'median': statistics.median(values),
                        'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                        'min': min(values),
                        'max': max(values),
                        'sum': sum(values)
                    }
                    
                    # Add percentiles
                    sorted_values = sorted(values)
                    n = len(sorted_values)
                    col_stats['percentiles'] = {
                        '25th': sorted_values[int(n * 0.25)],
                        '75th': sorted_values[int(n * 0.75)],
                        '90th': sorted_values[int(n * 0.90)]
                    }
                    
                    statistics_result[col] = col_stats
            
            # Calculate correlations if requested
            correlations = {}
            if include_correlations and len(numeric_data) > 1:
                column_pairs = [(col1, col2) for i, col1 in enumerate(numeric_data.keys()) 
                               for col2 in list(numeric_data.keys())[i+1:]]
                
                for col1, col2 in column_pairs:
                    values1 = numeric_data[col1]
                    values2 = numeric_data[col2]
                    
                    # Align data (only use rows where both columns have values)
                    aligned_data = []
                    for row in data:
                        val1 = row.get(col1)
                        val2 = row.get(col2)
                        if (isinstance(val1, (int, float)) and not math.isnan(val1) and
                            isinstance(val2, (int, float)) and not math.isnan(val2)):
                            aligned_data.append((val1, val2))
                    
                    if len(aligned_data) > 1:
                        correlation = self._calculate_correlation(aligned_data)
                        correlations[f"{col1}_vs_{col2}"] = correlation
            
            # Record operation
            await self._record_operation("analyze_dataset", {
                "dataset_name": dataset_name,
                "columns_analyzed": len(statistics_result),
                "correlations_calculated": len(correlations)
            })
            
            return {
                'summary': {
                    'dataset_name': dataset_name,
                    'rows': len(data),
                    'columns': len(dataset['columns']),
                    'numeric_columns': len(numeric_data),
                    'analyzed_columns': list(statistics_result.keys())
                },
                'statistics': statistics_result,
                'correlations': correlations if include_correlations else {}
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing dataset {dataset_name}: {e}")
            raise
    
    @exposed_function(
        name="filter_dataset",
        description="Filter dataset rows based on conditions",
        tags=["filter", "query", "data"],
        category="data_transformation"
    )
    @string_param("dataset_name", "Name of the dataset to filter", required=True)
    @array_param("conditions", "Filter conditions", required=True)
    @string_param("result_dataset", "Name for filtered dataset", required=False)
    @returns(ParameterType.OBJECT, "Filter operation result")
    @example(
        description="Filter dataset by conditions",
        input={"dataset_name": "sales", "conditions": [{"column": "amount", "operator": ">", "value": 100}]},
        output={"success": True, "original_rows": 1000, "filtered_rows": 750}
    )
    async def filter_dataset(self, dataset_name: str, conditions: List[Dict[str, Any]], 
                           result_dataset: str = None) -> Dict[str, Any]:
        """Filter dataset based on conditions."""
        try:
            # Get dataset
            if dataset_name not in self._datasets:
                raise ValueError(f"Dataset '{dataset_name}' not found")
            
            dataset = self._datasets[dataset_name]
            data = dataset['data']
            
            # Apply filters
            filtered_data = []
            for row in data:
                if self._evaluate_conditions(row, conditions):
                    filtered_data.append(row)
            
            # Store result if requested
            if result_dataset:
                filtered_dataset_info = {
                    'data': filtered_data,
                    'columns': dataset['columns'],
                    'created_at': time.time(),
                    'row_count': len(filtered_data),
                    'column_count': len(dataset['columns']),
                    'source_dataset': dataset_name,
                    'filter_conditions': conditions
                }
                
                self._datasets[result_dataset] = filtered_dataset_info
            
            # Record operation
            await self._record_operation("filter_dataset", {
                "dataset_name": dataset_name,
                "original_rows": len(data),
                "filtered_rows": len(filtered_data),
                "conditions_count": len(conditions)
            })
            
            return {
                'success': True,
                'dataset_name': dataset_name,
                'original_rows': len(data),
                'filtered_rows': len(filtered_data),
                'filter_ratio': len(filtered_data) / len(data) if data else 0,
                'result_dataset': result_dataset,
                'conditions_applied': len(conditions)
            }
            
        except Exception as e:
            self.logger.error(f"Error filtering dataset {dataset_name}: {e}")
            raise
    
    @exposed_function(
        name="aggregate_data",
        description="Perform aggregation operations on dataset",
        tags=["aggregate", "groupby", "summary"],
        category="data_analysis"
    )
    @string_param("dataset_name", "Name of the dataset to aggregate", required=True)
    @array_param("group_by", "Columns to group by", required=False, default=[])
    @object_param("aggregations", "Aggregation operations", required=True)
    @returns(ParameterType.OBJECT, "Aggregation results")
    @example(
        description="Aggregate sales data by category",
        input={"dataset_name": "sales", "group_by": ["category"], "aggregations": {"amount": ["sum", "avg"]}},
        output={"success": True, "groups": 3, "results": [{"category": "A", "amount_sum": 1000}]}
    )
    async def aggregate_data(self, dataset_name: str, group_by: List[str] = None, 
                           aggregations: Dict[str, List[str]] = None) -> Dict[str, Any]:
        """Perform data aggregation operations."""
        try:
            # Get dataset
            if dataset_name not in self._datasets:
                raise ValueError(f"Dataset '{dataset_name}' not found")
            
            dataset = self._datasets[dataset_name]
            data = dataset['data']
            
            group_by = group_by or []
            aggregations = aggregations or {}
            
            if not data:
                return {'success': True, 'groups': 0, 'results': []}
            
            # Group data
            if group_by:
                groups = defaultdict(list)
                for row in data:
                    key = tuple(row.get(col, None) for col in group_by)
                    groups[key].append(row)
            else:
                groups = {(): data}  # Single group with all data
            
            # Perform aggregations
            results = []
            for group_key, group_data in groups.items():
                result_row = {}
                
                # Add group by columns
                for i, col in enumerate(group_by):
                    result_row[col] = group_key[i] if i < len(group_key) else None
                
                # Perform aggregations
                for column, operations in aggregations.items():
                    # Extract numeric values for this column
                    values = []
                    for row in group_data:
                        value = row.get(column)
                        if isinstance(value, (int, float)) and not math.isnan(value):
                            values.append(value)
                    
                    # Apply operations
                    for operation in operations:
                        result_key = f"{column}_{operation}"
                        
                        if not values:
                            result_row[result_key] = None
                        elif operation == "sum":
                            result_row[result_key] = sum(values)
                        elif operation == "avg" or operation == "mean":
                            result_row[result_key] = statistics.mean(values)
                        elif operation == "count":
                            result_row[result_key] = len(values)
                        elif operation == "min":
                            result_row[result_key] = min(values)
                        elif operation == "max":
                            result_row[result_key] = max(values)
                        elif operation == "std":
                            result_row[result_key] = statistics.stdev(values) if len(values) > 1 else 0
                        else:
                            result_row[result_key] = None
                
                results.append(result_row)
            
            # Record operation
            await self._record_operation("aggregate_data", {
                "dataset_name": dataset_name,
                "groups": len(results),
                "group_by_columns": len(group_by),
                "aggregation_columns": len(aggregations)
            })
            
            return {
                'success': True,
                'dataset_name': dataset_name,
                'groups': len(results),
                'group_by': group_by,
                'aggregations': aggregations,
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"Error aggregating dataset {dataset_name}: {e}")
            raise
    
    @exposed_function(
        name="detect_outliers",
        description="Detect outliers in numeric data using statistical methods",
        tags=["outliers", "anomaly", "statistics"],
        category="data_analysis"
    )
    @string_param("dataset_name", "Name of the dataset", required=True)
    @string_param("column", "Column to analyze for outliers", required=True)
    @string_param("method", "Detection method: iqr, zscore, modified_zscore", required=False, default="iqr")
    @float_param("threshold", "Threshold for outlier detection", required=False, default=1.5)
    @returns(ParameterType.OBJECT, "Outlier detection results")
    @example(
        description="Detect outliers using IQR method",
        input={"dataset_name": "sales", "column": "amount", "method": "iqr"},
        output={"outliers_found": 5, "outlier_indices": [10, 25, 67], "method": "iqr"}
    )
    async def detect_outliers(self, dataset_name: str, column: str, method: str = "iqr", 
                            threshold: float = 1.5) -> Dict[str, Any]:
        """Detect outliers in numeric data."""
        try:
            # Get dataset
            if dataset_name not in self._datasets:
                raise ValueError(f"Dataset '{dataset_name}' not found")
            
            dataset = self._datasets[dataset_name]
            data = dataset['data']
            
            # Extract numeric values
            values = []
            indices = []
            for i, row in enumerate(data):
                value = row.get(column)
                if isinstance(value, (int, float)) and not math.isnan(value):
                    values.append(value)
                    indices.append(i)
            
            if len(values) < 4:
                return {
                    'outliers_found': 0,
                    'outlier_indices': [],
                    'outlier_values': [],
                    'method': method,
                    'threshold': threshold,
                    'total_values': len(values)
                }
            
            outlier_indices = []
            outlier_values = []
            
            if method == "iqr":
                # Interquartile Range method
                sorted_values = sorted(values)
                n = len(sorted_values)
                q1 = sorted_values[int(n * 0.25)]
                q3 = sorted_values[int(n * 0.75)]
                iqr = q3 - q1
                
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr
                
                for i, value in enumerate(values):
                    if value < lower_bound or value > upper_bound:
                        outlier_indices.append(indices[i])
                        outlier_values.append(value)
            
            elif method == "zscore":
                # Z-score method
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                
                if std_val > 0:
                    for i, value in enumerate(values):
                        z_score = abs((value - mean_val) / std_val)
                        if z_score > threshold:
                            outlier_indices.append(indices[i])
                            outlier_values.append(value)
            
            elif method == "modified_zscore":
                # Modified Z-score using median
                median_val = statistics.median(values)
                mad = statistics.median([abs(x - median_val) for x in values])
                
                if mad > 0:
                    for i, value in enumerate(values):
                        modified_z_score = 0.6745 * (value - median_val) / mad
                        if abs(modified_z_score) > threshold:
                            outlier_indices.append(indices[i])
                            outlier_values.append(value)
            
            else:
                raise ValueError(f"Unknown outlier detection method: {method}")
            
            # Record operation
            await self._record_operation("detect_outliers", {
                "dataset_name": dataset_name,
                "column": column,
                "method": method,
                "outliers_found": len(outlier_indices)
            })
            
            return {
                'outliers_found': len(outlier_indices),
                'outlier_indices': outlier_indices,
                'outlier_values': outlier_values,
                'method': method,
                'threshold': threshold,
                'total_values': len(values),
                'outlier_percentage': len(outlier_indices) / len(values) * 100 if values else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting outliers in {dataset_name}.{column}: {e}")
            raise
    
    @exposed_function(
        name="get_processing_history",
        description="Get the history of data processing operations",
        tags=["history", "audit", "monitoring"],
        category="monitoring"
    )
    @int_param("limit", "Maximum number of operations to return", required=False, default=50)
    @string_param("operation_filter", "Filter by operation type", required=False)
    @returns(ParameterType.OBJECT, "Processing history")
    async def get_processing_history(self, limit: int = 50, operation_filter: str = None) -> Dict[str, Any]:
        """Get data processing operation history."""
        history = self._processing_history.copy()
        
        # Filter by operation type if specified
        if operation_filter:
            history = [op for op in history if op.get("operation") == operation_filter]
        
        # Apply limit
        history = history[-limit:] if limit > 0 else history
        
        return {
            'operations': history,
            'total_operations': len(self._processing_history),
            'filtered_count': len(history),
            'operation_types': list(set(op.get("operation") for op in self._processing_history)),
            'datasets': list(self._datasets.keys())
        }
    
    # Helper methods
    
    def _calculate_correlation(self, paired_data: List[Tuple[float, float]]) -> float:
        """Calculate Pearson correlation coefficient."""
        if len(paired_data) < 2:
            return 0.0
        
        x_values = [pair[0] for pair in paired_data]
        y_values = [pair[1] for pair in paired_data]
        
        n = len(paired_data)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in paired_data)
        sum_x2 = sum(x * x for x in x_values)
        sum_y2 = sum(y * y for y in y_values)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    def _evaluate_conditions(self, row: Dict[str, Any], conditions: List[Dict[str, Any]]) -> bool:
        """Evaluate filter conditions for a row."""
        for condition in conditions:
            column = condition.get('column')
            operator = condition.get('operator')
            value = condition.get('value')
            
            if column not in row:
                return False
            
            row_value = row[column]
            
            if operator == "==":
                if row_value != value:
                    return False
            elif operator == "!=":
                if row_value == value:
                    return False
            elif operator == ">":
                if not (isinstance(row_value, (int, float)) and row_value > value):
                    return False
            elif operator == ">=":
                if not (isinstance(row_value, (int, float)) and row_value >= value):
                    return False
            elif operator == "<":
                if not (isinstance(row_value, (int, float)) and row_value < value):
                    return False
            elif operator == "<=":
                if not (isinstance(row_value, (int, float)) and row_value <= value):
                    return False
            elif operator == "contains":
                if not (isinstance(row_value, str) and str(value) in row_value):
                    return False
            elif operator == "startswith":
                if not (isinstance(row_value, str) and row_value.startswith(str(value))):
                    return False
            elif operator == "endswith":
                if not (isinstance(row_value, str) and row_value.endswith(str(value))):
                    return False
            elif operator == "in":
                if row_value not in value:
                    return False
            else:
                return False
        
        return True
    
    async def _record_operation(self, operation: str, details: Dict[str, Any]) -> None:
        """Record processing operation for audit trail."""
        operation_record = {
            "operation": operation,
            "timestamp": time.time(),
            "details": details
        }
        
        self._processing_history.append(operation_record)
        
        # Keep only last 1000 operations
        if len(self._processing_history) > 1000:
            self._processing_history = self._processing_history[-1000:]
