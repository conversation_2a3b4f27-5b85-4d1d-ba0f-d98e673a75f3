#!/usr/bin/env python3
"""
Validation script for the reorganized Agent Framework structure.

This script validates that the reorganization was successful and all
expected files and directories are in their correct locations.
"""

import os
import sys
from pathlib import Path


def check_file_exists(path: str, description: str) -> bool:
    """Check if a file exists and print the result."""
    exists = os.path.exists(path)
    status = "✓" if exists else "✗"
    print(f"{status} {description}: {path}")
    return exists


def check_directory_structure() -> bool:
    """Check the main directory structure."""
    print("🏗️  Checking Directory Structure")
    print("=" * 50)
    
    checks = [
        ("src/", "Source directory"),
        ("src/agent_framework/", "Main package"),
        ("src/agent_framework/core/", "Core modules"),
        ("src/agent_framework/cli/", "CLI modules"),
        ("src/agent_framework/agents/", "Agent modules"),
        ("src/agent_framework/plugins/", "Plugin system"),
        ("tests/", "Test directory"),
        ("docs/", "Documentation"),
        ("examples/", "Examples"),
        ("scripts/", "Utility scripts"),
        (".github/", "GitHub workflows"),
        (".github/workflows/", "CI/CD workflows"),
    ]
    
    all_passed = True
    for path, description in checks:
        if not check_file_exists(path, description):
            all_passed = False
    
    return all_passed


def check_essential_files() -> bool:
    """Check essential project files."""
    print("\n📄 Checking Essential Files")
    print("=" * 50)
    
    checks = [
        ("README.md", "Project README"),
        ("LICENSE", "License file"),
        ("CONTRIBUTING.md", "Contributing guidelines"),
        ("pyproject.toml", "Project configuration"),
        ("requirements.txt", "Runtime dependencies"),
        ("requirements-dev.txt", "Development dependencies"),
        (".gitignore", "Git ignore file"),
        ("Makefile", "Development commands"),
        (".pre-commit-config.yaml", "Pre-commit configuration"),
        ("cli.py", "CLI entry point"),
    ]
    
    all_passed = True
    for path, description in checks:
        if not check_file_exists(path, description):
            all_passed = False
    
    return all_passed


def check_github_files() -> bool:
    """Check GitHub-specific files."""
    print("\n🐙 Checking GitHub Files")
    print("=" * 50)
    
    checks = [
        (".github/workflows/ci.yml", "CI workflow"),
    ]
    
    all_passed = True
    for path, description in checks:
        if not check_file_exists(path, description):
            all_passed = False
    
    return all_passed


def check_moved_files() -> bool:
    """Check that files were moved to correct locations."""
    print("\n📦 Checking Moved Files")
    print("=" * 50)
    
    # Check that files are in scripts/
    script_files = [
        "scripts/apply_diff.py",
        "scripts/debug_cli.py",
        "scripts/user_service.py",
        "scripts/web_search.py",
        "scripts/workbench.py",
        "scripts/core/",
        "scripts/plugins/",
    ]
    
    # Check that test files are in tests/
    test_files = [
        "tests/test_cli.py",
        "tests/test_config.py",
        "tests/test_integration.py",
        "tests/test_enhanced_bug_fix_loop.py",
    ]
    
    # Check that examples are in examples/
    example_files = [
        "examples/basic_usage.py",
        "examples/multi_agent_example.py",
        "examples/simple_autogen_demo.py",  # moved from main.py
    ]
    
    all_passed = True
    
    print("Scripts:")
    for path in script_files:
        if not check_file_exists(path, f"  Script: {os.path.basename(path)}"):
            all_passed = False
    
    print("\nTests:")
    for path in test_files:
        if not check_file_exists(path, f"  Test: {os.path.basename(path)}"):
            all_passed = False
    
    print("\nExamples:")
    for path in example_files:
        if not check_file_exists(path, f"  Example: {os.path.basename(path)}"):
            all_passed = False
    
    return all_passed


def check_package_structure() -> bool:
    """Check the internal package structure."""
    print("\n🐍 Checking Package Structure")
    print("=" * 50)
    
    package_files = [
        "src/agent_framework/__init__.py",
        "src/agent_framework/core/__init__.py",
        "src/agent_framework/core/orchestrator.py",
        "src/agent_framework/core/config.py",
        "src/agent_framework/core/types.py",
        "src/agent_framework/cli/__init__.py",
        "src/agent_framework/cli/core.py",
        "src/agent_framework/agents/__init__.py",
        "src/agent_framework/plugins/__init__.py",
    ]
    
    all_passed = True
    for path in package_files:
        if not check_file_exists(path, f"  Package file: {path}"):
            all_passed = False
    
    return all_passed


def check_cleanup() -> bool:
    """Check that old files were cleaned up."""
    print("\n🧹 Checking Cleanup")
    print("=" * 50)
    
    # Files that should NOT exist in root anymore
    old_files = [
        "agent_framework/",  # Should be in src/ now
        "main.py",  # Should be moved to examples/
        "test_enhanced_bug_fix_loop.py",  # Should be in tests/
        "test_simple_integration.py",  # Should be in tests/
        "apply_diff.py",  # Should be in scripts/
        "core/",  # Should be in scripts/
        "plugins/",  # Should be in scripts/
        "__pycache__/",  # Should be cleaned up
    ]
    
    all_passed = True
    for path in old_files:
        if os.path.exists(path):
            print(f"✗ Old file still exists (should be removed): {path}")
            all_passed = False
        else:
            print(f"✓ Old file properly removed: {path}")
    
    return all_passed


def main():
    """Run all validation checks."""
    print("🔍 Agent Framework Structure Validation")
    print("=" * 60)
    print()
    
    checks = [
        check_directory_structure,
        check_essential_files,
        check_github_files,
        check_moved_files,
        check_package_structure,
        check_cleanup,
    ]
    
    all_passed = True
    for check in checks:
        if not check():
            all_passed = False
        print()
    
    print("📊 Validation Summary")
    print("=" * 50)
    if all_passed:
        print("🎉 All checks passed! The reorganization was successful.")
        print("✅ The project now follows standard GitHub conventions.")
        print()
        print("Next steps:")
        print("1. Install dependencies: make install-dev")
        print("2. Run tests: make test")
        print("3. Check code quality: make lint")
        return 0
    else:
        print("❌ Some checks failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
