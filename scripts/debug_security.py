#!/usr/bin/env python3

import re

def test_security_patterns():
    insecure_code = '''
password = "hardcoded_password"
query = "SELECT * FROM users WHERE id = '%s'" % user_id
result = eval(user_input)
'''
    
    print("Testing security patterns:")
    print(f"Code:\n{insecure_code}")
    
    # Test hardcoded password pattern
    password_pattern = r'password\s*=\s*["\'][^"\']+["\']'
    password_match = re.search(password_pattern, insecure_code, re.IGNORECASE)
    print(f"Password pattern match: {password_match is not None}")
    if password_match:
        print(f"  Matched: {password_match.group()}")
    
    # Test SQL injection patterns
    sql_patterns = [
        r'execute\s*\(\s*["\'].*%.*["\']',
        r'["\'].*%.*["\'].*%',
        r'SELECT.*%.*FROM'
    ]
    
    for i, pattern in enumerate(sql_patterns):
        match = re.search(pattern, insecure_code, re.IGNORECASE)
        print(f"SQL pattern {i+1} match: {match is not None}")
        if match:
            print(f"  Matched: {match.group()}")
    
    # Test eval pattern
    eval_match = 'eval(' in insecure_code
    print(f"Eval pattern match: {eval_match}")

if __name__ == "__main__":
    test_security_patterns()
