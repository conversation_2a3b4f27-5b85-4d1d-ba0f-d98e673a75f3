#!/usr/bin/env python3
"""
Installation script for security enhancements.

This script installs the required dependencies for the security enhancements
and performs basic configuration.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False


def main():
    """Main installation function."""
    print("Installing security enhancement dependencies...")
    
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("Warning: Not running in a virtual environment. Consider using one.")
    
    # Install core security dependencies
    security_deps = [
        "cryptography>=41.0.0",
        "keyring>=24.0.0",
        "pydantic-settings>=2.0.0",
        "slowapi>=0.1.9",
        "limits>=3.6.0"
    ]
    
    # Install monitoring dependencies
    monitoring_deps = [
        "prometheus-client>=0.17.0",
        "opentelemetry-api>=1.20.0",
        "opentelemetry-sdk>=1.20.0",
        "opentelemetry-instrumentation-asyncio>=0.41b0"
    ]
    
    # Install database dependencies
    db_deps = [
        "asyncpg>=0.29.0",
        "aiosqlite>=0.19.0"
    ]
    
    # Install resilience dependencies
    resilience_deps = [
        "circuit-breaker>=1.4.0",
        "tenacity>=8.2.0"
    ]
    
    all_deps = security_deps + monitoring_deps + db_deps + resilience_deps
    
    # Install dependencies
    for dep in all_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            print(f"Failed to install {dep}. Continuing with other dependencies...")
    
    # Update existing dependencies
    update_deps = [
        "anyio>=4.10.0",
        "certifi>=2025.8.3",
        "httpx>=0.28.1"
    ]
    
    for dep in update_deps:
        run_command(f"pip install --upgrade {dep}", f"Updating {dep}")
    
    # Create security configuration directory
    config_dir = Path("config/security")
    config_dir.mkdir(parents=True, exist_ok=True)
    print(f"✓ Created security configuration directory: {config_dir}")
    
    # Create basic security configuration
    security_config = """# Security Configuration
# This file contains security settings for the agent framework

# Input validation settings
max_string_length: 10000
max_list_length: 1000
max_dict_depth: 10

# Rate limiting settings
rate_limit_requests_per_minute: 60
rate_limit_burst_size: 10

# Secrets management settings
secrets_rotation_interval_days: 90
secrets_encryption_enabled: true

# Monitoring settings
security_monitoring_enabled: true
audit_log_retention_days: 365
alert_threshold: 10

# Database connection pooling
db_min_connections: 5
db_max_connections: 20
db_connection_timeout: 30.0
"""
    
    config_file = config_dir / "security.yaml"
    with open(config_file, 'w') as f:
        f.write(security_config)
    print(f"✓ Created security configuration file: {config_file}")
    
    print("\n" + "="*60)
    print("Security enhancements installation completed!")
    print("="*60)
    print("\nNext steps:")
    print("1. Review the security configuration in config/security/security.yaml")
    print("2. Initialize the secrets manager with a master key")
    print("3. Configure monitoring endpoints and alerts")
    print("4. Run the test suite to verify installation")
    print("\nFor more information, see the security documentation.")


if __name__ == "__main__":
    main()
