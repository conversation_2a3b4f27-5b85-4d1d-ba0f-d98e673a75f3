#!/usr/bin/env python3

import sys
import traceback
from agent_framework.cli.core import AgentCLI

def main():
    try:
        cli = AgentCLI()
        parser = cli.create_parser()
        print("Parser created successfully!")
        print(f"Program: {parser.prog}")
        
        # Try to parse help to see if it works
        try:
            parser.parse_args(['--help'])
        except SystemExit:
            print("Help parsing works!")
            
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
