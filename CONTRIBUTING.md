# Contributing to Agent Framework

Thank you for your interest in contributing to the Agent Framework! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Python 3.10 or higher
- Git
- UV package manager (recommended) or pip

### Development Setup

1. **Fork and Clone the Repository**
   ```bash
   git clone https://github.com/yourusername/agent-framework.git
   cd agent-framework
   ```

2. **Set Up Development Environment**
   ```bash
   # Using UV (recommended)
   uv sync --dev
   
   # Or using pip
   pip install -r requirements-dev.txt
   ```

3. **Install Pre-commit Hooks**
   ```bash
   pre-commit install
   ```

4. **Verify Installation**
   ```bash
   # Run tests
   make test
   
   # Check code quality
   make lint
   ```

## 🛠️ Development Workflow

### Making Changes

1. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**
   - Follow the coding standards (see below)
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Your Changes**
   ```bash
   # Run all tests
   make test
   
   # Run specific tests
   pytest tests/test_your_feature.py
   
   # Check coverage
   make coverage
   ```

4. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

### Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(agents): add new code analysis agent
fix(cli): resolve argument parsing issue
docs: update installation instructions
test(core): add tests for orchestrator
```

## 📋 Coding Standards

### Python Code Style

- Follow [PEP 8](https://pep8.org/) style guide
- Use [Black](https://black.readthedocs.io/) for code formatting
- Use [isort](https://isort.readthedocs.io/) for import sorting
- Use [flake8](https://flake8.pycqa.org/) for linting
- Use type hints for all function signatures

### Code Quality

- **Test Coverage**: Maintain >90% test coverage
- **Documentation**: Add docstrings for all public functions/classes
- **Error Handling**: Implement proper error handling and logging
- **Performance**: Consider performance implications of changes

### File Organization

- Place new modules in appropriate packages
- Follow the established directory structure
- Add `__init__.py` files for new packages
- Keep related functionality together

## 🧪 Testing

### Test Structure

```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── e2e/           # End-to-end tests
└── fixtures/      # Test data and fixtures
```

### Writing Tests

- Use `pytest` for all tests
- Follow the AAA pattern (Arrange, Act, Assert)
- Use descriptive test names
- Mock external dependencies
- Test both success and failure cases

### Running Tests

```bash
# All tests
make test

# Specific test file
pytest tests/test_example.py

# With coverage
make coverage

# Performance tests
make test-performance
```

## 📚 Documentation

### Types of Documentation

1. **Code Documentation**: Docstrings and inline comments
2. **API Documentation**: Auto-generated from docstrings
3. **User Documentation**: Guides and tutorials in `docs/`
4. **README**: Project overview and quick start

### Documentation Standards

- Use [Google-style docstrings](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)
- Include examples in docstrings where helpful
- Keep documentation up-to-date with code changes
- Use clear, concise language

## 🐛 Reporting Issues

### Bug Reports

When reporting bugs, please include:

- **Description**: Clear description of the issue
- **Steps to Reproduce**: Detailed steps to reproduce the bug
- **Expected Behavior**: What you expected to happen
- **Actual Behavior**: What actually happened
- **Environment**: Python version, OS, package versions
- **Logs**: Relevant error messages or logs

### Feature Requests

For feature requests, please include:

- **Use Case**: Why this feature would be useful
- **Description**: Detailed description of the proposed feature
- **Examples**: Examples of how the feature would be used
- **Alternatives**: Any alternative solutions you've considered

## 🔍 Code Review Process

### Submitting Pull Requests

1. **Ensure Quality**
   - All tests pass
   - Code follows style guidelines
   - Documentation is updated
   - No merge conflicts

2. **Create Pull Request**
   - Use descriptive title and description
   - Reference related issues
   - Include screenshots/examples if applicable

3. **Review Process**
   - Maintainers will review your PR
   - Address feedback promptly
   - Keep discussions constructive

### Review Criteria

- **Functionality**: Does the code work as intended?
- **Quality**: Is the code well-written and maintainable?
- **Tests**: Are there adequate tests?
- **Documentation**: Is documentation complete and accurate?
- **Performance**: Are there any performance concerns?

## 🏷️ Release Process

### Versioning

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Incompatible API changes
- **MINOR**: New functionality (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

- [ ] Update version numbers
- [ ] Update CHANGELOG.md
- [ ] Run full test suite
- [ ] Update documentation
- [ ] Create release notes
- [ ] Tag release in Git

## 💬 Community

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Discord**: Real-time chat (link in README)

### Code of Conduct

Please note that this project is released with a [Code of Conduct](CODE_OF_CONDUCT.md). By participating in this project you agree to abide by its terms.

## 🙏 Recognition

Contributors will be recognized in:

- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to the Agent Framework! 🚀
