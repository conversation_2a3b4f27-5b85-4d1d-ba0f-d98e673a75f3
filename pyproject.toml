[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "agent-framework"
version = "0.1.0"
description = "A comprehensive, extensible agent framework for programming assistance with multi-agent collaboration and advanced AI capabilities"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Agent Framework Contributors", email = "<EMAIL>"},
]
maintainers = [
    {name = "Agent Framework Contributors", email = "<EMAIL>"},
]
keywords = [
    "ai", "agents", "programming", "code-analysis", "autogen",
    "multi-agent", "mcp", "code-generation", "debugging"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.10"
dependencies = [
    "autogen-agentchat>=0.7.1",
    "autogen-ext[diskcache,docker,mcp,openai]>=0.7.1",
    "colorama>=0.4.6",
    "docker>=7.1.0",
    "mcp-server-fetch>=2025.4.7",
    "patch>=1.16",
    "psutil>=5.9.0",
    "pydantic>=2.0.0",
    "pyyaml>=6.0.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.10.0",
    "pytest-xdist>=3.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
]
test = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.10.0",
    "pytest-xdist>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/agent-framework"
Documentation = "https://agent-framework.readthedocs.io"
Repository = "https://github.com/yourusername/agent-framework.git"
Issues = "https://github.com/yourusername/agent-framework/issues"
Changelog = "https://github.com/yourusername/agent-framework/blob/main/CHANGELOG.md"

[project.scripts]
agent-framework = "src.agent_framework.cli.core:main"

[tool.hatchling.build.targets.wheel]
packages = ["src/agent_framework"]

[tool.hatchling.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/docs",
    "/examples",
    "/README.md",
    "/LICENSE",
    "/CONTRIBUTING.md",
    "/pyproject.toml",
]

# [[tool.uv.index]]
# url = "https://pypi.tuna.tsinghua.edu.cn/simple"
# default = true

# Development tools configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/agent_framework",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/agent_framework"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/examples/*",
    "*/scripts/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["agent_framework"]
known_third_party = ["autogen", "pydantic", "rich", "pytest"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "autogen.*",
    "autogen_agentchat.*",
    "autogen_ext.*",
    "docker.*",
    "psutil.*",
    "pydantic.*",
    "keyring.*",
    "jsonschema.*",
]
ignore_missing_imports = true

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]
