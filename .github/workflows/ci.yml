name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install UV
      uses: astral-sh/setup-uv@v1

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Run linting
      run: |
        uv run flake8 src/ tests/
        uv run black --check src/ tests/
        uv run isort --check-only src/ tests/

    - name: Run type checking
      run: |
        uv run mypy src/agent_framework

    - name: Run tests
      run: |
        uv run pytest --cov=src/agent_framework --cov-report=xml --cov-fail-under=80

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install UV
      uses: astral-sh/setup-uv@v1

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Run security checks
      run: |
        uv run bandit -r src/
        uv run safety check

  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install UV
      uses: astral-sh/setup-uv@v1

    - name: Install build dependencies
      run: |
        uv tool install build

    - name: Build package
      run: |
        python -m build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/
