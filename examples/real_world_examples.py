#!/usr/bin/env python3
"""
Real-World Examples: Advanced AI Agent Framework

This file contains practical, real-world examples of using the advanced AI agent
framework for common development tasks and scenarios.
"""

import asyncio
import tempfile
import os
from pathlib import Path

from agent_framework.core.agent_orchestrator import (
    AdvancedAgentOrchestrator,
    AgentCapabilities
)


async def example_1_legacy_code_modernization():
    """Example 1: Modernizing Legacy Python Code"""
    print("\n" + "="*60)
    print("EXAMPLE 1: Legacy Code Modernization")
    print("="*60)

    # Legacy code from an old Python 2.7 project
    legacy_code = '''
import urllib2
import json

def fetch_user_data(user_id):
    url = "https://api.example.com/users/" + str(user_id)
    response = urllib2.urlopen(url)
    data = response.read()
    user_data = json.loads(data)
    
    if user_data.has_key('email'):
        email = user_data['email']
    else:
        email = None
    
    return {
        'id': user_data['id'],
        'name': user_data['name'],
        'email': email,
        'active': user_data.get('active', True)
    }

def process_users(user_ids):
    results = []
    for uid in user_ids:
        try:
            user = fetch_user_data(uid)
            results.append(user)
        except:
            pass
    return results
'''

    print("📜 Legacy Code (Python 2.7 style):")
    print(legacy_code[:300] + "...")

    # Set up orchestrator for modernization
    capabilities = AgentCapabilities(
        enable_advanced_code_generation=True,
        enable_automatic_evaluation=True,
        enable_comprehensive_testing=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

    # Define modernization requirements
    modernization_requirements = {
        "type": "enhancement",
        "goals": [
            "python3_compatibility",
            "add_type_hints",
            "modern_http_library",
            "async_support",
            "error_handling",
            "logging",
            "documentation"
        ],
        "description": "Modernize legacy Python 2.7 code to Python 3.8+ with async support"
    }

    print("\n🔄 Running modernization process...")
    results = await orchestrator.run_full_advanced_cycle(
        code_content=legacy_code,
        file_path="legacy_user_service.py",
        requirements=modernization_requirements
    )

    if results.get("overall_success"):
        print("✅ Modernization completed successfully!")
        print("📈 Improvements made:")
        print("   - Updated to Python 3.8+ syntax")
        print("   - Added type hints and async support")
        print("   - Replaced urllib2 with modern requests/httpx")
        print("   - Added proper error handling and logging")
        print("   - Generated comprehensive documentation")

    return results


async def example_2_api_endpoint_generation():
    """Example 2: Generating REST API Endpoints"""
    print("\n" + "="*60)
    print("EXAMPLE 2: REST API Endpoint Generation")
    print("="*60)

    capabilities = AgentCapabilities(
        enable_advanced_code_generation=True,
        enable_comprehensive_testing=True,
        enable_automatic_evaluation=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

    # Define API endpoint requirements
    api_requirements = {
        "type": "class",
        "name": "UserController",
        "description": "REST API controller for user management",
        "framework": "FastAPI",
        "endpoints": [
            {
                "method": "GET",
                "path": "/users",
                "description": "List all users with pagination"
            },
            {
                "method": "GET",
                "path": "/users/{user_id}",
                "description": "Get user by ID"
            },
            {
                "method": "POST",
                "path": "/users",
                "description": "Create new user"
            },
            {
                "method": "PUT",
                "path": "/users/{user_id}",
                "description": "Update existing user"
            },
            {
                "method": "DELETE",
                "path": "/users/{user_id}",
                "description": "Delete user"
            }
        ],
        "features": [
            "input_validation",
            "error_handling",
            "authentication",
            "rate_limiting",
            "logging",
            "openapi_documentation",
            "async_support"
        ]
    }

    print("🚀 Generating FastAPI controller with full CRUD operations...")

    implementation_result = await orchestrator.advanced_code_implementation(
        requirements=api_requirements,
        file_path="controllers/user_controller.py"
    )

    if implementation_result["success"]:
        print("✅ API controller generated successfully!")
        print("📋 Features included:")
        for feature in api_requirements["features"]:
            print(f"   - {feature}")

        if implementation_result.get("evaluation"):
            eval_result = implementation_result["evaluation"]
            print(f"📊 Quality Score: {eval_result.overall_score:.2f}/10")

    return implementation_result


async def example_3_data_processing_pipeline():
    """Example 3: Data Processing Pipeline Enhancement"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Data Processing Pipeline Enhancement")
    print("="*60)

    # Existing data processing code that needs improvement
    existing_pipeline = '''
import pandas as pd

def process_sales_data(file_path):
    df = pd.read_csv(file_path)
    
    # Basic cleaning
    df = df.dropna()
    df['date'] = pd.to_datetime(df['date'])
    
    # Calculate metrics
    monthly_sales = df.groupby(df['date'].dt.month)['amount'].sum()
    top_products = df.groupby('product')['amount'].sum().sort_values(ascending=False).head(10)
    
    return monthly_sales, top_products

def generate_report(monthly_sales, top_products):
    print("Monthly Sales:")
    print(monthly_sales)
    print("\\nTop Products:")
    print(top_products)
'''

    print("📊 Existing Data Pipeline:")
    print(existing_pipeline[:200] + "...")

    capabilities = AgentCapabilities(
        enable_advanced_code_generation=True,
        enable_automatic_evaluation=True,
        enable_automatic_bug_fixing=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

    # Define enhancement requirements
    enhancement_requirements = {
        "type": "enhancement",
        "goals": [
            "add_data_validation",
            "error_handling",
            "performance_optimization",
            "memory_efficiency",
            "configurable_parameters",
            "logging",
            "unit_tests",
            "documentation",
            "type_hints"
        ],
        "description": "Enhance data processing pipeline with validation, optimization, and robustness"
    }

    print("\n⚙️  Enhancing data processing pipeline...")

    results = await orchestrator.run_full_advanced_cycle(
        code_content=existing_pipeline,
        file_path="data_pipeline.py",
        requirements=enhancement_requirements
    )

    if results.get("overall_success"):
        print("✅ Pipeline enhancement completed!")
        print("📈 Improvements made:")
        print("   - Added comprehensive data validation")
        print("   - Implemented robust error handling")
        print("   - Optimized memory usage for large datasets")
        print("   - Added configurable processing parameters")
        print("   - Generated unit tests and documentation")

    return results


async def example_4_security_vulnerability_fixing():
    """Example 4: Security Vulnerability Detection and Fixing"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Security Vulnerability Detection and Fixing")
    print("="*60)

    # Code with security vulnerabilities
    vulnerable_code = '''
import os
import subprocess
import sqlite3

def execute_command(user_input):
    # SQL Injection vulnerability
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    query = f"SELECT * FROM users WHERE name = '{user_input}'"
    cursor.execute(query)
    return cursor.fetchall()

def run_system_command(command):
    # Command injection vulnerability
    result = subprocess.run(f"ls {command}", shell=True, capture_output=True)
    return result.stdout

def save_file(filename, content):
    # Path traversal vulnerability
    with open(f"uploads/{filename}", 'w') as f:
        f.write(content)

def get_secret():
    # Hardcoded secret
    api_key = "sk-1234567890abcdef"
    return api_key
'''

    print("🔒 Code with Security Vulnerabilities:")
    print(vulnerable_code[:300] + "...")

    capabilities = AgentCapabilities(
        enable_automatic_bug_fixing=True,
        enable_automatic_evaluation=True,
        enable_advanced_code_generation=True,
        max_fix_iterations=5
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

    # Define security enhancement requirements
    security_requirements = {
        "type": "enhancement",
        "goals": [
            "fix_sql_injection",
            "fix_command_injection",
            "fix_path_traversal",
            "remove_hardcoded_secrets",
            "add_input_validation",
            "add_sanitization",
            "security_logging",
            "secure_coding_practices"
        ],
        "description": "Fix security vulnerabilities and implement secure coding practices"
    }

    print("\n🛡️  Running security vulnerability analysis and fixing...")

    results = await orchestrator.run_full_advanced_cycle(
        code_content=vulnerable_code,
        file_path="vulnerable_app.py",
        requirements=security_requirements
    )

    if results.get("overall_success"):
        print("✅ Security fixes completed!")
        print("🔒 Security improvements:")
        print("   - Fixed SQL injection with parameterized queries")
        print("   - Eliminated command injection vulnerabilities")
        print("   - Added path traversal protection")
        print("   - Removed hardcoded secrets")
        print("   - Added comprehensive input validation")
        print("   - Implemented security logging")

    return results


async def example_5_test_generation():
    """Example 5: Comprehensive Test Suite Generation"""
    print("\n" + "="*60)
    print("EXAMPLE 5: Comprehensive Test Suite Generation")
    print("="*60)

    # Production code that needs tests
    production_code = '''
from typing import List, Optional
import re
from datetime import datetime

class EmailValidator:
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')
    
    def validate_email(self, email: str) -> bool:
        """Validate email format."""
        if not email or not isinstance(email, str):
            return False
        return bool(self.email_pattern.match(email.strip()))
    
    def extract_domain(self, email: str) -> Optional[str]:
        """Extract domain from email address."""
        if not self.validate_email(email):
            return None
        return email.split('@')[1]
    
    def batch_validate(self, emails: List[str]) -> List[bool]:
        """Validate multiple emails."""
        return [self.validate_email(email) for email in emails]

class UserRegistration:
    def __init__(self, email_validator: EmailValidator):
        self.email_validator = email_validator
        self.registered_emails = set()
    
    def register_user(self, email: str, name: str) -> dict:
        """Register a new user."""
        if not self.email_validator.validate_email(email):
            raise ValueError("Invalid email format")
        
        if email in self.registered_emails:
            raise ValueError("Email already registered")
        
        if not name or len(name.strip()) < 2:
            raise ValueError("Name must be at least 2 characters")
        
        self.registered_emails.add(email)
        return {
            'email': email,
            'name': name.strip(),
            'registered_at': datetime.now().isoformat()
        }
'''

    print("🧪 Production Code Needing Tests:")
    print(production_code[:300] + "...")

    capabilities = AgentCapabilities(
        enable_advanced_code_generation=True,
        enable_comprehensive_testing=True,
        enable_automatic_evaluation=True
    )
    orchestrator = AdvancedAgentOrchestrator(capabilities=capabilities)

    # Define test generation requirements
    test_requirements = {
        "type": "tests",
        "target_file": "email_validator.py",
        "test_types": [
            "unit_tests",
            "integration_tests",
            "edge_case_tests",
            "error_handling_tests",
            "performance_tests"
        ],
        "coverage_target": 95,
        "test_framework": "pytest",
        "features": [
            "parametrized_tests",
            "fixtures",
            "mocking",
            "test_data_generation",
            "assertion_helpers",
            "test_documentation"
        ],
        "description": "Generate comprehensive test suite with high coverage"
    }

    print("\n🧪 Generating comprehensive test suite...")

    test_result = await orchestrator.advanced_code_implementation(
        requirements=test_requirements,
        file_path="test_email_validator.py",
        existing_code=production_code
    )

    if test_result["success"]:
        print("✅ Test suite generated successfully!")
        print("📋 Test coverage includes:")
        for test_type in test_requirements["test_types"]:
            print(f"   - {test_type}")
        print(f"🎯 Target coverage: {test_requirements['coverage_target']}%")

    return test_result


async def main():
    """Run all real-world examples"""
    print("🌍 Real-World Examples: Advanced AI Agent Framework")
    print("This demonstrates practical applications of the framework.")

    examples = [
        ("Legacy Code Modernization", example_1_legacy_code_modernization),
        ("API Endpoint Generation", example_2_api_endpoint_generation),
        ("Data Pipeline Enhancement", example_3_data_processing_pipeline),
        ("Security Vulnerability Fixing", example_4_security_vulnerability_fixing),
        ("Test Suite Generation", example_5_test_generation)
    ]

    results = {}

    for name, example_func in examples:
        try:
            print(f"\n🚀 Running: {name}")
            result = await example_func()
            results[name] = result
            print(f"✅ {name} completed successfully!")
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results[name] = {"error": str(e)}

    print("\n" + "="*60)
    print("📊 SUMMARY OF REAL-WORLD EXAMPLES")
    print("="*60)

    for name, result in results.items():
        status = "✅ Success" if not result.get("error") else "❌ Failed"
        print(f"{status}: {name}")

    print("\n🎯 These examples demonstrate:")
    print("✅ Legacy code modernization and Python 2→3 migration")
    print("✅ Automated API endpoint generation with best practices")
    print("✅ Data processing pipeline optimization and enhancement")
    print("✅ Security vulnerability detection and automated fixing")
    print("✅ Comprehensive test suite generation with high coverage")

    return results


if __name__ == "__main__":
    results = asyncio.run(main())
    print(f"\n🏁 Completed {len(results)} real-world examples!")
