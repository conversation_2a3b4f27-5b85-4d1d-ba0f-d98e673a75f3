# Plugin Development Guide

This guide provides step-by-step instructions for developing plugins for the enhanced plugin system, including best practices, patterns, and common use cases.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Plugin Structure](#basic-plugin-structure)
3. [Function Exposure](#function-exposure)
4. [Advanced Features](#advanced-features)
5. [Best Practices](#best-practices)
6. [Common Patterns](#common-patterns)
7. [Testing Your Plugin](#testing-your-plugin)
8. [Deployment](#deployment)

## Getting Started

### Prerequisites

- Python 3.8+
- Agent Framework installed
- Basic understanding of async/await patterns

### Development Environment Setup

1. Create a new directory for your plugin:

```bash
mkdir my_plugin
cd my_plugin
```

2. Create the plugin file:

```bash
touch my_plugin.py
```

3. Set up your development environment with the required dependencies:

```bash
pip install agent-framework watchdog pydantic
```

## Basic Plugin Structure

Every enhanced plugin should inherit from `EnhancedPlugin` and implement the required methods:

```python
from agent_framework.plugins import EnhancedPlugin
from agent_framework.core.types import PluginRequest, PluginResponse

class MyPlugin(EnhancedPlugin):
    """
    My custom plugin description.
    
    This plugin provides [describe functionality].
    """
    
    # Plugin metadata
    PLUGIN_NAME = "my_plugin"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "My custom plugin"
    PLUGIN_AUTHOR = "Your Name"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []  # List of required plugins
    
    def __init__(self):
        """Initialize the plugin."""
        super().__init__()
        # Initialize plugin-specific attributes
        self.my_data = {}
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config: dict) -> None:
        """
        Plugin-specific initialization.
        
        Args:
            config: Plugin configuration dictionary
        """
        # Initialize your plugin here
        self.my_data = config.get('my_data', {})
        
        # Set up any resources, connections, etc.
        self.logger.info(f"Initializing {self.name} plugin")
    
    async def _cleanup_plugin(self) -> None:
        """Plugin-specific cleanup."""
        # Clean up resources
        self.my_data.clear()
        
        self.logger.info(f"Cleaning up {self.name} plugin")
    
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """
        Execute traditional capability requests.
        
        This method handles non-exposed function requests.
        """
        if request.capability == "my_capability":
            return PluginResponse(
                success=True,
                result="Capability executed",
                metadata={"plugin_name": self.name}
            )
        
        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )
```

## Function Exposure

The enhanced plugin system allows you to expose functions directly to AI models using decorators:

### Basic Function Exposure

```python
from agent_framework.plugins.decorators import (
    exposed_function, string_param, int_param, returns
)
from agent_framework.plugins.function_registry import ParameterType

class MyPlugin(EnhancedPlugin):
    # ... (basic structure as above)
    
    @exposed_function(
        name="process_text",  # Optional: defaults to function name
        description="Process text with various operations",
        tags=["text", "processing", "utility"],
        category="text_processing"
    )
    @string_param("text", "The text to process", required=True)
    @string_param("operation", "Operation: upper, lower, reverse", required=True)
    @returns(ParameterType.STRING, "Processed text")
    async def process_text(self, text: str, operation: str) -> str:
        """Process text with the specified operation."""
        if operation == "upper":
            return text.upper()
        elif operation == "lower":
            return text.lower()
        elif operation == "reverse":
            return text[::-1]
        else:
            raise ValueError(f"Unknown operation: {operation}")
```

### Advanced Parameter Types

```python
from agent_framework.plugins.decorators import (
    exposed_function, parameter, array_param, object_param, bool_param, float_param
)

@exposed_function(
    description="Analyze data with multiple parameters",
    tags=["analysis", "data"]
)
@array_param("data", "Array of numbers to analyze", required=True)
@bool_param("include_stats", "Include statistical analysis", required=False, default=True)
@float_param("threshold", "Threshold for filtering", required=False, default=0.5)
@object_param("options", "Additional options", required=False, default={})
@returns(ParameterType.OBJECT, "Analysis results")
async def analyze_data(self, data: list, include_stats: bool = True, 
                      threshold: float = 0.5, options: dict = None) -> dict:
    """Analyze numerical data."""
    options = options or {}
    
    # Filter data based on threshold
    filtered_data = [x for x in data if isinstance(x, (int, float)) and x >= threshold]
    
    result = {
        "filtered_count": len(filtered_data),
        "original_count": len(data),
        "threshold": threshold
    }
    
    if include_stats and filtered_data:
        import statistics
        result["statistics"] = {
            "mean": statistics.mean(filtered_data),
            "median": statistics.median(filtered_data),
            "min": min(filtered_data),
            "max": max(filtered_data)
        }
    
    return result
```

### Function Examples and Documentation

```python
from agent_framework.plugins.decorators import example

@exposed_function(description="Calculate mathematical operations")
@float_param("a", "First number", required=True)
@float_param("b", "Second number", required=True)
@string_param("operation", "Operation: add, subtract, multiply, divide", required=True)
@returns(ParameterType.FLOAT, "Calculation result")
@example(
    description="Add two numbers",
    input={"a": 5.5, "b": 3.2, "operation": "add"},
    output=8.7
)
@example(
    description="Multiply two numbers",
    input={"a": 4.0, "b": 2.5, "operation": "multiply"},
    output=10.0
)
async def calculate(self, a: float, b: float, operation: str) -> float:
    """Perform mathematical calculations."""
    if operation == "add":
        return a + b
    elif operation == "subtract":
        return a - b
    elif operation == "multiply":
        return a * b
    elif operation == "divide":
        if b == 0:
            raise ValueError("Division by zero")
        return a / b
    else:
        raise ValueError(f"Unknown operation: {operation}")
```

## Advanced Features

### Input and Output Validation

```python
from agent_framework.plugins.decorators import validate_input, validate_output

@exposed_function(description="Process user data with validation")
@validate_input(lambda args: "email" in args and "@" in args["email"] or "Invalid email")
@validate_output(lambda result: len(result) > 0 or "Empty result not allowed")
async def process_user_data(self, email: str, name: str) -> str:
    """Process user data with validation."""
    return f"Processed user: {name} ({email})"
```

### Caching and Rate Limiting

```python
from agent_framework.plugins.decorators import cached, rate_limited

@exposed_function(description="Expensive computation with caching")
@cached(ttl=300)  # Cache for 5 minutes
@rate_limited(calls_per_minute=10)  # Limit to 10 calls per minute
async def expensive_computation(self, input_data: str) -> str:
    """Perform expensive computation."""
    import asyncio
    await asyncio.sleep(1)  # Simulate expensive operation
    return f"Computed result for: {input_data}"
```

### Hot Reload State Management

```python
import json
from pathlib import Path

class MyPlugin(EnhancedPlugin):
    def __init__(self):
        super().__init__()
        self.persistent_data = {}
        self.state_file = Path("my_plugin_state.json")
    
    async def _save_reload_state(self) -> None:
        """Save state before hot reload."""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.persistent_data, f)
            self.logger.info("Plugin state saved for reload")
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    async def _restore_reload_state(self) -> None:
        """Restore state after hot reload."""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r') as f:
                    self.persistent_data = json.load(f)
                self.logger.info("Plugin state restored after reload")
        except Exception as e:
            self.logger.error(f"Failed to restore state: {e}")
```

## Best Practices

### 1. Error Handling

```python
@exposed_function(description="Safe operation with error handling")
async def safe_operation(self, data: str) -> dict:
    """Perform operation with comprehensive error handling."""
    try:
        # Validate input
        if not data or not isinstance(data, str):
            raise ValueError("Invalid input data")
        
        # Perform operation
        result = self._process_data(data)
        
        return {
            "success": True,
            "result": result,
            "timestamp": time.time()
        }
        
    except ValueError as e:
        self.logger.warning(f"Validation error: {e}")
        return {
            "success": False,
            "error": f"Validation error: {e}",
            "error_type": "validation"
        }
    except Exception as e:
        self.logger.error(f"Unexpected error: {e}")
        return {
            "success": False,
            "error": f"Internal error: {e}",
            "error_type": "internal"
        }
```

### 2. Logging

```python
import logging

class MyPlugin(EnhancedPlugin):
    def __init__(self):
        super().__init__()
        # Plugin-specific logger
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @exposed_function(description="Operation with detailed logging")
    async def logged_operation(self, input_data: str) -> str:
        """Operation with comprehensive logging."""
        self.logger.info(f"Starting operation with input: {input_data[:50]}...")
        
        try:
            result = self._process_input(input_data)
            self.logger.info(f"Operation completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Operation failed: {e}", exc_info=True)
            raise
```

### 3. Configuration Management

```python
class MyPlugin(EnhancedPlugin):
    async def _initialize_plugin(self, config: dict) -> None:
        """Initialize with configuration validation."""
        # Set defaults
        self.max_items = config.get('max_items', 1000)
        self.timeout = config.get('timeout', 30)
        self.api_key = config.get('api_key')
        
        # Validate required configuration
        if not self.api_key:
            raise ValueError("API key is required in plugin configuration")
        
        # Validate configuration values
        if self.max_items <= 0:
            raise ValueError("max_items must be positive")
        
        if self.timeout <= 0:
            raise ValueError("timeout must be positive")
        
        self.logger.info(f"Plugin configured: max_items={self.max_items}, timeout={self.timeout}")
```

## Common Patterns

### 1. Data Processing Plugin Pattern

```python
class DataProcessorPlugin(EnhancedPlugin):
    """Pattern for data processing plugins."""
    
    def __init__(self):
        super().__init__()
        self.processors = {}
    
    async def _initialize_plugin(self, config: dict) -> None:
        # Register data processors
        self.processors = {
            'csv': self._process_csv,
            'json': self._process_json,
            'xml': self._process_xml
        }
    
    @exposed_function(description="Process data in various formats")
    @string_param("data", "Data to process", required=True)
    @string_param("format", "Data format: csv, json, xml", required=True)
    async def process_data(self, data: str, format: str) -> dict:
        """Process data based on format."""
        if format not in self.processors:
            raise ValueError(f"Unsupported format: {format}")
        
        processor = self.processors[format]
        return await processor(data)
    
    async def _process_csv(self, data: str) -> dict:
        # CSV processing logic
        pass
    
    async def _process_json(self, data: str) -> dict:
        # JSON processing logic
        pass
    
    async def _process_xml(self, data: str) -> dict:
        # XML processing logic
        pass
```

### 2. API Integration Plugin Pattern

```python
import aiohttp

class APIIntegrationPlugin(EnhancedPlugin):
    """Pattern for API integration plugins."""
    
    def __init__(self):
        super().__init__()
        self.session = None
        self.base_url = None
        self.auth_headers = {}
    
    async def _initialize_plugin(self, config: dict) -> None:
        self.base_url = config.get('base_url')
        api_key = config.get('api_key')
        
        if api_key:
            self.auth_headers['Authorization'] = f"Bearer {api_key}"
        
        # Create HTTP session
        self.session = aiohttp.ClientSession()
    
    async def _cleanup_plugin(self) -> None:
        if self.session:
            await self.session.close()
    
    @exposed_function(description="Make API request")
    @string_param("endpoint", "API endpoint", required=True)
    @object_param("params", "Query parameters", required=False, default={})
    async def api_request(self, endpoint: str, params: dict = None) -> dict:
        """Make API request to external service."""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        async with self.session.get(url, params=params, headers=self.auth_headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"API request failed: {response.status}")
```

## Testing Your Plugin

### Unit Testing

```python
import pytest
from your_plugin import MyPlugin

@pytest.mark.asyncio
async def test_plugin_initialization():
    """Test plugin initialization."""
    plugin = MyPlugin()
    config = {"test_setting": "value"}
    
    await plugin.initialize(config)
    assert plugin._is_initialized is True
    
    await plugin.cleanup()
    assert plugin._is_initialized is False

@pytest.mark.asyncio
async def test_exposed_function():
    """Test exposed function."""
    plugin = MyPlugin()
    await plugin.initialize({})
    
    result = await plugin.process_text("hello", "upper")
    assert result == "HELLO"
    
    await plugin.cleanup()
```

### Integration Testing

```python
from agent_framework.plugins import PluginManager, ModelIntegration, FunctionCallRequest
from agent_framework.core.config import FrameworkConfig

@pytest.mark.asyncio
async def test_plugin_integration():
    """Test plugin integration with framework."""
    config = FrameworkConfig()
    config.plugins.plugin_directories = ["path/to/your/plugin"]
    
    plugin_manager = PluginManager(config)
    await plugin_manager.initialize()
    
    try:
        # Test function discovery
        functions = plugin_manager.get_available_functions()
        assert "your_plugin.process_text" in functions
        
        # Test function calling
        model_integration = ModelIntegration(plugin_manager)
        request = FunctionCallRequest(
            function_name="your_plugin.process_text",
            arguments={"text": "test", "operation": "upper"}
        )
        
        result = await model_integration.call_function(request)
        assert result.success is True
        assert result.result == "TEST"
        
    finally:
        await plugin_manager.shutdown()
```

## Deployment

### 1. Plugin Structure

Organize your plugin files:

```
my_plugin/
├── my_plugin.py          # Main plugin file
├── requirements.txt      # Plugin dependencies
├── config.yaml          # Default configuration
├── README.md            # Plugin documentation
└── tests/               # Plugin tests
    ├── test_my_plugin.py
    └── __init__.py
```

### 2. Configuration File

Create a `config.yaml` for your plugin:

```yaml
# Plugin configuration
my_plugin:
  max_items: 1000
  timeout: 30
  api_key: "${API_KEY}"  # Environment variable
  features:
    - feature1
    - feature2
```

### 3. Installation

1. Copy your plugin to the plugins directory
2. Install dependencies: `pip install -r requirements.txt`
3. Update framework configuration to include your plugin directory
4. Restart the framework or use hot reload

### 4. Documentation

Document your plugin with:

- Clear function descriptions
- Parameter explanations
- Usage examples
- Configuration options
- Error handling information

This completes the plugin development guide. Follow these patterns and best practices to create robust, maintainable plugins for the enhanced plugin system.
