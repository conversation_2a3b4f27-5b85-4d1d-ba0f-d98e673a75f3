#!/usr/bin/env python3
"""
CLI Usage Examples for the Agent Framework

This script demonstrates various ways to use the agent framework CLI
for different programming tasks.
"""

from agent_framework.cli.core import AgentCLI
import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


async def example_code_analysis():
    """Example: Analyze code for quality and complexity."""
    print("=" * 60)
    print("Example 1: Code Analysis")
    print("=" * 60)

    # Sample code to analyze
    sample_code = '''
def complex_function(data, options=None, debug=False):
    """A complex function that needs analysis."""
    if options is None:
        options = {}
    
    results = []
    for item in data:
        if debug:
            print(f"Processing {item}")
        
        if item.get('type') == 'special':
            for sub_item in item.get('children', []):
                if sub_item.get('active', False):
                    processed = process_special_item(sub_item, options)
                    if processed:
                        results.append(processed)
        else:
            processed = process_regular_item(item, options)
            if processed:
                results.append(processed)
    
    return results

def process_special_item(item, options):
    # Complex processing logic
    return item

def process_regular_item(item, options):
    # Regular processing logic
    return item
    '''

    cli = AgentCLI()

    # Simulate CLI arguments for analysis
    args = [
        'analyze',
        '--code', sample_code,
        '--type', 'all',
        '--detailed',
        '--format', 'text'
    ]

    print("Running: agent-framework analyze --code '<sample_code>' --type all --detailed")
    print()

    try:
        result = await cli.run(args)
        print(f"Analysis completed with exit code: {result}")
    except Exception as e:
        print(f"Error: {e}")


async def example_code_generation():
    """Example: Generate code from specifications."""
    print("\n" + "=" * 60)
    print("Example 2: Code Generation")
    print("=" * 60)

    cli = AgentCLI()

    # Generate a function
    args = [
        'generate', 'function',
        '--name', 'calculate_average',
        '--description', 'Calculate the average of a list of numbers',
        '--parameters', 'numbers:List[float]', 'exclude_zeros:bool:False',
        '--return-type', 'float',
        '--docstring-style', 'google'
    ]

    print("Running: agent-framework generate function --name calculate_average ...")
    print()

    try:
        result = await cli.run(args)
        print(f"Generation completed with exit code: {result}")
    except Exception as e:
        print(f"Error: {e}")


async def example_code_optimization():
    """Example: Optimize code for performance."""
    print("\n" + "=" * 60)
    print("Example 3: Code Optimization")
    print("=" * 60)

    # Sample inefficient code
    inefficient_code = '''
def find_duplicates(items):
    duplicates = []
    for i in range(len(items)):
        for j in range(len(items)):
            if i != j and items[i] == items[j]:
                if items[i] not in duplicates:
                    duplicates.append(items[i])
    return duplicates

def process_data(data):
    result = ""
    for item in data:
        result = result + str(item) + ","
    return result[:-1]
    '''

    cli = AgentCLI()

    args = [
        'optimize',
        '--code', inefficient_code,
        '--type', 'performance',
        '--target', 'speed',
        '--show-diff'
    ]

    print("Running: agent-framework optimize --code '<inefficient_code>' --type performance")
    print()

    try:
        result = await cli.run(args)
        print(f"Optimization completed with exit code: {result}")
    except Exception as e:
        print(f"Error: {e}")


async def example_error_debugging():
    """Example: Debug errors and get suggestions."""
    print("\n" + "=" * 60)
    print("Example 4: Error Debugging")
    print("=" * 60)

    # Sample error traceback
    error_traceback = '''
Traceback (most recent call last):
  File "example.py", line 15, in <module>
    result = process_data(data)
  File "example.py", line 8, in process_data
    return data[index]
IndexError: list index out of range
    '''

    cli = AgentCLI()

    args = [
        'debug',
        '--traceback', error_traceback,
        '--suggest-fixes'
    ]

    print("Running: agent-framework debug --traceback '<error_traceback>' --suggest-fixes")
    print()

    try:
        result = await cli.run(args)
        print(f"Debug analysis completed with exit code: {result}")
    except Exception as e:
        print(f"Error: {e}")


async def example_documentation_generation():
    """Example: Generate documentation."""
    print("\n" + "=" * 60)
    print("Example 5: Documentation Generation")
    print("=" * 60)

    # Sample code without docstrings
    undocumented_code = '''
class UserManager:
    def __init__(self, database_url):
        self.db_url = database_url
        self.users = []
    
    def add_user(self, username, email, role="user"):
        user = {"username": username, "email": email, "role": role}
        self.users.append(user)
        return user
    
    def find_user(self, username):
        for user in self.users:
            if user["username"] == username:
                return user
        return None
    
    def update_user_role(self, username, new_role):
        user = self.find_user(username)
        if user:
            user["role"] = new_role
            return True
        return False
    '''

    cli = AgentCLI()

    args = [
        'document', 'docstrings',
        '--code', undocumented_code,
        '--style', 'google',
        '--include-examples'
    ]

    print("Running: agent-framework document docstrings --code '<undocumented_code>' --style google")
    print()

    try:
        result = await cli.run(args)
        print(f"Documentation generation completed with exit code: {result}")
    except Exception as e:
        print(f"Error: {e}")


async def example_interactive_mode():
    """Example: Interactive mode usage."""
    print("\n" + "=" * 60)
    print("Example 6: Interactive Mode")
    print("=" * 60)

    print("To start interactive mode, run:")
    print("  agent-framework interactive")
    print()
    print("In interactive mode, you can:")
    print("  • Ask natural language questions:")
    print("    > How can I optimize this function?")
    print("    > What's wrong with my code?")
    print("    > Generate a class for user authentication")
    print()
    print("  • Use framework commands:")
    print("    > analyze complexity def my_function(): pass")
    print("    > generate function --name hello --description 'Say hello'")
    print(
        "    > optimize performance for i in range(len(items)): print(items[i])")
    print()
    print("  • Get help and status:")
    print("    > help")
    print("    > status")
    print("    > history")


async def main():
    """Run all examples."""
    print("Agent Framework CLI Usage Examples")
    print("=" * 60)
    print()
    print("This script demonstrates various CLI capabilities.")
    print("Note: These examples require the framework to be properly configured.")
    print()

    try:
        await example_code_analysis()
        await example_code_generation()
        await example_code_optimization()
        await example_error_debugging()
        await example_documentation_generation()
        await example_interactive_mode()

        print("\n" + "=" * 60)
        print("All examples completed!")
        print("=" * 60)
        print()
        print("To try these examples yourself:")
        print("1. Set up your API key: export AGENT_API_KEY='your-key'")
        print("2. Run: python cli.py --help")
        print("3. Try: python cli.py interactive")

    except Exception as e:
        print(f"Example execution failed: {e}")
        print("\nThis is expected if the framework is not fully configured.")
        print("The examples show the intended CLI usage patterns.")


if __name__ == '__main__':
    asyncio.run(main())
